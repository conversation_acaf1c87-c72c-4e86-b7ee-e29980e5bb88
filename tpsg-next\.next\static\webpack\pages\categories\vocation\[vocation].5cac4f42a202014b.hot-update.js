"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/categories/vocation/[vocation]",{

/***/ "./components/shared/Featured.js":
/*!***************************************!*\
  !*** ./components/shared/Featured.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Featured; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _utils_image_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/image-utils */ \"./utils/image-utils.js\");\n/* harmony import */ var _atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./atoms/Buttons/BigCta */ \"./components/shared/atoms/Buttons/BigCta.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  padding: 0 var(--border-space);\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  column-gap: 24px;\\n  padding-bottom: 80px;\\n  width: 100%;\\n  min-height: 400px;\\n  background-color: \",\n        ';\\n  z-index: 100;\\n\\n  .fw-featured-author {\\n    font-family: \"Lora\", sans-serif;\\n    font-style: italic;\\n    letter-spacing: 1px;\\n    opacity: 0.4;\\n  }\\n\\n  .fw-featured-image {\\n    position: relative;\\n    width: 100%;\\n    aspect-ratio: 1/1;\\n    grid-column: 1/5;\\n\\n    img {\\n      object-fit: cover;\\n    }\\n  }\\n\\n  .text-content {\\n    position: relative;\\n    grid-column: 1/5;\\n    color: ',\n        \";\\n    background-color: \",\n        \";\\n  }\\n\\n  .fw-featured-type {\\n    font-family: Switzer, Arial, sans-serif;\\n    opacity: 0.48;\\n    margin-top: 48px;\\n    margin-bottom: 56px;\\n    font-size: 16px;\\n    font-weight: 400;\\n    text-transform: uppercase;\\n    letter-spacing: 0.02em;\\n  }\\n\\n  .fw-featured-title {\\n    margin-top: 8px;\\n    margin-bottom: 0;\\n    font-size: 30px;\\n    font-weight: 500;\\n    line-height: 95%;\\n  }\\n\\n  .fw-featured-lead {\\n    margin-top: 8px;\\n    font-size: 17px;\\n    margin-right: 32px;\\n  }\\n\\n  .fw-featured-buttons {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  @media \",\n        \" {\\n    flex-direction: row;\\n    min-height: 400px;\\n    padding-bottom: 96px;\\n\\n    .fw-featured-image {\\n      position: relative;\\n      grid-column: 1/3;\\n    }\\n\\n    .text-content {\\n      margin-top: 0;\\n      margin-left: 32px;\\n      grid-column: 3/5;\\n      display: flex;\\n      flex-direction: column;\\n      justify-content: space-between;\\n    }\\n\\n    .fw-featured-type {\\n      font-size: 20px;\\n      margin-top: 46px;\\n    }\\n\\n    .fw-featured-title {\\n      font-size: 46px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-lead {\\n      font-familly: Switzer, sans-serif;\\n      font-size: 20px;\\n      font-weight: 400;\\n      opacity: 0.72;\\n      //font-size: 18px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-buttons {\\n      display: flex;\\n      flex-direction: row;\\n      gap: 24px;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n// import { getPostRoute } from \"utils/posts.utils\";\n// import AnimatedTextButton from \"./Buttons/AnimatedTextButton\";\n\n\nfunction Featured(param) {\n    let { content } = param;\n    const { image, title, description, cta = null, cta2 = null, color, type: rawType, postRef, route, _formatted// From CornerStone\n     } = content;\n    // Fix for corrupted type field: if type contains author name instead of content type, use default\n    const validContentTypes = [\n        \"article\",\n        \"podcast\",\n        \"video\",\n        \"webinar\",\n        \"livre\",\n        \"formation\"\n    ];\n    const isValidType = validContentTypes.includes(rawType === null || rawType === void 0 ? void 0 : rawType.toLowerCase());\n    // If type is invalid (contains author name), use fallback logic\n    let type;\n    if (!isValidType && rawType) {\n        // Default to 'article' for featured content when type field contains invalid data\n        type = \"ARTICLE\";\n    } else if (rawType) {\n        // Convert to uppercase for display\n        type = rawType.toUpperCase();\n    } else {\n        // No type provided, default to article\n        type = \"ARTICLE\";\n    }\n    const link = (cta === null || cta === void 0 ? void 0 : cta.url) || ((route === null || route === void 0 ? void 0 : route.startsWith(\"/\")) ? route : \"/\" + route);\n    const link2 = (cta2 === null || cta2 === void 0 ? void 0 : cta2.url) || null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Section, {\n        backgroundColor: color === null || color === void 0 ? void 0 : color.background,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"fw-featured-image\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: (0,_utils_image_utils__WEBPACK_IMPORTED_MODULE_4__.withRealSrc)(image),\n                    fill: true,\n                    priority: true,\n                    alt: \"\",\n                    sizes: \"50vw\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                        className: \"fw-featured-type\",\n                        children: type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(PostAuthor, {\n                                post: postRef,\n                                content: content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                                className: \"fw-featured-title\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                className: \"fw-featured-lead\",\n                                children: description || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.lead) || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.body)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"fw-featured-buttons\",\n                                children: [\n                                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta === null || cta === void 0 ? void 0 : cta.name) || \"D\\xe9couvrir\",\n                                        link: link,\n                                        outline: (cta === null || cta === void 0 ? void 0 : cta.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    link2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta2 === null || cta2 === void 0 ? void 0 : cta2.name) || \"D\\xe9couvrir\",\n                                        link: link2,\n                                        outline: (cta2 === null || cta2 === void 0 ? void 0 : cta2.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_c = Featured;\nconst PostAuthor = (param)=>{\n    let { post, content } = param;\n    var _post_author;\n    // Logique de fallback simple - les données sont maintenant enrichies côté serveur\n    let authorName = null;\n    if (post === null || post === void 0 ? void 0 : (_post_author = post.author) === null || _post_author === void 0 ? void 0 : _post_author.fullName) {\n        // Données Strapi via postRef (featured personnalisés)\n        authorName = post.author.fullName;\n    } else if (post === null || post === void 0 ? void 0 : post.author) {\n        // Données Strapi via postRef mais author est une string\n        authorName = post.author;\n    } else if (content === null || content === void 0 ? void 0 : content.author) {\n        // Données Meilisearch (cornerStones) OU featured enrichi côté serveur\n        authorName = content.author;\n    }\n    if (!authorName) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"fw-featured-author\",\n        children: authorName\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PostAuthor;\nconst Section = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"Featured__Section\",\n    componentId: \"sc-b4d3bd09-0\"\n})(_templateObject(), (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", (p)=>p.color ? p.color : \"var(--c-soft-cream)\", (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", styles_device__WEBPACK_IMPORTED_MODULE_3__.device.tablet);\n_c2 = Section;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Featured\");\n$RefreshReg$(_c1, \"PostAuthor\");\n$RefreshReg$(_c2, \"Section\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/Featured.js\n"));

/***/ })

});