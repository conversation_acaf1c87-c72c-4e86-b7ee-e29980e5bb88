"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/categories/ministere/[ministry]",{

/***/ "./pages/categories/ministere/[ministry]/index.js":
/*!********************************************************!*\
  !*** ./pages/categories/ministere/[ministry]/index.js ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ PageMinistry; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var components_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/shared */ \"./components/shared/index.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var components_shared_Card_HorizontalReversePostCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/shared/Card/HorizontalReversePostCard */ \"./components/shared/Card/HorizontalReversePostCard.js\");\n/* harmony import */ var components_shared_Card_CornerStoneCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/shared/Card/CornerStoneCard */ \"./components/shared/Card/CornerStoneCard.js\");\n/* harmony import */ var components_shared_pagination_ssr_paginate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/shared/pagination/ssr-paginate */ \"./components/shared/pagination/ssr-paginate.js\");\n/* harmony import */ var components_shared_categories_CategoriesHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! components/shared/categories/CategoriesHeader */ \"./components/shared/categories/CategoriesHeader.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  margin-top: 48px;\\n  .posts-container {\\n    display: block;\\n  }\\n  @media \",\n        \" {\\n    margin-top: 96px;\\n    .posts-container {\\n      display: flex;\\n      flex-wrap: wrap;\\n      justify-content: space-between;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 100%;\\n  margin-bottom: 64px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n\\n  .list-container {\\n    padding: 0;\\n    width: 100%;\\n  }\\n\\n  .post-card-li {\\n    list-style: none;\\n    padding-right: 0;\\n  }\\n\\n  @media \",\n        \" {\\n    width: 66.7%;\\n    margin-bottom: 164px;\\n    .post-card-li {\\n      padding-right: 142px;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  width: 100%;\\n\\n  .cornerstone-container {\\n    /* position: sticky;\\n    top: 60px; */\\n  }\\n\\n  .podcast-platform {\\n    display: flex;\\n    flex-wrap: wrap;\\n    margin-top: 64px;\\n    width: 100%;\\n    gap: 32px;\\n  }\\n\\n  @media \",\n        \" {\\n    .podcast-platform {\\n      margin-top: 16px;\\n      gap: 16px;\\n    }\\n  }\\n  @media \",\n        \" {\\n    width: 33.3%;\\n  }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  .label-type {\\n    font-size: 20px;\\n    font-family: Stelvio, sans-serif;\\n    margin: 0 0 16px 0;\\n\\n    font-weight: 500;\\n    letter-spacing: 4%;\\n    line-height: 32px;\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  margin-top: 0;\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n\n\n\n\nconst postPerPage = 15; // Number items per page\nvar __N_SSG = true;\nfunction PageMinistry(param) {\n    let { ministry, fallback } = param;\n    var _fallback_posts, _fallback_posts1;\n    const nbHits = (fallback === null || fallback === void 0 ? void 0 : (_fallback_posts = fallback.posts) === null || _fallback_posts === void 0 ? void 0 : _fallback_posts.totalHits) || 0;\n    const posts = fallback === null || fallback === void 0 ? void 0 : (_fallback_posts1 = fallback.posts) === null || _fallback_posts1 === void 0 ? void 0 : _fallback_posts1.hits;\n    const cornerStonesFeatured = (fallback === null || fallback === void 0 ? void 0 : fallback.cornerStonesFeatured) || [];\n    if (!ministry) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(PageWrapper, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared_categories_CategoriesHeader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                category: ministry,\n                type: \"minist\\xe8re\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MainContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"section\", {\n                        children: cornerStonesFeatured[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared__WEBPACK_IMPORTED_MODULE_2__.Featured, {\n                            content: cornerStonesFeatured[0]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SectionPosts, {\n                        className: \"site-padding\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                className: \"label-type\",\n                                children: (posts === null || posts === void 0 ? void 0 : posts.length) > 0 ? \"Derni\\xe8res ressources\" : \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"posts-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(LeftContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"ul\", {\n                                                className: \"list-container\",\n                                                children: posts === null || posts === void 0 ? void 0 : posts.map((post, key)=>{\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"li\", {\n                                                        className: \"post-card-li\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared_Card_HorizontalReversePostCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            post: post,\n                                                            options: {\n                                                                showLead: true,\n                                                                showDate: true,\n                                                                showAuthor: true\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, \"post-\".concat(key), false, {\n                                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                                lineNumber: 39,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared_pagination_ssr_paginate__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                nbHits: nbHits,\n                                                baseUrl: \"/categories/ministere/\".concat(ministry.slug, \"/ressources?page=\"),\n                                                currentPage: 1,\n                                                options: {\n                                                    postPerPage: postPerPage\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                                lineNumber: 55,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(RightContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            className: \"cornerstone-container\",\n                                            children: [\n                                                cornerStonesFeatured[1] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared_Card_CornerStoneCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    post: cornerStonesFeatured[1],\n                                                    options: {\n                                                        showAuthor: true,\n                                                        showBlur: true,\n                                                        aspectRatio: 16 / 9\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this),\n                                                cornerStonesFeatured[2] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared_Card_CornerStoneCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    post: cornerStonesFeatured[2],\n                                                    options: {\n                                                        showAuthor: true,\n                                                        showBlur: true,\n                                                        aspectRatio: 16 / 9\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"section\", {\n                        children: cornerStonesFeatured[3] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared__WEBPACK_IMPORTED_MODULE_2__.Featured, {\n                            content: cornerStonesFeatured[3]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\ministere\\\\[ministry]\\\\index.js\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_c = PageMinistry;\nconst SectionPosts = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].section.withConfig({\n    displayName: \"[ministry]__SectionPosts\",\n    componentId: \"sc-5d815418-0\"\n})(_templateObject(), styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop);\n_c1 = SectionPosts;\nconst LeftContent = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].article.withConfig({\n    displayName: \"[ministry]__LeftContent\",\n    componentId: \"sc-5d815418-1\"\n})(_templateObject1(), styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop);\n_c2 = LeftContent;\nconst RightContent = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div.withConfig({\n    displayName: \"[ministry]__RightContent\",\n    componentId: \"sc-5d815418-2\"\n})(_templateObject2(), styles_device__WEBPACK_IMPORTED_MODULE_3__.device.tablet, styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop);\n_c3 = RightContent;\nconst PageWrapper = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div.withConfig({\n    displayName: \"[ministry]__PageWrapper\",\n    componentId: \"sc-5d815418-3\"\n})(_templateObject3());\n_c4 = PageWrapper;\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div.withConfig({\n    displayName: \"[ministry]__MainContent\",\n    componentId: \"sc-5d815418-4\"\n})(_templateObject4());\n_c5 = MainContent;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageMinistry\");\n$RefreshReg$(_c1, \"SectionPosts\");\n$RefreshReg$(_c2, \"LeftContent\");\n$RefreshReg$(_c3, \"RightContent\");\n$RefreshReg$(_c4, \"PageWrapper\");\n$RefreshReg$(_c5, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/categories/ministere/[ministry]/index.js\n"));

/***/ })

});