import styled from "styled-components";
import Image from "next/image";
import { device } from "styles/device";
// import { getPostRoute } from "utils/posts.utils";
// import AnimatedTextButton from "./Buttons/AnimatedTextButton";
import { withRealSrc } from "../../utils/image-utils";
import BigCta from "./atoms/Buttons/BigCta";

export default function Featured({ content }) {

  // Debug: Log the content to see what data we're receiving (can be removed in production)
  console.log("Featured component - content data:", content);
  console.log("Featured component - type field:", content?.type);

  const {
    image,
    title,
    description,
    cta = null,
    cta2 = null,
    color,
    type: rawType,
    postRef,
    route,      // From CornerStone
    _formatted  // From CornerStone
  } = content;

  // Fix for corrupted type field: if type contains author name instead of content type, use default
  const validContentTypes = ["article", "podcast", "video", "webinar", "livre", "formation"];
  const isValidType = validContentTypes.includes(rawType?.toLowerCase());

  // If type is invalid (contains author name), use fallback logic
  let type;
  if (!isValidType && rawType) {
    console.log("⚠️ Invalid type detected:", rawType, "- using fallback to ARTICLE");
    // Default to 'article' for featured content
    type = "ARTICLE";
  } else if (rawType) {
    // Convert to uppercase for display
    type = rawType.toUpperCase();
  } else {
    // No type provided, default to article
    type = "ARTICLE";
  }

  console.log("Featured component - final type for display:", type);


  const link = cta?.url || (route?.startsWith("/") ? route : "/" + route)
  const link2 = cta2?.url || null;

  return (
    <Section backgroundColor={color?.background}>
      <div className={"fw-featured-image"}>
        <Image
          src={withRealSrc(image)}
          fill
          priority={true}
          alt={""}
          sizes={"50vw"}
        />
      </div>
      <div className={"text-content"}>
        <p className={"fw-featured-type"}>{type}</p>
        <div>
          <PostAuthor post={postRef} content={content}/>
          <h3 className={"fw-featured-title"}>{title}</h3>
          <p className={"fw-featured-lead"}>{description || _formatted?.lead || _formatted?.body}</p>
          <div className={"fw-featured-buttons"}>
            {link && (
              <BigCta text={cta?.name || "Découvrir"} link={link} outline={cta?.outline || false} theme={"light"}/>
            )}
            {link2 && (
              <BigCta text={cta2?.name || "Découvrir"} link={link2} outline={cta2?.outline || false} theme={"light"}/>
            )}
          </div>
        </div>
      </div>
    </Section>
  );
}

const PostAuthor = ({ post, content }) => {
  // Logique de fallback simple - les données sont maintenant enrichies côté serveur
  let authorName = null;

  if (post?.author?.fullName) {
    // Données Strapi via postRef (featured personnalisés)
    authorName = post.author.fullName;
  } else if (post?.author) {
    // Données Strapi via postRef mais author est une string
    authorName = post.author;
  } else if (content?.author) {
    // Données Meilisearch (cornerStones) OU featured enrichi côté serveur
    authorName = content.author;
  }

  if (!authorName) return null;

  return (
    <div className={"fw-featured-author"}>
      {authorName}
    </div>
  );
};

const Section = styled.div`
  position: relative;
  padding: 0 var(--border-space);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 24px;
  padding-bottom: 80px;
  width: 100%;
  min-height: 400px;
  background-color: ${(p) => ( p.backgroundColor ? p.backgroundColor : "var(--c-dark-green)" )};
  z-index: 100;

  .fw-featured-author {
    font-family: "Lora", sans-serif;
    font-style: italic;
    letter-spacing: 1px;
    opacity: 0.4;
  }

  .fw-featured-image {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1;
    grid-column: 1/5;

    img {
      object-fit: cover;
    }
  }

  .text-content {
    position: relative;
    grid-column: 1/5;
    color: ${(p) => ( p.color ? p.color : "var(--c-soft-cream)" )};
    background-color: ${(p) => p.backgroundColor ? p.backgroundColor : "var(--c-dark-green)"};
  }

  .fw-featured-type {
    font-family: Switzer, Arial, sans-serif;
    opacity: 0.48;
    margin-top: 48px;
    margin-bottom: 56px;
    font-size: 16px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.02em;
  }

  .fw-featured-title {
    margin-top: 8px;
    margin-bottom: 0;
    font-size: 30px;
    font-weight: 500;
    line-height: 95%;
  }

  .fw-featured-lead {
    margin-top: 8px;
    font-size: 17px;
    margin-right: 32px;
  }

  .fw-featured-buttons {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  @media ${device.tablet} {
    flex-direction: row;
    min-height: 400px;
    padding-bottom: 96px;

    .fw-featured-image {
      position: relative;
      grid-column: 1/3;
    }

    .text-content {
      margin-top: 0;
      margin-left: 32px;
      grid-column: 3/5;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .fw-featured-type {
      font-size: 20px;
      margin-top: 46px;
    }

    .fw-featured-title {
      font-size: 46px;
      margin-top: 24px;
    }

    .fw-featured-lead {
      font-familly: Switzer, sans-serif;
      font-size: 20px;
      font-weight: 400;
      opacity: 0.72;
      //font-size: 18px;
      margin-top: 24px;
    }

    .fw-featured-buttons {
      display: flex;
      flex-direction: row;
      gap: 24px;
    }
  }
`;
