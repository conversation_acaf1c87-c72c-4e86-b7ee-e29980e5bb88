import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import styled from "styled-components";
import { device } from "styles/device";
import { modulesAsObj } from "utils/components.utils";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import SectionMinistries from "components/shared/categories/SectionMinistries";
import { Featured } from "components/shared";

import CornerStoneCard from "components/shared/Card/CornerStoneCard";
import { FilterTopicsString } from "utils/filterSearchString";
import { topicsPostsfetcher } from "utils/fetcher";
import CategoriesHeader from "components/shared/categories/CategoriesHeader";
import { MeiliApi } from "api/meili-client";

const postPerPage = 15; // Number items per page

export default function PageVocation({ vocation, fallback }) {

  const nbHits = fallback?.posts?.totalHits || 0;
  const posts = fallback?.posts?.hits;
  const cornerStonesFeatured = fallback?.cornerStonesFeatured || []

  if (!vocation) return null;

  return (
    <PageWrapper>
      <CategoriesHeader category={vocation} type={"vocation"}/>
      <MainContent>

        { vocation?.children?.length > 0 &&
            <section className={"section-ministries"}>
              <SectionMinistries ministries={vocation.children}/>
            </section>
        }

        <section>
          { cornerStonesFeatured[0] && (
            <Featured content={cornerStonesFeatured[0]} />
          )}
        </section>

        <SectionPosts className={"site-padding"}>
          <p className="label-type">{posts?.length > 0 ? "Dernières ressources" : ""}</p>
          <div className="posts-container ">
            <LeftContent>
              <ul className={"list-container"}>
                {posts?.map((post, key) => {
                  let modules = modulesAsObj(post.modules);
                  !post.lead ? (post.lead = modules?.lead) : "";
                  return (
                    <li key={`post-${key}`} className={"post-card-li"}>
                      <HorizontalReversePostCard
                        post={post}
                        options={{
                          showLead: true,
                          showDate: true,
                          showAuthor: true
                        }}
                      />
                    </li>
                  );
                })}
              </ul>
              <SSRPaginate
                nbHits={nbHits}
                baseUrl={`/categories/vocation/${vocation.slug}/ressources?page=`}
                currentPage={1}
                options={{
                  postPerPage: postPerPage,
                }}

              />
            </LeftContent>
            <RightContent>
              <div className="cornerstone-container">
                {cornerStonesFeatured[1] &&
                  <CornerStoneCard
                    post={cornerStonesFeatured[1]}
                    options={{
                      showAuthor: true,
                      showBlur: true,
                      aspectRatio: 16 / 9
                    }}
                  />
                }
                {cornerStonesFeatured[2] &&
                  <CornerStoneCard
                    post={cornerStonesFeatured[2]}
                    options={{
                      showAuthor: true,
                      showBlur: true,
                      aspectRatio: 16 / 9
                    }}
                  />
                }
              </div>
            </RightContent>
          </div>
        </SectionPosts>
        <section>
          {cornerStonesFeatured[3] &&
            <Featured content={cornerStonesFeatured[3]} />
          }
        </section>
      </MainContent>
    </PageWrapper>
  );
}

export async function getStaticProps({ params }) {
  const vocation = await client
    .query({
      query: QUERY_VOCATION,
      variables: { slug: params.vocation },
    })
    .then((response) => {
      return response.data.topicGroups[0];
    });

  if (!vocation) {
    return {
      notFound: true,
    };
  }

  // Put all topic into an array
  let topics = [];
  vocation?.topics && topics.push(...vocation.topics);

  vocation.children.forEach((ministry) => {
    ministry.topics.forEach((topic) => {
      topics.push(topic);
    });
  });

  // Clear duplicate topics
  topics = topics.filter(
    (topic, index, self) =>
      self.findIndex((topic2) => topic2.id === topic.id) === index
  );

  //fetch posts
  const filterTopicsString = FilterTopicsString(topics);

  let posts = []

  let cornerStonesFeatured = [null, null, null, null]

  if (filterTopicsString.length > 0) {
    posts = await topicsPostsfetcher({}, filterTopicsString, postPerPage);
    //fetch corner stone for compleate featured data
    const filterTopicsAnd = filterTopicsString.length > 0 ? " AND " : "";
    const filterTopicsStringCornerStone =
      `cs=true ${filterTopicsAnd} (${filterTopicsString})`;
    const cornerStones = await MeiliApi.searchHighlight("", {
      filter: filterTopicsStringCornerStone,
      sort: ["date:desc"],
      limit: 4
    });

    const fullFeatured = vocation?.featured?.filter((f) => !f.inColumn);
    const columnFeatured = vocation?.featured?.filter((f) => f.inColumn);

    // Fonction pour enrichir un featured avec l'auteur si nécessaire
    const enrichFeaturedWithAuthor = async (featured) => {
      if (!featured) return null;

      // Créer une copie pour éviter les mutations
      const enrichedFeatured = { ...featured };

      // Si l'auteur existe déjà, on le garde
      if (featured.postRef?.author?.fullName) {
        enrichedFeatured.author = featured.postRef.author.fullName;
        return enrichedFeatured;
      }

      // Si pas d'auteur mais qu'il y a une URL, on essaie de récupérer l'auteur
      if (featured.cta?.url && !featured.author) {
        try {
          const urlParts = featured.cta.url.split("/");
          const slug = urlParts[urlParts.length - 1];

          if (slug) {
            const { data } = await client.query({
              query: GET_POST_AUTHOR_BY_SLUG,
              variables: { slug },
            });

            if (data?.posts?.length > 0) {
              enrichedFeatured.author = data.posts[0].author?.fullName || null;
            }
          }
        } catch (error) {
          console.error("Erreur lors de la récupération de l'auteur pour:", featured.title, error);
          // En cas d'erreur, on retourne le featured sans auteur plutôt que de faire planter
        }
      }

      return enrichedFeatured;
    };

    // Enrichir les featured avec les auteurs (avec gestion d'erreur globale)
    let enrichedFullFeatured = fullFeatured || [];
    let enrichedColumnFeatured = columnFeatured || [];

    try {
      enrichedFullFeatured = await Promise.all(
        (fullFeatured || []).map(enrichFeaturedWithAuthor)
      );
      enrichedColumnFeatured = await Promise.all(
        (columnFeatured || []).map(enrichFeaturedWithAuthor)
      );
    } catch (error) {
      console.error("Erreur lors de l'enrichissement des featured:", error);
      // En cas d'erreur globale, on utilise les featured originaux
    }

    // Remplace les CornerStones par les Featured si ils existent.
    cornerStonesFeatured[0] = enrichedFullFeatured[0] ? enrichedFullFeatured[0] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;
    cornerStonesFeatured[1] = enrichedColumnFeatured[0] ? enrichedColumnFeatured[0] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;
    cornerStonesFeatured[2] = enrichedColumnFeatured[1] ? enrichedColumnFeatured[1] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;
    cornerStonesFeatured[3] = enrichedFullFeatured[1] ? enrichedFullFeatured[1] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;

    // Supprime les posts qui ont les mêmes routes que les cornerstones et featured
    posts.hits = posts?.hits?.filter(post => !cornerStonesFeatured?.find(csfeatured => csfeatured?.route === post?.route || csfeatured?.cta?.url === post?.route));
  }

  return {
    props: {
      vocation,
      fallback: {
        posts,
        cornerStonesFeatured
      },
    },
    revalidate: 10,
  };
}

export async function getStaticPaths() {
  const vocations = await client
    .query({
      query: QUERY_SLUGS,
    })
    .then((response) => {
      return response.data.topicGroups;
    });

  return {
    paths: vocations.map((vocation) => ({
      params: {
        vocation: vocation.slug,
      },
    })),
    fallback: true,
  };
}

const SectionPosts = styled.section`
  margin-top: 48px;
  .posts-container {
    display: block;
  }
  @media ${device.desktop} {
    margin-top: 96px;
    .posts-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`;
const LeftContent = styled.article`
  width: 100%;
  margin-bottom: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
  .post-card-li {
    list-style: none;
    padding-right: 0;
  }

  @media ${device.desktop} {
    margin-bottom: 164px;
    width: 66.7%;
    .post-card-li {
      padding-right: 142px;
    }
  }
`;
const RightContent = styled.div`
  position: relative;
  width: 100%;
  .cornerstone-container {
    /* position: sticky;
    top: 60px; */
  }
  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }
  @media ${device.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${device.desktop} {
    width: 33.3%;
  }
`;

const PageWrapper = styled.div`
  .label-type {
    font-size: 16px;
    font-family: Stelvio, sans-serif;
    margin: 0 0 16px 0;

    font-weight: 500;
    letter-spacing: 4%;
    line-height: 32px;
  }
`;
const MainContent = styled.main`
  //margin-top: 0;
  .section-ministries {
    margin-top: 64px;
    padding: 0 var(--border-space);
    margin-bottom: 80px;
  }
`;


const GET_POST_AUTHOR_BY_SLUG = gql`
  query GetPostAuthorBySlug($slug: String!) {
    posts(where: { slug: $slug }) {
      author {
        fullName
      }
    }
  }
`;

const QUERY_SLUGS = gql`
  query Vocations {
    topicGroups {
      slug
    }
  }
`;
const QUERY_VOCATION = gql`
  query Vocations($slug: String!) {
    topicGroups(where: { slug: $slug }) {
      name
      description
      slug
      cover {
        formats
      }
      topics {
        id
        name
        postCount
      }
      children {
        id
        name
        slug
        type
        topics {
          id
          name
          postCount
        }
      }
      featured {
        title
        description
        inColumn
        image {
          url
          height
          width
          alternativeText
          provider
        }
        cta {
          name
          url
        }
        color {
          foreground
          background
        }
        type
        postRef {
          author {
            fullName
          }
        }
      }
    }
  }
`;