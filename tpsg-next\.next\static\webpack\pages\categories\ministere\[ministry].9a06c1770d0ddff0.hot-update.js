"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/categories/ministere/[ministry]",{

/***/ "./components/shared/Featured.js":
/*!***************************************!*\
  !*** ./components/shared/Featured.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Featured; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _utils_image_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/image-utils */ \"./utils/image-utils.js\");\n/* harmony import */ var _atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./atoms/Buttons/BigCta */ \"./components/shared/atoms/Buttons/BigCta.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  padding: 0 var(--border-space);\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  column-gap: 24px;\\n  padding-bottom: 80px;\\n  width: 100%;\\n  min-height: 400px;\\n  background-color: \",\n        ';\\n  z-index: 100;\\n\\n  .fw-featured-author {\\n    font-family: \"Lora\", sans-serif;\\n    font-style: italic;\\n    letter-spacing: 1px;\\n    opacity: 0.4;\\n  }\\n\\n  .fw-featured-image {\\n    position: relative;\\n    width: 100%;\\n    aspect-ratio: 1/1;\\n    grid-column: 1/5;\\n\\n    img {\\n      object-fit: cover;\\n    }\\n  }\\n\\n  .text-content {\\n    position: relative;\\n    grid-column: 1/5;\\n    color: ',\n        \";\\n    background-color: \",\n        \";\\n  }\\n\\n  .fw-featured-type {\\n    font-family: Switzer, Arial, sans-serif;\\n    opacity: 0.48;\\n    margin-top: 48px;\\n    margin-bottom: 56px;\\n    font-size: 16px;\\n    font-weight: 400;\\n    text-transform: uppercase;\\n    letter-spacing: 0.02em;\\n  }\\n\\n  .fw-featured-title {\\n    margin-top: 8px;\\n    margin-bottom: 0;\\n    font-size: 30px;\\n    font-weight: 500;\\n    line-height: 95%;\\n  }\\n\\n  .fw-featured-lead {\\n    margin-top: 8px;\\n    font-size: 17px;\\n    margin-right: 32px;\\n  }\\n\\n  .fw-featured-buttons {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  @media \",\n        \" {\\n    flex-direction: row;\\n    min-height: 400px;\\n    padding-bottom: 96px;\\n\\n    .fw-featured-image {\\n      position: relative;\\n      grid-column: 1/3;\\n    }\\n\\n    .text-content {\\n      margin-top: 0;\\n      margin-left: 32px;\\n      grid-column: 3/5;\\n      display: flex;\\n      flex-direction: column;\\n      justify-content: space-between;\\n    }\\n\\n    .fw-featured-type {\\n      font-size: 20px;\\n      margin-top: 46px;\\n    }\\n\\n    .fw-featured-title {\\n      font-size: 46px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-lead {\\n      font-familly: Switzer, sans-serif;\\n      font-size: 20px;\\n      font-weight: 400;\\n      opacity: 0.72;\\n      //font-size: 18px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-buttons {\\n      display: flex;\\n      flex-direction: row;\\n      gap: 24px;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n// import { getPostRoute } from \"utils/posts.utils\";\n// import AnimatedTextButton from \"./Buttons/AnimatedTextButton\";\n\n\nfunction Featured(param) {\n    let { content } = param;\n    const { image, title, description, cta = null, cta2 = null, color, type, postRef, route, _formatted// From CornerStone\n     } = content;\n    const link = (cta === null || cta === void 0 ? void 0 : cta.url) || \"/\" + route;\n    const link2 = (cta2 === null || cta2 === void 0 ? void 0 : cta2.url) || null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Section, {\n        backgroundColor: color === null || color === void 0 ? void 0 : color.background,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"fw-featured-image\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: (0,_utils_image_utils__WEBPACK_IMPORTED_MODULE_4__.withRealSrc)(image),\n                    fill: true,\n                    priority: true,\n                    alt: \"\",\n                    sizes: \"50vw\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                        className: \"fw-featured-type\",\n                        children: type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(PostAuthor, {\n                                post: postRef,\n                                content: content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                                className: \"fw-featured-title\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                className: \"fw-featured-lead\",\n                                children: description || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.lead) || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.body)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"fw-featured-buttons\",\n                                children: [\n                                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta === null || cta === void 0 ? void 0 : cta.name) || \"D\\xe9couvrir\",\n                                        link: link,\n                                        outline: (cta === null || cta === void 0 ? void 0 : cta.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    link2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta2 === null || cta2 === void 0 ? void 0 : cta2.name) || \"D\\xe9couvrir\",\n                                        link: link2,\n                                        outline: (cta2 === null || cta2 === void 0 ? void 0 : cta2.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_c = Featured;\nconst PostAuthor = (param)=>{\n    let { post, content } = param;\n    var _post_author;\n    // Logique de fallback simple - les données sont maintenant enrichies côté serveur\n    let authorName = null;\n    if (post === null || post === void 0 ? void 0 : (_post_author = post.author) === null || _post_author === void 0 ? void 0 : _post_author.fullName) {\n        // Données Strapi via postRef (featured personnalisés)\n        authorName = post.author.fullName;\n    } else if (post === null || post === void 0 ? void 0 : post.author) {\n        // Données Strapi via postRef mais author est une string\n        authorName = post.author;\n    } else if (content === null || content === void 0 ? void 0 : content.author) {\n        // Données Meilisearch (cornerStones) OU featured enrichi côté serveur\n        authorName = content.author;\n    }\n    if (!authorName) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"fw-featured-author\",\n        children: authorName\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PostAuthor;\nconst Section = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"Featured__Section\",\n    componentId: \"sc-71976cbb-0\"\n})(_templateObject(), (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", (p)=>p.color ? p.color : \"var(--c-soft-cream)\", (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", styles_device__WEBPACK_IMPORTED_MODULE_3__.device.tablet);\n_c2 = Section;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Featured\");\n$RefreshReg$(_c1, \"PostAuthor\");\n$RefreshReg$(_c2, \"Section\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/Featured.js\n"));

/***/ })

});