"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/categories/vocation/[vocation]",{

/***/ "./components/shared/Featured.js":
/*!***************************************!*\
  !*** ./components/shared/Featured.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Featured; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _utils_image_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/image-utils */ \"./utils/image-utils.js\");\n/* harmony import */ var _atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./atoms/Buttons/BigCta */ \"./components/shared/atoms/Buttons/BigCta.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  padding: 0 var(--border-space);\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  column-gap: 24px;\\n  padding-bottom: 80px;\\n  width: 100%;\\n  min-height: 400px;\\n  background-color: \",\n        ';\\n  z-index: 100;\\n\\n  .fw-featured-author {\\n    font-family: \"Lora\", sans-serif;\\n    font-style: italic;\\n    letter-spacing: 1px;\\n    opacity: 0.4;\\n  }\\n\\n  .fw-featured-image {\\n    position: relative;\\n    width: 100%;\\n    aspect-ratio: 1/1;\\n    grid-column: 1/5;\\n\\n    img {\\n      object-fit: cover;\\n    }\\n  }\\n\\n  .text-content {\\n    position: relative;\\n    grid-column: 1/5;\\n    color: ',\n        \";\\n    background-color: \",\n        \";\\n  }\\n\\n  .fw-featured-type {\\n    font-family: Switzer, Arial, sans-serif;\\n    opacity: 0.48;\\n    margin-top: 48px;\\n    margin-bottom: 56px;\\n    font-size: 16px;\\n    font-weight: 400;\\n    text-transform: uppercase;\\n    letter-spacing: 0.02em;\\n  }\\n\\n  .fw-featured-title {\\n    margin-top: 8px;\\n    margin-bottom: 0;\\n    font-size: 30px;\\n    font-weight: 500;\\n    line-height: 95%;\\n  }\\n\\n  .fw-featured-lead {\\n    margin-top: 8px;\\n    font-size: 17px;\\n    margin-right: 32px;\\n  }\\n\\n  .fw-featured-buttons {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  @media \",\n        \" {\\n    flex-direction: row;\\n    min-height: 400px;\\n    padding-bottom: 96px;\\n\\n    .fw-featured-image {\\n      position: relative;\\n      grid-column: 1/3;\\n    }\\n\\n    .text-content {\\n      margin-top: 0;\\n      margin-left: 32px;\\n      grid-column: 3/5;\\n      display: flex;\\n      flex-direction: column;\\n      justify-content: space-between;\\n    }\\n\\n    .fw-featured-type {\\n      font-size: 20px;\\n      margin-top: 46px;\\n    }\\n\\n    .fw-featured-title {\\n      font-size: 46px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-lead {\\n      font-familly: Switzer, sans-serif;\\n      font-size: 20px;\\n      font-weight: 400;\\n      opacity: 0.72;\\n      //font-size: 18px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-buttons {\\n      display: flex;\\n      flex-direction: row;\\n      gap: 24px;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n// import { getPostRoute } from \"utils/posts.utils\";\n// import AnimatedTextButton from \"./Buttons/AnimatedTextButton\";\n\n\nfunction Featured(param) {\n    let { content } = param;\n    const { image, title, description, cta = null, cta2 = null, color, type: rawType, postRef, route, _formatted// From CornerStone\n     } = content;\n    // Fix for corrupted type field: if type contains author name instead of content type, use default\n    const validContentTypes = [\n        \"article\",\n        \"podcast\",\n        \"video\",\n        \"webinar\",\n        \"livre\",\n        \"formation\"\n    ];\n    const isValidType = validContentTypes.includes(rawType === null || rawType === void 0 ? void 0 : rawType.toLowerCase());\n    // If type is invalid (contains author name), use fallback logic\n    let type;\n    if (!isValidType && rawType) {\n        console.log(\"⚠️ Invalid type detected:\", rawType, \"- using fallback to ARTICLE\");\n        // Default to 'article' for featured content\n        type = \"ARTICLE\";\n    } else if (rawType) {\n        // Convert to uppercase for display\n        type = rawType.toUpperCase();\n    } else {\n        // No type provided, default to article\n        type = \"ARTICLE\";\n    }\n    console.log(\"Featured component - final type for display:\", type);\n    const link = (cta === null || cta === void 0 ? void 0 : cta.url) || ((route === null || route === void 0 ? void 0 : route.startsWith(\"/\")) ? route : \"/\" + route);\n    const link2 = (cta2 === null || cta2 === void 0 ? void 0 : cta2.url) || null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Section, {\n        backgroundColor: color === null || color === void 0 ? void 0 : color.background,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"fw-featured-image\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: (0,_utils_image_utils__WEBPACK_IMPORTED_MODULE_4__.withRealSrc)(image),\n                    fill: true,\n                    priority: true,\n                    alt: \"\",\n                    sizes: \"50vw\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                        className: \"fw-featured-type\",\n                        children: type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(PostAuthor, {\n                                post: postRef,\n                                content: content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                                className: \"fw-featured-title\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                className: \"fw-featured-lead\",\n                                children: description || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.lead) || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.body)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"fw-featured-buttons\",\n                                children: [\n                                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta === null || cta === void 0 ? void 0 : cta.name) || \"D\\xe9couvrir\",\n                                        link: link,\n                                        outline: (cta === null || cta === void 0 ? void 0 : cta.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    link2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta2 === null || cta2 === void 0 ? void 0 : cta2.name) || \"D\\xe9couvrir\",\n                                        link: link2,\n                                        outline: (cta2 === null || cta2 === void 0 ? void 0 : cta2.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_c = Featured;\nconst PostAuthor = (param)=>{\n    let { post, content } = param;\n    var _post_author;\n    // Logique de fallback simple - les données sont maintenant enrichies côté serveur\n    let authorName = null;\n    if (post === null || post === void 0 ? void 0 : (_post_author = post.author) === null || _post_author === void 0 ? void 0 : _post_author.fullName) {\n        // Données Strapi via postRef (featured personnalisés)\n        authorName = post.author.fullName;\n    } else if (post === null || post === void 0 ? void 0 : post.author) {\n        // Données Strapi via postRef mais author est une string\n        authorName = post.author;\n    } else if (content === null || content === void 0 ? void 0 : content.author) {\n        // Données Meilisearch (cornerStones) OU featured enrichi côté serveur\n        authorName = content.author;\n    }\n    if (!authorName) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"fw-featured-author\",\n        children: authorName\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PostAuthor;\nconst Section = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"Featured__Section\",\n    componentId: \"sc-ac7a0bac-0\"\n})(_templateObject(), (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", (p)=>p.color ? p.color : \"var(--c-soft-cream)\", (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", styles_device__WEBPACK_IMPORTED_MODULE_3__.device.tablet);\n_c2 = Section;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Featured\");\n$RefreshReg$(_c1, \"PostAuthor\");\n$RefreshReg$(_c2, \"Section\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/Featured.js\n"));

/***/ })

});