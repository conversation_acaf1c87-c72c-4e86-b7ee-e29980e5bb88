import Link from "next/link";
import styled from "styled-components";
import { device } from "styles/device";
import { PostCardDetails, PostCardTitle } from "styles/styled-typography";
import { getPostRoute } from "utils/posts.utils";
import CondImage from "../condimage";
import CondLink from "../CondLink";


/**
 *
 * @param {object}  post
 * @param {object}  options
 * @param {boolean} options.author
 * @return {JSX.Element}
 * @constructor
 */
export default function CornerStoneCard({ post, options }) {
  const {
    showAuthor,
    showBlur,
    aspectRatio = 1
  } = options;

  let link = post?.cta?.url || post?.link || "/" + post?.route;

  if (!link) return <></>

  return (
    <CornerStonesContainer>
      <CondLink link={link}>
        <CornerStone>
          <CornerStoneImage aspectRatio={aspectRatio}>
            {showBlur && (
              <BlurImage aspectRatio={aspectRatio}>
                <CondImage imageData={post.image} />
              </BlurImage>
            )}
            <CondImage imageData={post.image} />
          </CornerStoneImage>
          <CornerStoneContent>
            <h2 className="corner-stone-title">
              {post?.title}
            </h2>
            {showAuthor && (
              <>
                {/* DEBUG: Log pour CornerStoneCard */}
                {console.log('🔍 CORNERSTONE CARD:', {
                  title: post.title,
                  showAuthor,
                  author: post.author,
                  authorType: typeof post.author,
                  willShow: showAuthor && post.author
                })}
                {post.author && (
                  <PostCardDetails>
                    {post.author?.fullName ? post.author.fullName : post.author}
                  </PostCardDetails>
                )}
              </>
            )}
          </CornerStoneContent>
        </CornerStone>
      </CondLink>
    </CornerStonesContainer>
  );
}
const BlurImage = styled.div`
  position: absolute;
  width: 100%;
  filter: blur(12px);
  aspect-ratio: ${(props) => (props.aspectRatio)};
`;
const CornerStonesContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);
  a {
    width: 100%;
    display: flex;
    justify-content: center;
  }
`;
const CornerStoneContent = styled.div`
  width: 70%;
  padding-left: 20px;
  @media ${device.mini} {
    width: 100%;
    padding-left: 0;
  }
  @media ${device.desktop} {
    padding-left: 0;
    width: 100%;
  }
`;
const CornerStoneImage = styled.div`
  position: relative;
  width: 30%;
  margin-bottom: 24px;
  aspect-ratio: ${(props) => (props.aspectRatio)};


  @media ${device.desktop} {
    width: 100%;
  }
  @media ${device.mini} {
    width: 80%;
  }
`;
const CornerStone = styled.div`
  color: white;
  background-color: #0F0F0F;
  padding: 20px 20px;

  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .corner-stone-title {
    padding-top: 8px;
    font-weight: 400;
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 1.25rem;
    font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media ${device.desktop} {
    .corner-stone-title {
      -webkit-line-clamp:  2!important;
    }
  }
  @media ${device.mini} {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
`;