{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "70339", "content-type": "application/json", "date": "<PERSON><PERSON>, 27 May 2025 10:39:06 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "11998ms"}, "body": "eyJkYXRhIjp7InBvc3RzIjpbeyJpZCI6IjQxMjAiLCJ0aXRsZSI6IkNvbW11bmlxdcOpIFRQU0c6IGdyYW5kZSDDqXRhcGUhIiwic2x1ZyI6ImNvbW11bmlxdWUtdHBzZy1ncmFuZGUtZXRhcGUiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDktMjFUMDM6NDU6MDAuMDAwWiIsImJvZHkiOiJNYXR0aGlldSBHaXJhbHQgZXQgbW9pIGF2b25zIGNvbW1lbmPDqSBs4oCZYXZlbnR1cmUgVFBTRyBpbCB5IGEgcGx1cyBkZSAxMCBhbnMuXG5cbkF1am91cmTigJlodWksIG5vdXMgY29udGludW9ucyBkZSBjcm9pcmUgcXXigJlpbCBlc3QgZXNzZW50aWVsIGTigJlhbm5vbmNlciBzdXIgaW50ZXJuZXQgbGEgQm9ubmUgTm91dmVsbGUgZGUgSsOpc3VzLUNocmlzdCAoZXQgbGVzIGltcGxpY2F0aW9ucyBkZSBsJ8OJdmFuZ2lsZSBkYW5zIGxhIHZpZSBkdSBjcm95YW50KSBhdmVjIGZpZMOpbGl0w6ksIGNsYXJ0w6kgZXQgcGVydGluZW5jZS5cblxuTGUgcHJlbWllciByw6lmbGV4ZSBkZXMgamV1bmVzIChldCBkZXMgbW9pbnMgamV1bmVzKSBlc3QgZGUgY2hlcmNoZXIgZW4gbGlnbmUgbGVzIHLDqXBvbnNlcyDDoCBsZXVycyBxdWVzdGlvbnMgZXhpc3RlbnRpZWxsZXMgZXQgdGjDqW9sb2dpcXVlcy4gR29vZ2xlIGVzdCBkZXZlbnUgbGUgXCJwYXN0ZXVyXCIgZHUgbW9uZGUgZW50aWVyIVxuXG7igItD4oCZZXN0IHBvdXIgY2VzIHJhaXNvbnMgcXVlIG5vdXMgY29tbWVuw6dvbnMgYXVqb3VyZOKAmWh1aSB1bmUgZ3JhbmRlIMOpdGFwZSBxdWkgdmEgbm91cyBhaWRlciDDoCBtaWV1eCDDqXF1aXBlciBsZXMgY2hyw6l0aWVucyBlbiBmcmFuY29waG9uaWUgw6AgZMOpdmVsb3BwZXIgdW5lIHZpc2lvbiBiaWJsaXF1ZSBkdSBtb25kZS5cblxuRGVwdWlzIDUgYW5zLCBub3VzIGVudHJldGVub25zIGTigJlleGNlbGxlbnRlcyByZWxhdGlvbnMgYXZlYyAqKmzigJlhc3NvY2lhdGlvbiBCTEYgw4lkaXRpb25zKiouIENldCDDqWRpdGV1ciDDqXZhbmfDqWxpcXVlIGZyYW7Dp2FpcyBwdWJsaWUgdW5lIHZpbmd0YWluZSBk4oCZb3V2cmFnZXMgY2hhcXVlIGFubsOpZSBldCBnw6hyZSB0cm9pcyBsaWJyYWlyaWVzIGNocsOpdGllbm5lcyBlbiBsaWduZS5cblxuYDxwPkRlcHVpcyAyMDIwLCBub3MgYmxvZ3VldXJzIDxhIGhyZWY9XCIvYmxvZy9kb21pbmlxdWUtYW5nZXJzXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPkRvbWluaXF1ZSBBbmdlcnM8L2E+LCA8YSBocmVmPVwiL2Jsb2cvcmFwaGFlbC1jaGFycmllclwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5SYXBoYcOrbCBDaGFycmllcjwvYT4gZXQgPGEgaHJlZj1cIi9ibG9nL2JlbmphbWluLWVnZ2VuXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPkJlbmphbWluIEVnZ2VuPC9hPiBvbnQgcHVibGnDqSA8YSBocmVmPVwiaHR0cHM6Ly9ibGZzdG9yZS5jb20vY29sbGVjdGlvbnMvbGl2cmVzLWRlcy1ibG9ndWV1cnMtdHNwZ1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5wbHVzaWV1cnMgbGl2cmVzPC9hPiBpbXBvcnRhbnRzIGV0IGFwcHLDqWNpw6lzIGVuIHBhcnRlbmFyaWF0IGF2ZWMgQkxGIMOJZGl0aW9ucy4gRXQgb3VpLCDDp2EgYWlkZSB1biBwZXUgcXVlIGplIHNvaXMgbGUgZGlyZWN0ZXVyIGfDqW7DqXJhbCBkZSBCTEYgZGVwdWlzIDIwMjEuIDopPC9wPmBcblxuSWwgeSBhIGRldXggYW5zLCBUaW0gS3lsZSwgcHLDqXNpZGVudCBkdSBjb25zZWlsIGTigJlhZG1pbmlzdHJhdGlvbiBkZSBCTEYsIGEgbGFuY8OpIGxlIGTDqWZpIMOgIE1hdHQgR2lyYWx0IGV0IFJhcGhhw6tsIENoYXJyaWVyOiAq4oCcRXQgc2kgVFBTRyBldCBCTEYgc2UgcmFwcHJvY2hhaWVudCBlbmNvcmUgcGx1cz/igJ0qXG5cbkFwcsOocyBkZXV4IGFucyBkZSBkaXNjdXNzaW9ucyBlbiBjb3VsaXNzZXMsIGplIHBldXggb2ZmaWNpZWxsZW1lbnQgdOKAmWFubm9uY2VyIHF1ZSBj4oCZZXN0IGFjdMOpOiBUUFNHIHZhIGludMOpZ3JlciBhZG1pbmlzdHJhdGl2ZW1lbnQgbOKAmWFzc29jaWF0aW9uIEJMRiDDiWRpdGlvbnMuXG5cbiMjIENvbmNyw6h0ZW1lbnQsIGxlcyBjaG9zZXMgZXNzZW50aWVsbGVzIG5lIHZvbnQgcGFzIGNoYW5nZXI6XG5cbiogVG91dGVzIGxlcyByZXNzb3VyY2VzIHB1Ymxpw6llcyBzdXIgbGUgc2l0ZSByZXN0ZXJvbnQgZ3JhdHVpdGVzLCBmaW5hbmPDqWVzIHBhciBsZXMgZG9ucyBkZXMgaW50ZXJuYXV0ZXMuXG4qIE5vdHJlIHZpc2lvbiBldCBub3RyZSBtaXNzaW9uIHJlc3RlbnQgaW5jaGFuZ8OpZXM6IMOpcXVpcGVyIGxlcyBjaHLDqXRpZW5zIMOgIHZvaXIgY29tbWUgRGlldSB2b2l0IHBvdXIgdml2cmUgY29tbWUgRGlldSB2ZXV0IVxuKiBOb3RyZSBmb3JjZSByZXN0ZSBsZXMgYmxvZ3VldXJzIGZyYW5jb3Bob25lcyBxdWkgb250IGNoYWN1biB1biBhbmNyYWdlIGRhbnMgdW4gbWluaXN0w6hyZSBsb2NhbCwgZXQgbGVzIHJlc3NvdXJjZXMgcXVpIG1ldHRlbnQgbOKAmWFjY2VudCBzdXIgbGVzIGltcGxpY2F0aW9ucyBjb25jcsOodGVzIGRlcyBncmFuZGVzIHbDqXJpdMOpcyBiaWJsaXF1ZXMuXG5cbiMjIE1haXMgY2VydGFpbmVzIGNob3NlcyB2b250IGNoYW5nZXI6XG5cbjEuIErigJlhaSByZXByaXMgbGEgZGlyZWN0aW9uIGRlIGzigJnDqXF1aXBlIGRlIGLDqW7DqXZvbGVzIGV0IGZyZWVsYW5jZXMuIENlbGEgcGVybWV0IMOgIE1hdHRoaWV1IEdpcmFsdCBkZSBzZSBjb25jZW50cmVyIHN1ciBs4oCZw6ljcml0dXJlIGV0IGzigJllbnNlaWduZW1lbnQuIFR1IGFzIGNlcnRhaW5lbWVudCByZW1hcnF1w6kgcXXigJlpbCBz4oCZZXN0IHJlbWlzIMOgIGzigJnDqWNyaXR1cmUgZGVwdWlzIG5vdmVtYnJlIDIwMjMhXG4yLiBDZSByYXBwcm9jaGVtZW50IHZhIGxpYsOpcmVyIGR1IHRlbXBzIMOgIGzigJnDqXF1aXBlIFRQU0csIGNhciBsZSB0cmF2YWlsIGFkbWluaXN0cmF0aWYgKGFzc29jaWF0aWYgZXQgZmlzY2FsKSBzZXJhIGNvbmZpw6kgw6AgbOKAmcOpcXVpcGUgZGUgQkxGIGTDqWrDoCBlbiBwbGFjZS5cbjMuIE5vdXMgYWxsb25zIHJldmVuaXIgYXV4IGZvbmRhbWVudGF1eCBldCBub3VzIGNvbmNlbnRyZXIgc3VyIGzigJl1biBkZXMgc2VjcmV0cyBkZSBsYSByw6l1c3NpdGUgZGUgVFBTRzogZW50cmV0ZW5pciB1bmUgcmVsYXRpb24gZGlyZWN0ZSBhdmVjIG5vcyBsZWN0ZXVycy4gQ2VzIGRlcm5pw6hyZXMgYW5uw6llcywgYXZlYyBs4oCZZXhwbG9zaW9uIGRlIG5vcyBhdWRpZW5jZXMgc3VyIGxlcyByw6lzZWF1eCBzb2NpYXV4IGV0IHBsdXMgcsOpY2VtbWVudCBzdXIgWW91VHViZSAoMiBtaWxsaW9ucyBkZSB2dWVzIGVuIDIwMjMhKSwgbm91cyBhdm9ucyB1biBwZXUgZMOpbGFpc3PDqSBsYSByZWxhdGlvbiBkaXJlY3RlIHBhciBlLW1haWwgYXZlYyBsZXMgbGVjdGV1cnMgZHUgc2l0ZS4gTm91cyB2b3Vsb25zIHJlbcOpZGllciDDoCBjZWxhIGV0IHJlbm91ZXIgYXZlYyBsYSBjb21tdW5hdXTDqSBkZSBsZWN0ZXVycyBUUFNHLlxuXG4jIyDDgCB0b24gdG91ciFcblxuMS4gKipBcy10dSBkZXMgcXVlc3Rpb25zIGF1IHN1amV0IGRlIGNldHRlIGFubm9uY2U/KiogVHUgcGV1eCBtZSBsZXMgcG9zZXIgPGEgaHJlZj1cIiNcIiBvbmNsaWNrPVwidGhpcy5ocmVmPSdtYWlsdG86JyArICdzdGVwaGFuZScgKyAnQCcgKyAndG91dHBvdXJzYWdsb2lyZScgKyAnLmNvbSc7XCI+cGFyIGUtbWFpbDwvYT4uIFRlcyBxdWVzdGlvbnMgbSdhaWRlcm9udCDDoCBjb21wcmVuZHJlIG/DuSBub3MgbGVjdGV1cnMgbm91cyBhdHRlbmRlbnQuIEplIHZhaXMgcsOpcG9uZHJlIGF1IG1heGltdW0gZGUgcXVlc3Rpb25zLCBzb2l0IGRpcmVjdGVtZW50LCBzb2l0IHBhciB1bmUgRm9pcmUgQXV4IFF1ZXN0aW9ucyBxdWkgcsOpcG9uZHJhIGF1eCBxdWVzdGlvbnMgbGVzIHBsdXMgZnLDqXF1ZW50ZXMuXG4yLiAqKlBldXgtdHUgcHJpZXIgcG91ciBUUFNHIGR1cmFudCBjZXR0ZSB0cmFuc2l0aW9uPyoqIFBsdXNpZXVycyBncm9zIGTDqWZpcyBzb250IGRldmFudCBub3VzOlxuICAgKiBPbiBkb2l0IHRyb3V2ZXIgZGVzIHNvbHV0aW9ucyBwb3VyIHJlbWV0dHJlIGVuIG1hcmNoZSBsZXMgaW5ub3ZhdGlvbnMgdGVjaG5pcXVlcyBzdXIgbGUgc2l0ZTtcbiAgICogT24gZG9pdCB0cm91dmVyIGxlcyBtZWlsbGV1cmVzIGZhw6dvbnMgZGUgZmFpcmUgcmVzc29ydGlyIGxlcyBmb3JjZXMgZGVzIMOpcXVpcGVzIFRQU0cgZXQgQkxGLCBldCBiaWVuIMOpdmlkZW1tZW50O1xuICAgKiBDaGFxdWUgYW5uw6llLCBUUFNHIGRvaXQgdHJvdXZlciBwbHVzIGRlIDYwIDAwMCDigqwgZGUgZmluYW5jZW1lbnQgcG91ciBjb250aW51ZXIgw6AgZm9uY3Rpb25uZXIuXG4zLiBgPGRpdj48Yj5WZXV4LXR1IHNvdXRlbmlyIGZpbmFuY2nDqHJlbWVudCBsZSBtaW5pc3TDqHJlIGRlIFRQU0c/PC9iPiBOb3VzIGNoZXJjaG9ucyAyODAgcGVyc29ubmVzIHByw6p0ZXMgw6AgcmVqb2luZHJlIG5vdHJlIHLDqXNlYXUgZGUgc291dGllbiBlbiBkb25uYW50IDIwIOKCrCBwYXIgbW9pcy4gVHUgcGV1eCA8YSBocmVmPVwiaHR0cHM6Ly93d3cuaGVsbG9hc3NvLmNvbS9hc3NvY2lhdGlvbnMvdG91dHBvdXJzYWdsb2lyZS9mb3JtdWxhaXJlcy8xP3V0bV9zb3VyY2U9Y29udmVydGtpdCZ1dG1fbWVkaXVtPWVtYWlsJnV0bV9jYW1wYWlnbj1Db21tdW5pcXUlQzMlQTklMjBUUFNHJTNBJTIwZ3JhbmRlJTIwJUMzJUE5dGFwZSUyMSUyMC0lMjAxNTA2OTI0OFwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5kZXZlbmlyIHBhcnRlbmFpcmUgaWNpPC9hPi4gQWluc2ksIG5vdXMgcG91cnJvbnMgYXJyw6p0ZXIgZGUgdml2cmUgYXUgbW9pcyBsZSBtb2lzIGV0IGNlc3NlciBk4oCZYXZvaXIgbCdpbXByZXNzaW9uIGRlIHNvdXZlbnQgcGFybGVyIFwiYXJnZW50XCIuPC9kaXY+YFxuXG5KZSBtZSByw6lqb3VpcyBkJ2F2b2lyIGRlIHRlcyBub3V2ZWxsZXMuIERpcy1tb2kgY2UgcXVlIHR1IGVuIHBlbnNlcy4gSmUgc3VpcyBpbnTDqXJlc3PDqSBwYXIgdG91dCByZXRvdXIsIHN1Z2dlc3Rpb24sIGlkw6llLuKAi1xuXG5Ub3V0IHBvdXIgc2EgZ2xvaXJlIVxuXG4qKlN0w6lwaGFuZSBLYXBpdGFuaXVrLCoqPGJyPk5vdXZlYXUgZGlyZWN0ZXVyIGRlIFtUb3V0UG91clNhR2xvaXJlLmNvbV0oLykuXG5cblxuLS0tXG5cblBTOiBOZSBtYW5xdWUgc3VydG91dCBwYXMgKipub3RyZSBwcm9jaGFpbiB3ZWJpbmFpcmUqKiBxdWkgYXVyYSBsaWV1IGxlICoqMjQgc2VwdGVtYnJlIMOgIDIwaDAwKiouIFJhcGhhw6tsIENoYXJyaWVyIGV0IG1vaSBzZXJvbnMgZW4gZGlyZWN0ISBTaSB0dSBhcyBkZXMgcXVlc3Rpb25zIMOgIG5vdXMgcG9zZXIgYXUgc3VqZXQgZGUgY2UgcmFwcHJvY2hlbWVudCwgY+KAmWVzdCAqKkxFIG1vbWVudCBpZMOpYWwqKiFcblxuYDxicj48ZGl2IGFsaWduPVwiY2VudGVyXCI+IDxoMiBzdHlsZT1cIm1hcmdpbjowO1wiPjxhIGhyZWY9XCJodHRwczovL2NrLnRvdXRwb3Vyc2FnbG9pcmUuY29tL3dlYmluYWlyZS1yZW50cmVlXCIgc3R5bGU9XCJkaXNwbGF5OiBpbmxpbmUtZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsganVzdGlmeS1jb250ZW50OiBjZW50ZXI7IGJhY2tncm91bmQtY29sb3I6I0YzNjczQjsgYm9yZGVyLXJhZGl1czogNTBweDsgaGVpZ2h0OiA1NXB4OyBjb2xvcjojZmZmZmZmOyB0ZXh0LWRlY29yYXRpb246bm9uZTsgcGFkZGluZzoyMHB4XCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPjxGT05UIHNpemU9XCI1cHRcIj48c3ViPiBKZSBt4oCZaW5zY3JpcyEgPC9zdWI+PC9GT05UPjwvYT48L2gyPjwvZGl2Pjxicj5gXG5cbmA8YSBocmVmPVwiaHR0cHM6Ly9jay50b3V0cG91cnNhZ2xvaXJlLmNvbS93ZWJpbmFpcmUtcmVudHJlZVwiIHRhcmdldD1cIl9ibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+PGltZyB0aXRsZT1cImVuIHNhdm9pciBwbHVzXCIgc3JjPVwiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC93ZWJpbmFpcmVfcmVudHJlZV8yMDI0dF82OThiMjkxNThjLmpwZ1wiIHZzcGFjZT1cIjBcIiBoc3BhY2U9XCIwXCIgYWxpZ249XCJsZWZ0XCIgd2lkdGg9MTAwJSBoZWlnaHQ9MTAlPjwvYT5gXG5cbiA8ZGl2IHN0eWxlPVwiY2xlYXI6Ym90aFwiPjwvZGl2PiAiLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJTdMOpcGhhbmUgS2FwaXRhbml1ayIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9zdGVwaGFuZV9rYXBpdGFuaXVrX2Jsb2d0cHNnX2U2ZTMxYjU4NGQuanBnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcGV4ZWxzX29sbHlfMzc2MDgwOV9iMGE1YWFlZjQ5LmpwZyIsImhlaWdodCI6ODUzLCJ3aWR0aCI6MTI4MCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiQWN0dWFsaXTDqSBkZSBUUFNHIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJK4oCZYWkgbGEgam9pZSBkZSB04oCZYW5ub25jZXIgYXVqb3VyZOKAmWh1aSB1biBjaGFuZ2VtZW50IHBsdXTDtHQgaW1wb3J0YW50LiJ9LHt9XX0seyJpZCI6IjIxNzQiLCJ0aXRsZSI6IkFtw6lsaW9yZXogdm9zIHByw6lkaWNhdGlvbnMgZW4gdW5lIHNldWxlIHF1ZXN0aW9uIiwic2x1ZyI6InF1ZXN0aW9uLXBvdS1hbWVsaW9yZXItc2VzLXByZWRpY2F0aW9ucyIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wNy0yNlQwMzo0NTowMC4wMDBaIiwiYm9keSI6ImA8ZGl2IGlkPVwiZWxldmVubGFicy1hdWRpb25hdGl2ZS13aWRnZXRcIiBkYXRhLWhlaWdodD1cIjkwXCIgZGF0YS13aWR0aD1cIjEwMCVcIiBkYXRhLWZyYW1lYm9yZGVyPVwibm9cIiBkYXRhLXNjcm9sbGluZz1cIm5vXCIgZGF0YS1wdWJsaWN1c2VyaWQ9XCJjMzliMjNiYmIxN2U4MGJlNmI1ZmUzZDZmZWQ0ZDk3ZWY4YzFlNDViYzU3ZjU0MjA3NDIzYjhkMmViMTExZmIyXCIgZGF0YS1wbGF5ZXJ1cmw9XCJodHRwczovL2VsZXZlbmxhYnMuaW8vcGxheWVyL2luZGV4Lmh0bWxcIiA+TG9hZGluZyB0aGUgPGEgaHJlZj1cImh0dHBzOi8vZWxldmVubGFicy5pby90ZXh0LXRvLXNwZWVjaFwiIHRhcmdldD1cIl9ibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+RWxldmVubGFicyBUZXh0IHRvIFNwZWVjaDwvYT4gQXVkaW9OYXRpdmUgUGxheWVyLi4uPC9kaXY+PHNjcmlwdCBzcmM9XCJodHRwczovL2VsZXZlbmxhYnMuaW8vcGxheWVyL2F1ZGlvTmF0aXZlSGVscGVyLmpzXCIgdHlwZT1cInRleHQvamF2YXNjcmlwdFwiPjwvc2NyaXB0PmBcblxuIyMgTOKAmWltcG9ydGFuY2UgZGUgbGEgcXVlc3Rpb25cblxuKuKAnE1hIHByw6lkaWNhdGlvbiBzZXJhaXQtZWxsZSBhY2NlcHRhYmxlIGRhbnMgdW5lIHN5bmFnb2d1ZSBvdSB1bmUgbW9zcXXDqWU/4oCdKiBlc3QgdW5lIHF1ZXN0aW9uIGltcG9ydGFudGUuIExlIHByw6lkaWNhdGV1ciBjaHLDqXRpZW4gbmUgdmV1dCBwYXMgc2UgY29udGVudGVyIGRlIG1lc3NhZ2VzIG1vcmFsaXNhdGV1cnMgYWNjZXB0YWJsZXMgcG91ciBsYSBtYWpvcml0w6kuIEVuIGVmZmV0LCBxdWkgc2VyYWl0IGNob3F1w6kgZOKAmWVudGVuZHJlIHF14oCZaWwgZmF1dCDDqnRyZSBtb2lucyDDqWdvw69zdGUgZXQgcGx1cyBnZW50aWw/XG5cbkxlIHByw6lkaWNhdGV1ciBjaHLDqXRpZW4gbmUgY2hlcmNoZSBwYXMgw6AgZXhob3J0ZXIgc2VzIGF1ZGl0ZXVycyDDoCDDqnRyZSBzaW1wbGVtZW50IG1laWxsZXVycy4gSWwgdmV1dCBwcsOqY2hlciBKw6lzdXMsIGxlIE1lc3NpZSBjcnVjaWZpw6kgKDFDbyAyLjIpLiBJbCB2YSBwcsOqY2hlciBsYSBCb25uZSBOb3V2ZWxsZS4gSWwgdmV1dCBtb250cmVyIHF1ZSBub3VzIHNvbW1lcyBww6ljaGV1cnMsIG1vcnRzLCBldCBxdWUgY+KAmWVzdCBwYXIgSsOpc3VzIHNldWwgcXVlIG5vdXMgc29tbWVzIHBhcmRvbm7DqXMgZXQgcmVjZXZvbnMgbGEgdmllICjDiXAgMi4xLTgpLlxuXG5MZSBwcsOpZGljYXRldXIgY2hyw6l0aWVuIHNvdWxpZ25lcmEgcXVlIGNlIG7igJllc3QgcGFzIHBhciBsZXMgxZN1dnJlcyBxdWUgbm91cyBzb21tZXMgc2F1dsOpcyAow4lwIDIuOSksIG1haXMgcGFyIGxhIHB1cmUgZXQgZ3JhdHVpdGUgZ3LDomNlIGRlIERpZXUgKFJtIDMuMjQpLiDDgCB0ZWwgcG9pbnQgcXVlIGNlcnRhaW5zIGF1ZGl0ZXVycyBwYXJ0aXJvbnQgY2hvcXXDqXMsIHNlIGRpc2FudDogKuKAnFNpIGNlIHF14oCZaWwgZGl0IGVzdCB2cmFpLCBwb3VycXVvaSBlc3NheWVyIGRlIGZhaXJlIGxlIGJpZW4/IE9uIHNlcmEgcGFyZG9ubsOpIGRhbnMgdG91cyBsZXMgY2FzIeKAnSogQ2VzIHLDqWFjdGlvbnMgbW9udHJlbnQgcXXigJlpbHMgbuKAmW9udCBwYXMgY29tcHJpcyBs4oCZw4l2YW5naWxlLCBtYWlzIGF1c3NpIHF1ZSB2b3VzIGF2ZXogY29ycmVjdGVtZW50IGV4cGxpcXXDqSBsZSBjxZN1ciBkZSBs4oCZw4l2YW5naWxlLlxuXG5DaGFwZWxsIGNpdGUgSmF5IEFkYW1zLCBxdWkgZXhwcmltZSBtZXJ2ZWlsbGV1c2VtZW50IGJpZW4gY2V0dGUgaWTDqWU6XG5cbj4gU2kgbGUgc2VybW9uIHF1ZSB2b3VzIHByw6pjaGV6IGNvbnZpZW50IGF1IG1lbWJyZSBk4oCZdW5lIHN5bmFnb2d1ZSBqdWl2ZSBvdSBk4oCZdW5lIGNvbW11bmF1dMOpIHVuaXRhcmllbm5lLCBhbG9ycyBpbCBwb3NlIHVuIHZyYWkgcHJvYmzDqG1lLiBMYSBwcsOpZGljYXRpb24sIHNpIGVsbGUgZXN0IHbDqXJpdGFibGVtZW50IGNocsOpdGllbm5lLCBwb3Nzw6hkZSBkZXMgY2FyYWN0w6lyaXN0aXF1ZXMgc3DDqWNpZmlxdWVzLCBjYXIgbGUgQ2hyaXN0IHNhdXZldXIgZXQgc2FuY3RpZmljYXRldXIgeSBlc3Qgb21uaXByw6lzZW50LiBKw6lzdXMtQ2hyaXN0IGRvaXQgw6p0cmUgYXUgY8WTdXIgZHUgc2VybW9uIHF1ZSB2b3VzIHByw6pjaGV6LCBxdSdpbCBzJ2FnaXNzZSBkJ3VuIHNlcm1vbiBkJ8OpZGlmaWNhdGlvbiBvdSBkJ8OpdmFuZ8OpbGlzYXRpb24uIFxcW+KAplxcXSBcbj5cbj4gXFxcbj4gTGEgcHLDqWRpY2F0aW9uIGQnw6lkaWZpY2F0aW9uIG5lIGRvaXQgcGFzIGNlc3NlciBkJ8OqdHJlIMOpdmFuZ8OpbGlxdWU7IGMnZXN0IGNlIHF1aSBsYSByZW5kIG1vcmFsZSBwbHV0w7R0IHF1ZSBtb3JhbGlzYW50ZSwgZXQgYydlc3QgY2UgcXVpIGZhaXQgcXUnZWxsZSBuJ2VzdCBwYXMgYWNjZXB0YWJsZSBkYW5zIHVuZSBzeW5hZ29ndWUsIHVuZSBtb3NxdcOpZSwgb3UgdW5lIGNvbW11bmF1dMOpIHVuaXRhcmllbm5lLiBQYXIgw6l2YW5nw6lsaXF1ZSwgamUgdmV1eCBkaXJlIHF1ZSBsZXMgY29uc8OpcXVlbmNlcyBkZSBsYSBtb3J0IGV0IGRlIGxhIHLDqXN1cnJlY3Rpb24gZHUgQ2hyaXN0IOKAkyBzYSBtb3J0IHN1YnN0aXR1dGl2ZSwgcMOpbmFsZSwgZXQgc2EgcsOpc3VycmVjdGlvbiBjb3Jwb3JlbGxlIOKAkyBzdXIgbGUgc3VqZXQgdHJhaXTDqSBzb250IGJpZW4gcGVyY2VwdGlibGVzLiBWb3VzIG5lIGRldmV6IHBhcyBleGhvcnRlciB2b3RyZSBjb21tdW5hdXTDqSDDoCBmYWlyZSBjZSBxdWUgbGEgQmlibGUgZXhpZ2UgZOKAmWVsbGUgY29tbWUgc2kgZWxsZSBwb3V2YWl0IGxlIGZhaXJlIHBhciBlbGxlLW3Dqm1lLCBtYWlzIHNldWxlbWVudCBlbiBjb25zw6lxdWVuY2UgZGUgbGEgcHVpc3NhbmNlIHNhbHZhdHJpY2UgZGUgbGEgY3JvaXgsIGFpbnNpIHF1ZSBkZSBsYSBwdWlzc2FuY2UgZXQgZGUgbGEgcHLDqXNlbmNlIGR1IENocmlzdCwgcXVpIGRlbWV1cmUgZW4gbm91cyBldCBub3VzIHNhbmN0aWZpZSBwYXIgc29uIFNhaW50LUVzcHJpdC4gTGEgcHLDqWRpY2F0aW9uIGQnw6lkaWZpY2F0aW9uLCBwb3VyIHF1J2VsbGUgc29pdCBjaHLDqXRpZW5uZSwgZG9pdCBwbGVpbmVtZW50IHByZW5kcmUgZW4gY29uc2lkw6lyYXRpb24gbCfFk3V2cmUgZGUgbGEgZ3LDomNlIGRlIERpZXUgZGFucyBsZSBzYWx1dCBldCBsYSBzYW5jdGlmaWNhdGlvbi48c3VwPlsxXSgvYXJ0aWNsZS9xdWVzdGlvbi1wb3UtYW1lbGlvcmVyLXNlcy1wcmVkaWNhdGlvbnMjbm90ZXMpPC9zdXA+XG5cbkF2YW50IGRlIHByw6pjaGVyLCBpbCBmYXV0IMOqdHJlIHPDu3IgcXVlIG5vdHJlIG1lc3NhZ2UgZXN0IHVuIHNjYW5kYWxlIHBvdXIgbGUgSnVpZiBldCBsZSBNdXN1bG1hbi4gU295b25zIGNsYWlyczogSWwgbuKAmWVzdCBwYXMgcXVlc3Rpb24gaWNpIGRlIHByw6pjaGVyIGR1IHJhY2lzbWUgb3UgZGUgbGEgaGFpbmUsIG1haXMgYmllbiBkZSBwcsOqY2hlciBs4oCZw4l2YW5naWxlIHNpIGNsYWlyZW1lbnQgcXVlIG5vdHJlIHByw6lkaWNhdGlvbiBzb2l0IGNvbW1lIHVuIMOpbGVjdHJvY2hvYyBwb3VyIGxlcyBqdWlmcywgbGVzIG11c3VsbWFucywgbGVzIGF0aMOpZXMgZXQgw6AgdG91dGVzIGxlcyBhdXRyZXMgcmVsaWdpb25zIHF1aSBjcm9pZW50IMOqdHJlIHNhdXbDqXMgcGFyIGxldXJzIMWTdXZyZXMuXG5cbiMjIEzigJl1dGlsaXTDqSBkZSBsYSBxdWVzdGlvblxuXG5JbCBwZXV0IHNlbWJsZXIgZmFjaWxlIGRlIG5lIHBhcyBzZSB0cm9tcGVyIGVuIHByw6pjaGFudCBzdXIgUm9tYWlucyAzLjIzLTI0LlxuXG4+IENhciBpbCBu4oCZeSBhIHBhcyBkZSBkaXN0aW5jdGlvbjogdG91cyBvbnQgcMOpY2jDqSBldCBzb250IHByaXbDqXMgZGUgbGEgZ2xvaXJlIGRlIERpZXU7IGV0IGlscyBzb250IGdyYXR1aXRlbWVudCBqdXN0aWZpw6lzIHBhciBzYSBncsOiY2UsIHBhciBsZSBtb3llbiBkZSBsYSByw6lkZW1wdGlvbiBxdWkgZXN0IGRhbnMgbGUgQ2hyaXN0LUrDqXN1cy5cblxuTWFpcyBk4oCZYXV0cmVzIHBhc3NhZ2VzIGRlIGxhIEJpYmxlIHNvbnQgcGx1cyBjb21wbGlxdcOpcyDDoCBwcsOqY2hlciBkZSBmYcOnb24gY2hyw6l0aWVubmUuIFNlIHBvc2VyIGNldHRlIHF1ZXN0aW9uIHBldXQgbGl0dMOpcmFsZW1lbnQgc2F1dmVyIHZvdHJlIHByw6lkaWNhdGlvbi5cblxuQ2VydGFpbmVzIHBhcnRpZXMgZHUgW3Nlcm1vbiBkZSBKw6lzdXMgc3VyIGxhIG1vbnRhZ25lXSgvYXJ0aWNsZS9jb21wcmVuZHJlLWxlLXNlcm1vbi1zdXItbGEtbW9udGFnbmUvKSwgcGFyIGV4ZW1wbGUsIHBldXZlbnQgdHLDqHMgZmFjaWxlbWVudCDDqnRyZSBtYWwgaW50ZXJwcsOpdMOpZXMgZXQgZG9uYyBtYWwgcHLDqmNow6llcy4gRXhlbXBsZTogKuKAnFNpIHRvbiDFk2lsIGRyb2l0IGVzdCBwb3VyIHRvaSB1bmUgb2NjYXNpb24gZGUgY2h1dGUsIGFycmFjaGUtbGUgZXQgamV0dGUtbGUgbG9pbiBkZSB0b2ksIGNhciBpbCBlc3QgYXZhbnRhZ2V1eCBwb3VyIHRvaSBxdeKAmXVuIHNldWwgZGUgdGVzIG1lbWJyZXMgcMOpcmlzc2UgZXQgcXVlIHRvbiBjb3JwcyBlbnRpZXIgbuKAmWFpbGxlIHBhcyBkYW5zIGxhIGfDqWhlbm5l4oCdIChNdCA1LjI5KSwqIG91IGVuY29yZTogKuKAnFNveWV6IGRvbmMgcGFyZmFpdHMsIGNvbW1lIHZvdHJlIFDDqHJlIGPDqWxlc3RlIGVzdCBwYXJmYWl04oCdIChNdCA1LjQ4KS4qXG5cbklsIGVuIHZhIGRlIG3Dqm1lIHBvdXIgYmVhdWNvdXAgZGUgdGV4dGVzIGRlIGzigJlBbmNpZW4gVGVzdGFtZW50IChsZSBsaXZyZSBzYWNyw6kgZGVzIGp1aWZzISkuIEp1bGVzLU1hcmNlbCBOaWNvbGUgZXhwbGlxdWUgYmllbiBjZSBkYW5nZXIuIElsIG1vbnRyZSBjb21tZW50IGxlcyBqdWlmcyBkZSBs4oCZw6lwb3F1ZSBkZSBKw6lzdXMgc29udCB0b21iw6lzIGRhbnMgY2UgcGnDqGdlIGV0IGNvbW1lbnQgb24gcGV1dCBs4oCZw6l2aXRlciBkYW5zIG5vcyBwcsOpZGljYXRpb25zOlxuXG4+IEPigJllc3QgdG91am91cnMgw6AgbGEgbHVtacOocmUgZHUgTm91dmVhdSBUZXN0YW1lbnQgcXXigJlpbCBmYXV0IGludGVycHLDqXRlciBs4oCZQW5jaWVuIDsgbWFpcyBzYW5zIGzigJlBbmNpZW4gVGVzdGFtZW50LCBsZSBOb3V2ZWF1IHNlIHRyb3V2ZSBwcml2w6kgZOKAmXVuIGZvbmRlbWVudCBxdWkgbGUgbWV0IGVuIHZhbGV1ci5cbj5cbj4gQ2UgcXXigJlpbCBuZSBmYXV0IGphbWFpcyBvdWJsaWVyLCBj4oCZZXN0IHF1ZSB0b3VzIGxlcyBkZXV4LCBjaGFjdW4gw6Agc2EgbWFuacOocmUsIHJlbmRlbnQgdMOpbW9pZ25hZ2Ugw6AgSsOpc3VzLUNocmlzdC4gQ+KAmWVzdCBwYXNzZXIgw6AgY8O0dMOpIGR1IG1lc3NhZ2UgY2VudHJhbCBkZSBs4oCZw4ljcml0dXJlIHF1ZSBkZSBuw6lnbGlnZXIgY2V0dGUgw6l2aWRlbmNlLiBKw6lzdXMsIHBlbmRhbnQgc29uIG1pbmlzdMOocmUgdGVycmVzdHJlLCDDqXRhaXQgYXR0cmlzdMOpIGRlIHZvaXIgc2VzIGludGVybG9jdXRldXJzLCBtYWxncsOpIGxldXIgw6l0dWRlIGFwcHJvZm9uZGllIGRlIGxhIExvaSBldCBkZXMgcHJvcGjDqHRlcywgcmVmdXNlciBkZSByZWNvbm5hw650cmUgcXVlIGNlcyB2aWV1eCB0ZXh0ZXMgYXZhaWVudCBwb3VyIGJ1dCB1bHRpbWUgZGUgbGVzIGNvbmR1aXJlIMOgIGx1aS5cbj5cbj4gTm90cmUgcHLDqWRpY2F0aW9uIGRvaXQgcHLDqXNlbnRlciDDoCBub3MgYXVkaXRldXJzIErDqXN1cy1DaHJpc3QsIGV0IErDqXN1cy1DaHJpc3QgY3J1Y2lmacOpLCBjZSBxdWkgaW5jbHV0IGzigJlhbm5vbmNlIGRlIHNhIHLDqXN1cnJlY3Rpb24gc2FucyBsYXF1ZWxsZSBub3RyZSBmb2kgc2VyYWl0IHZhaW5lLlxuPlxuPiBPbiBlbnRlbmQgcGFyZm9pcywgZHUgaGF1dCBk4oCZdW5lIGNoYWlyZSBjaHLDqXRpZW5uZSwgZGVzIGV4aG9ydGF0aW9ucyBxdWkgYXVyYWllbnQgcHUgw6p0cmUgZXhwcmltw6llcyBwYXIgdW4ganVpZiB6w6lsw6ksIHVuIG11c3VsbWFuIGhvbm7DqnRlLCB2b2lyZSBwYXIgdW4gY29uZnVjaWFuaXN0ZSB2ZXJ0dWV1eCBvdSB1biBoaW5kb3Vpc3RlIGJpZW4gaW50ZW50aW9ubsOpLiBD4oCZZXN0IGFmZmxpZ2VhbnQuIFF1ZWxsZSBxdWUgc29pdCBs4oCZaW5maW5pZSB2YXJpw6l0w6kgZGVzIGVuc2VpZ25lbWVudHMgYmlibGlxdWVzLCBpbHMgcG9pbnRlbnQgdG91am91cnMgdmVycyBjZSBjZW50cmUgY2hyaXN0b2xvZ2lxdWUuIMOJdml0b25zIGxlcyDDqWNhcnRzIGNlbnRyaWZ1Z2VzIHF1aSDDoCBsYSBsaW1pdGUgZGV2aWVuZHJhaWVudCB1bmUgdHJhaGlzb24gZGUgbm90cmUgbWFuZGF0PHN1cD5bMl0oL2FydGljbGUvcXVlc3Rpb24tcG91LWFtZWxpb3Jlci1zZXMtcHJlZGljYXRpb25zI25vdGVzKTwvc3VwPi5cblxuIyMgQ29uY2x1c2lvblxuXG5Kw6lzdXMgZXN0IGxlIGNlbnRyZSBkZSBsYSBCaWJsZS4gVG91dCBwb2ludGUgdmVycyBsdWkuIFBldSBpbXBvcnRlIGxlIHRleHRlIGJpYmxpcXVlLCBpbCBmYXV0IHNlIGRlbWFuZGVyOiAq4oCcT8O5IHNlIHNpdHVlIGNlIHBhc3NhZ2UgcGFyIHJhcHBvcnQgw6AgSsOpc3VzP+KAnSogQ2VsYSBub3VzIMOpdml0ZXJhIGRlIHByw6pjaGVyIGwnQW5jaWVuIFRlc3RhbWVudCBkZSBtYW5pw6hyZSBtb3JhbGlzYXRyaWNlLiBDZWxhIG5vdXMgw6l2aXRlcmEgZGUgcHLDqmNoZXIgw6AgcGFydGlyIGRlcyDDqXZhbmdpbGVzIGNvbW1lIGxlIGZlcmFpdCB1biBub24tY2hyw6l0aWVuLiBFdCBjZWxhIGVzdCB1bmUgYm9ubmUgY2hvc2UuIEMnZXN0IHBvdXIgY2VsYSBxdSdvbiBzZSBwb3NlIGNldHRlIHF1ZXN0aW9uIGNoYXF1ZSBmb2lzIHF1J29uIHByw6lwYXJlIHVuZSBwcsOpZGljYXRpb246ICrigJxNb24gbWVzc2FnZSBwb3VycmFpdC1pbCDDqnRyZSBwcsOqY2jDqSBwYXIgdW4ganVpZiBvdSB1biBtdXN1bG1hbj/igJ0qIFNpIGxhIHLDqXBvbnNlIGVzdCBvdWksIGFsb3JzIG5vdHJlIHByw6lkaWNhdGlvbiBu4oCZZXN0IHBhcyBlbmNvcmUgY29udmVydGllLiBFbGxlIG7igJllc3QgcGFzIGVuY29yZSBjaHLDqXRpZW5uZS5cblxuYDxzdWIgaWQ9XCJub3Rlc1wiPkFydGljbGUgcHVibGnDqSBwb3VyIGxhIHByZW1pw6hyZSBmb2lzIGxlIDE3IGbDqXZyaWVyIDIwMTQuIFJlbWlzIGVuIGF2YW50IHBhciBsYSByw6lkYWN0aW9uIGxlIDI2IGp1aWxsZXQgMjAyNCwgcG91ciBhdHRlaW5kcmUgZGUgbm91dmVhdXggbGVjdGV1cnMuPC9zdWI+YFxuXG5gPHN1Yj4xLiBDaGFwZWxsLCBCcnlhbi4gPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL3ByZWNoZXItbGFydC1ldC1sYS1tYW5pZXJlXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPjxlbT5QcsOqY2hlciBs4oCZYXJ0IGV0IGxhIG1hbmnDqHJlPC9lbT48L2E+LiBEaWFrb25vcy4gQ2hhcm9sczogRXhjZWxzaXMsIDIwMDkuIHAuIDMxMS0zMTI8YnI+Mi4gTmljb2xlLCA8YSBocmVmPVwiaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvcHJlY2lzLWRlLXByZWRpY2F0aW9uLWNocmV0aWVubmVcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+PGVtPlByw6ljaXMgZGUgcHLDqWRpY2F0aW9uIGNocsOpdGllbm5lPC9lbT48L2E+LCBwLiAyMi0yMy4gQ2l0w6kgcGFyIERvbWluaXF1ZSBBbmdlcnMgZGFucyBzb24gY291cnMgZOKAmUhvbWlsw6l0aXF1ZSBuwrAzIGRpc3BlbnPDqSDDoCBs4oCZSUJHLjwvc3ViPmBcblxuIyMgMyBhdXRyZXMgYXJ0aWNsZXMgc3VyIGxhIHByw6lkaWNhdGlvbjpcblxuKiA8YSBocmVmPVwiL2FydGljbGUvYnJ5YW4tY2hhcGVsbC1xdWVzdC1jZS1xdWUtbGEtcHJlZGljYXRpb24tdGV4dHVlbGxlL1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5CcnlhbiBDaGFwZWxsOiBRdeKAmWVzdC1jZSBxdWUgbGEgcHLDqWRpY2F0aW9uIHRleHR1ZWxsZT88L2E+XG4qIDxhIGhyZWY9XCIvYXJ0aWNsZS9sZS1jb2V1ci1jaWJsZS1kZS1sYS1wcmVkaWNhdGlvblwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5MZSBjxZN1ciwgY2libGUgZGUgbGEgcHLDqWRpY2F0aW9uPC9hPlxuKiA8YSBocmVmPVwiL2FydGljbGUvMTAtbWFuaWVyZXMtZGUtYmllbi1yZWFnaXItYS11bmUtbWF1dmFpc2UtcHJlZGljYXRpb25cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+MTAgZmHDp29ucyBkZSBiaWVuIHLDqWFnaXIgw6AgdW5lIG1hdXZhaXNlIHByw6lkaWNhdGlvbjwvYT5cblxuXFxcbiIsImF1dGhvciI6eyJmdWxsTmFtZSI6IlN0w6lwaGFuZSBLYXBpdGFuaXVrIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3N0ZXBoYW5lX2thcGl0YW5pdWtfYmxvZ3Rwc2dfZTZlMzFiNTg0ZC5qcGciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9lbGxpb3Rfc2xvbWFuXzRfVG9ka19aSV9Ka3JfWV91bnNwbGFzaF82YzY3NDY5NWUzLmpwZyIsImhlaWdodCI6ODUzLCJ3aWR0aCI6MTI4MCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiUHLDqWRpY2F0aW9uIGV0IGVuc2VpZ25lbWVudCJ9XSwibW9kdWxlcyI6W3t9LHsiX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiQ+KAmcOpdGFpdCB1bmUgZGUgbWVzIHF1ZXN0aW9ucyBwcsOpZsOpcsOpZXMgZGUgQnJ5YW4gQ2hhcGVsbCBsb3JzIGTigJl1biBzw6ltaW5haXJlOiAq4oCcVm90cmUgcHLDqWRpY2F0aW9uIHNlcmFpdC1lbGxlIGFjY2VwdGFibGUgc2kgdm91cyBsYSBwcsOqY2hpZXogZGFucyB1bmUgbW9zcXXDqWUgb3UgdW5lIHN5bmFnb2d1ZT/igJ0qIFNpIG91aSwgYWxvcnMgdm90cmUgcHLDqWRpY2F0aW9uIG7igJllc3QgcGFzIGVuY29yZSBjaHLDqXRpZW5uZS4ifV19LHsiaWQiOiIzNzgxIiwidGl0bGUiOiJEw6lmaSBkZSBsZWN0dXJlIDIwMjQ6IFJlam9pZ25lei1ub3VzIGNldHRlIGFubsOpZSEiLCJzbHVnIjoiZGVmaS1kZS1sZWN0dXJlLWJsZi0yMDI0IiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDIzLTEyLTMwVDExOjAwOjAwLjAwMFoiLCJib2R5IjoiQ2hhcXVlIGFubsOpZSwgamUgdm91cyBlbmNvdXJhZ2Ugw6AgW2Nob2lzaXIgdW4gYm9uIHBsYW4gZGUgbGVjdHVyZSBiaWJsaXF1ZV0oL2FydGljbGUvcGxhbi1kZS1sZWN0dXJlLWJpYmxpcXVlKS4gTWFpcyBqZSB2b3VzIGludml0ZSBhdXNzaSDDoCAqKnJlam9pbmRyZSBsZSBkw6lmaSBkZSBsZWN0dXJlIDIwMjQqKiBkZSBUUFNHIGV0IEJMRiDDiWRpdGlvbnMuXG5cbiMjIyBWb2ljaSBsYSBwcsOpc2VudGF0aW9uIHZpZMOpbyBkdSBkw6lmaSBkZSBsZWN0dXJlIGVuIDIwMjM6XG5cbjxpZnJhbWUgbG9hZGluZz1cImxhenlcIiBjbGFzcz1cInlvdXR1YmUtcGxheWVyXCIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiNDA1XCIgc3JjPVwiaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvWG1kMHZLQzZKbTA/dmVyc2lvbj0zJmFtcDtyZWw9MSZhbXA7c2hvd3NlYXJjaD0wJmFtcDtzaG93aW5mbz0xJmFtcDtpdl9sb2FkX3BvbGljeT0xJmFtcDtmcz0xJmFtcDtobD1mci1GUiZhbXA7YXV0b2hpZGU9MiZhbXA7d21vZGU9dHJhbnNwYXJlbnRcIiBhbGxvd2Z1bGxzY3JlZW49XCJ0cnVlXCIgc3R5bGU9XCJib3JkZXI6MDtcIiBzYW5kYm94PVwiYWxsb3ctc2NyaXB0cyBhbGxvdy1zYW1lLW9yaWdpbiBhbGxvdy1wb3B1cHMgYWxsb3ctcHJlc2VudGF0aW9uXCI+PC9pZnJhbWU+XG5cbiMjIETigJlvw7kgdmllbnQgbOKAmWlkw6llIGTigJl1biBkw6lmaSBkZSBsZWN0dXJlP1xuXG5DZXMgZGVybmnDqHJlcyBhbm7DqWVzIG9udCDDqXTDqSBleGNlcHRpb25uZWxsZXMgw6AgQkxGIMOJZGl0aW9ucy4gQ2hhcXVlIGFubsOpZSwgbm91cyB2ZW5kb25zIGV0IGRpc3RyaWJ1b25zIGRlIHBsdXMgZW4gcGx1cyBkZSBsaXZyZXMuXG5cbk1haXMgb24gcG91cnJhaXQgw6p0cmUgZW4gdHJhaW4gZGUgcGFzc2VyIMOgIGPDtHTDqSBkZSBs4oCZZXNzZW50aWVsLiBNYWxncsOpIGTigJlleGNlbGxlbnRlcyB2ZW50ZXMgZXQgZGVzIGFubsOpZXMgcmVjb3JkLCBub3VzIHBvdXZvbnMgw6ljaG91ZXIgZGFucyBub3RyZSBtaXNzaW9uLiBFbiBlZmZldCwgc2kgbGVzIGNocsOpdGllbnMgZGUgZnJhbmNvcGhvbmllIG5lIGxpc2VudCBwYXMgbGVzIGxpdnJlcyBxdeKAmWlscyBhY2jDqHRlbnQsIHPigJlpbHMgbmUgc29udCBwYXMgdHJhbnNmb3Jtw6lzIHBhciBsZXMgaWTDqWVzIGV0IGxlcyBlbnNlaWduZW1lbnRzIGRlIG5vcyBhdXRldXJzLCBub3VzIMOpY2hvdW9ucyBkYW5zICoqbm90cmUgbWlzc2lvbiBk4oCZYWlkZXIgbGVzIGNocsOpdGllbnMgw6Agbm91cnJpciB1bmUgcGFzc2lvbiBwb3VyIERpZXUqKi5cblxuTm91cyB2b3Vsb25zIGVuY291cmFnZXIgbGVzIGNocsOpdGllbnMgw6AgbGlyZSBkZSBib25zIGxpdnJlcywgZXQgcG91ciBjZWxhLCBub3VzIHZvdXMgcHJvcG9zb25zIGNlIGTDqWZpIGRlIGxlY3R1cmUuXG5cbkNlIGTDqWZpIGVzdCB1bmUgaWTDqWUgZGUgbm90cmUgYXV0ZXVyIFRpbSBDaGFsbGllcy4gRW4gcGFydGVuYXJpYXQgYXZlYyBbVG91dFBvdXJTYUdsb2lyZS5jb21dKGh0dHA6Ly9Ub3V0UG91clNhR2xvaXJlLmNvbSkgbm91cyB2b3VzIHByw6lzZW50b25zICoqbGUgZMOpZmkgZGUgbGVjdHVyZSBCTEYgMjAyNCoqOlxuXG48ZGl2IGFsaWduPVwiY2VudGVyXCI+PGltZyBzcmM9XCJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0RFRklfTEVDVFVSRV8yMDI0X3YyX3BhZ2VfMDAwMV9mMGFhNTdjOWIxLmpwZ1wiIHdpZHRoPTEwMCUgaGVpZ2h0PTQwJT48L2Rpdj5cblxuW1TDqWzDqWNoYXJnZXIgbOKAmWFmZmljaGUgQTMgZHUgZMOpZmkgZGUgbGVjdHVyZSAyMDI0XShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0RFRklfTEVDVFVSRV8yMDI0X3YyXzBiMjI1Y2ZmZmUucGRmKS5cblxuIyMgRW4gcXVvaSBjb25zaXN0ZSBsZSBkw6lmaSBkZSBsZWN0dXJlIEJMRiAyMDI0P1xuXG4qKkxlIGTDqWZpIGRlIGxlY3R1cmUgMjAyNCoqIGVzdCBjb21wb3PDqSBkZSA0IG5pdmVhdXggZGUgZGlmZmljdWx0w6kuIENoYXF1ZSBuaXZlYXUgY29udGllbnQgZGVzIGlkw6llcyBkZSBsaXZyZXMgw6AgbGlyZSAoc2V1bGVtZW50IDIgZGVzIGlkw6llcyBzb250IHNww6ljaWZpcXVlcyDDoCBCTEYgw4lkaXRpb25zIGV0IENydWNpZm9ybWUsIGxlcyBkZXV4IG1hcnF1ZXMgcXVlIG5vdXMgZGlzdHJpYnVvbnMgZW4gRXVyb3BlKS4gTGUgZMOpZmkgZXN0IGRvbmMgdnJhaW1lbnQgb3V2ZXJ0IMOgIHRvdXMuIE3Dqm1lIGNldXggcXVpIG5lIGNvbm5haXNzZW50IHBhcyBub3RyZSBtYWlzb24gZOKAmcOpZGl0aW9uLlxuXG7DgCB2b3VzIGRlIGNob2lzaXIgdm90cmUgbml2ZWF1OlxuXG4xLiAqKkxlIGxlY3RldXIgZMOpYnV0YW50OioqIENlIGTDqWZpIGVzdCBkZSBsaXJlIDEzIGxpdnJlcyBlbiAyMDI0LCBj4oCZZXN0LcOgLWRpcmUgdW4gbGl2cmUgw6AgbGlyZSB0b3V0ZXMgbGVzIDQgc2VtYWluZXMuIE5vdXMgdm91cyBkb25ub25zIDEzIGlkw6llcyBkZSBsaXZyZXMgw6AgbGlyZS5cbjIuICoqTGUgbGVjdGV1ciBzw6lyaWV1eDoqKiBEYW5zIGNlIGTDqWZpLCBvbiBham91dGUgMTMgbGl2cmVzIHN1cHBsw6ltZW50YWlyZXMsIGNlIHF1aSBwb3J0ZSBsZSByeXRobWUgw6AgMSBsaXZyZSB0b3V0ZXMgbGVzIDIgc2VtYWluZXMuIEVuIHBsdXMgZGVzIDEzIGlkw6llcyBkdSBuaXZlYXUgZMOpYnV0YW50LCBvbiB2b3VzIHN1Z2fDqHJlIDEzIGF1dHJlcyBpZMOpZXMgZGUgbGVjdHVyZXMuXG4zLiAqKkxlIGxlY3RldXIgesOpbMOpOioqIERhbnMgY2UgZMOpZmksIG9uIGFqb3V0ZSAyNiBsaXZyZXMgc3VwcGzDqW1lbnRhaXJlcywgY2UgcXVpIGVucmljaGl0IGxlIHRvdGFsIMOgIDUyLCBzb2l0IDEgbGl2cmUgw6AgbGlyZSB0b3V0ZXMgbGVzIHNlbWFpbmVzLiBWb3VzIGxpc2V6IHRvdXMgbGVzIGxpdnJlcyBkZXMgbml2ZWF1eCDCqyBkw6lidXRhbnQgwrsgZXQgwqsgc8OpcmlldXggwrssICsgMjYgbGl2cmVzIGR1IG5pdmVhdSDCqyB6w6lsw6kgwrsuXG40LiAqKkxlIGxlY3RldXIgcGFzc2lvbm7DqToqKiBDZSBkw6lmaSBkb3VibGUgbGUgdG90YWwgw6AgMTA0IGxpdnJlcywgY2UgcXVpIMOpdGFibGl0IHVuIHJ5dGhtZSBmb3UgZGUgMiBsaXZyZXMgcGFyIHNlbWFpbmUuXG5cbiMjIFF1ZXN0aW9ucyBmcsOpcXVlbW1lbnQgcG9zw6llczpcblxuIyMjIERvaXMtamUgYWNoZXRlciBkZXMgbGl2cmVzP1xuXG5Ob24hIENlIGTDqWZpIG5lIHBvdXNzZSBwYXMgw6AgbOKAmWFjaGF0LiBJbCBu4oCZZXN0IHBhcyBjcsOpw6kgcGFyIEJMRiDDiWRpdGlvbnMgcG91ciB2ZW5kcmUgcGx1cyBkZSBsaXZyZXMsIG1haXMgcG91ciBlbmNvdXJhZ2VyIGxhIGxlY3R1cmUgZGUgbWFuacOocmUgZ8OpbsOpcmFsZS4gSmUgc3VpcyBzw7tyIHF14oCZb24gYSB0b3VzIGNoZXogbm91cyBkZXMgbGl2cmVzIHF1ZSBs4oCZb24gYSBkw6lqw6AgYWNoZXTDqXMsIG1haXMgcXVlIGzigJlvbiBu4oCZYSBqYW1haXMgbHVzLiBWb3VzIG7igJnDqnRlcyBwYXMgZGFucyBs4oCZb2JsaWdhdGlvbiBk4oCZYWNoZXRlciBkZSBub3V2ZWF1eCBsaXZyZXMsIHZvdXMgcG91dmV6IGNvbW1lbmNlciBhdmVjIGNldXggcXVlIHZvdXMgYXZleiBkw6lqw6AuIFZvdXMgcG91dmV6IMOpZ2FsZW1lbnQgcHJvcG9zZXIgw6Agdm9zIGFtaXMgcXVpIHNvbnQgbGVjdGV1cnMsIHBhciBleGVtcGxlLCBk4oCZw6ljaGFuZ2VyIGRlcyBsaXZyZXMgYXZlYyB2b3VzLlxuXG4jIyMgVW5lIGluc2NyaXB0aW9uIGVzdC1lbGxlIG7DqWNlc3NhaXJlIHBvdXIgcGFydGljaXBlciBhdSBkw6lmaT9cblxuQXVjdW5lIGluc2NyaXB0aW9uIG7igJllc3QgbsOpY2Vzc2FpcmUuIFtUw6lsw6ljaGFyZ2V6IGzigJlhZmZpY2hlIDIwMjRdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvREVGSV9MRUNUVVJFXzIwMjRfdjJfMGIyMjVjZmZmZS5wZGYpIGV0IGTDqW1hcnJleiBhdWpvdXJk4oCZaHVpLiBTaSB2b3VzIGxlIHNvdWhhaXRleiwgaWwgZXhpc3RlIFxcW3VuIGdyb3VwZSBGYWNlYm9vayBEw6lmaSBkZSBsZWN0dXJlIDIwMjRcXF0gcG91ciBwYXJ0YWdlciB2b3MgZW5jb3VyYWdlbWVudHMsIGNvbnNlaWxzIGFpbnNpIHF1ZSB2b3MgZGlmZsOpcmVudGVzIGxlY3R1cmVzIGF2ZWMgZOKAmWF1dHJlcyBwZXJzb25uZXMgcXVpIHN1aXZlbnQgbGUgZMOpZmkgY29tbWUgdm91cy5cblxuW1Jlam9pbmRyZSBsZSBncm91cGUgRmFjZWJvb2tdKGh0dHBzOi8vd3d3LmZhY2Vib29rLmNvbS9ncm91cHMvNDU0Mzg3NTg2MzEwMDcxKS5cblxuIyMjIEZhdXQtaWwgbGlyZSB1bmlxdWVtZW50IGRlcyBsaXZyZXMgY2hyw6l0aWVucz9cblxuT2ggcXVlIG5vbiEgTm90cmUgb2JqZWN0aWYgZXN0IGTigJllbmNvdXJhZ2VyIGxhIGxlY3R1cmUuIExlcyBnZW5zIHF1aSBsaXNlbnQgcsOpZ3VsacOocmVtZW50IGRlcyBib25zIGxpdnJlcyBzZXJvbnQgZGVzIG1laWxsZXVycyBsZWN0ZXVycyBkZSBsYSBCaWJsZSBldCBkZSBsaXZyZXMgY2hyw6l0aWVucywgbWFpcyBEaWV1IG5vdXMgYSBkb25uw6kgdGVsbGVtZW50IGRlIGJvbnMgYXV0ZXVycyBub24gY2hyw6l0aWVucywgaWwgbmUgZmF1dCBzdXJ0b3V0IHBhcyBz4oCZZW4gcHJpdmVyIVxuXG5QcmVub25zIHBhciBleGVtcGxlIGxlcyAxMyBwcmVtacOocmVzIGlkw6llcyBkZSBsZWN0dXJlcyAocXVpIGNvcnJlc3BvbmRlbnQgYXUgbml2ZWF1IMKrIGTDqWJ1dGFudCDCuyk6XG5cbiAxLiBVbiBsaXZyZSBwdWJsacOpIGVuIDIwMjIgb3UgMjAyM1xuIDIuIFVuIG3DqW1vaXJlIG91IGF1dG9iaW9ncmFwaGllXG4gMy4gVW4gcm9tYW4gY2xhc3NpcXVlXG4gNC4gKipVbiBsaXZyZSDDqWNyaXQgcGFyIHVuIHBhc3RldXIgb3UgdW5lIGZlbW1lIGRlIHBhc3RldXIqKlxuIDUuICoqVW4gbGl2cmUgc3VyIHVuIGxpdnJlIGRlIGxhIEJpYmxlKipcbiA2LiAqKlVuIGxpdnJlIHB1Ymxpw6kgcGFyIENydWNpZm9ybWUqKlxuIDcuICoqVW4gbGl2cmUgYXZlYyBsZSBtb3Qgwqsgw4l2YW5naWxlIMK7IGRhbnMgbGUgdGl0cmUgb3Ugc291cy10aXRyZSoqXG4gOC4gVW4gbGl2cmUgYXZlYyB1bmUgaW1hZ2UgZOKAmXVuZSBwZXJzb25uZSBzdXIgbGEgY291dmVydHVyZVxuIDkuIFVuIGxpdnJlIHF1ZSB2b3VzIG5lIHZvdWxleiBwYXMgbGlyZSwgbWFpcyBxdWUgdm91cyBkZXZyaWV6XG4xMC4gVW4gbGl2cmUgcG91ciBlbmZhbnQgb3UgYWRvc1xuMTEuICoqVW4gbGl2cmUgZGUgQkxGIMOJZGl0aW9ucyoqXG4xMi4gKipVbiBsaXZyZSBkdSByYXlvbiDCqyB2aWUgY2hyw6l0aWVubmUgwrsqKlxuMTMuIFVuIGxpdnJlIGRlIHZvdHJlIGNob2l4XG5cbkxhIG1vaXRpw6kgc2V1bGVtZW50IGRlcyBpZMOpZXMgY29uY2VybmVudCBkZXMgbGl2cmVzIGNocsOpdGllbnMgKGplIGxlcyBhaSBtaXNlcyBlbiBncmFzKTogNiBwaXN0ZXMgc3VyIGxlcyAxMyBpZMOpZXMuIEV0IGVuY29yZSwgauKAmWltYWdpbmUgcXXigJlvbiBwZXV0IHRyb3V2ZXIgZGVzIGxpdnJlcyBub24gY2hyw6l0aWVucyB1dGlsaXNhbnQgbGUgbW90IMKrIMOJdmFuZ2lsZSDCuy4g8J+ZglxuXG4jIyMgT8O5IHBldXQtb24gdMOpbMOpY2hhcmdlciBs4oCZYWZmaWNoZSBkdSBkw6lmaT9cblxuVm91cyBwb3V2ZXogW2xhIHTDqWzDqWNoYXJnZXIgZ3JhdHVpdGVtZW50IGljaV0oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9ERUZJX0xFQ1RVUkVfMjAyNF92Ml8wYjIyNWNmZmZlLnBkZikuIFZvdXMgcG91dmV6IGzigJlpbXByaW1lciBhdXRhbnQgcXVlIHZvdXMgdm91bGV6IGV0IGxhIGRpc3RyaWJ1ZXIgw6Agdm9zIGFtaXMgbGlicmVtZW50LiBFbmNvdXJhZ2VvbnMgbGEgbGVjdHVyZSFcblxuW1TDqWzDqWNoYXJnZXIgbOKAmWFmZmljaGUgQTMgZHUgZMOpZmkgZGUgbGVjdHVyZSAyMDI0XShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0RFRklfTEVDVFVSRV8yMDI0X3YyXzBiMjI1Y2ZmZmUucGRmKS5cblxuIyMjIFNpIGrigJlhaSBk4oCZYXV0cmVzIHF1ZXN0aW9ucyBzdXIgbGUgZMOpZmksIG/DuSBsZXMgcG9zZXI/XG5cblNpIHZvdXMgYXZleiBk4oCZYXV0cmVzIHF1ZXN0aW9ucywgcG9zZXotbGVzIHNpbXBsZW1lbnQgcGFyIGVtYWlsIMOgIFtpbmZvQGJsZmVkaXRpb25zLmNvbV0obWFpbHRvOmluZm9AYmxmZWRpdGlvbnMuY29tKS4gUXVlbHF14oCZdW4gZGUgbOKAmcOpcXVpcGUgdm91cyByw6lwb25kcmEgcmFwaWRlbWVudC5cblxuTWVyY2kgW0FpbmEgUmFrb3RvbWFsYWxhXShodHRwczovL3d3dy5ibGZlZGl0aW9ucy5jb20vYXV0aG9yL2FpbmFibGZlZGl0aW9ucy1jb20vKSBwb3VyIHRvbiBhaWRlIGRhbnMgbGEgcsOpYWxpc2F0aW9uIGRlIGNldCBhcnRpY2xlIGV0IGTigJlhdm9pciByYXNzZW1ibMOpIGxlcyBxdWVzdGlvbnMgcG91ciBsYSBGQVEuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiU3TDqXBoYW5lIEthcGl0YW5pdWsiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvc3RlcGhhbmVfa2FwaXRhbml1a19ibG9ndHBzZ19lNmUzMWI1ODRkLmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0RlZmlfZGVfbGVjdHVyZV8yMDI0XzYxMGIwMGY5NjAuanBlZyIsImhlaWdodCI6MTkyMCwid2lkdGgiOjM0MTMsImFsdGVybmF0aXZlVGV4dCI6IiIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IlByb2R1Y3Rpdml0w6kifSx7Im5hbWUiOiJBY3R1YWxpdMOpIHBlcnNvIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJCb25uZSBhbm7DqWUgMjAyNCEgQ+KAmWVzdCAoYmllbnTDtHQpIGxlIGTDqWJ1dCBkZSBs4oCZYW5uw6llLCBldCBj4oCZZXN0IGF1c3NpIGxlIG1vbWVudCBvw7kgdG91dCBsZSBtb25kZSBzZSBmaXhlIGRlIG5vdXZlYXV4IG9iamVjdGlmcyBldCBkZSBub3V2ZWxsZXMgcsOpc29sdXRpb25zLiBDZXR0ZSBhbm7DqWUgZW5jb3JlLCBqJ2FpIGxlIHBsYWlzaXIgZGUgdm91cyBpbnZpdGVyIMOgIHVuIGTDqWZpIHRvdXQgcGFydGljdWxpZXIuIn0se31dfSx7ImlkIjoiMzc0MiIsInRpdGxlIjoiOCBub3V2ZWF1dMOpcyBCTEYgw4lkaXRpb25zIGRlIG5vdmVtYnJlIDIwMjMiLCJzbHVnIjoibm91dmVhdXRlcy1ibGYtbm92ZW1icmUtMjAyMyIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyMy0xMi0wMlQwNTowMDowMC4wMDBaIiwiYm9keSI6IlRvdXMgbGVzIGRlc2NyaXB0aWZzIHF1aSBzdWl2ZW50IHNvbnQgY2V1eCBkZXMgcXVhdHJpw6htZSBkZSBjb3V2ZXJ0dXJlcyBvZmZpY2llbGxlcy5cblxuIyMgMS4gW0NhbGVuZHJpZXIgZGUgbOKAmUF2ZW50XShodHRwczovL2JsZnN0b3JlLmNvbS9jb2xsZWN0aW9ucy9ub3V2ZWF1dGVzLWJsZi1lZGl0aW9ucy1ldC1jcnVjaWZvcm1lLTIwMjMvcHJvZHVjdHMvY2FsZW5kcmllci1kZS1sYXZlbnQpXG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9jYWxlbmRyaWVyX2RlX2xfYXZlbnRfMV9hZTRkMjMxZTc3LmpwZWcpQ2V0dGUgYW5uw6llLCB2aXZvbnMgbGEgam9pZSBldCBs4oCZaW1wYXRpZW5jZSBkZSBs4oCZQXZlbnQhXG5cbkNlIGNhbGVuZHJpZXIgZGUgMjUgbcOpZGl0YXRpb25zIGVzdCBjb27Dp3UgcG91ciB0b3VzIGxlcyDDomdlcy4gUGFydGV6IMOgIGxhIGTDqWNvdXZlcnRlIGRlIErDqXN1czogY2VsdWkgcXVpIHJlbmQgTm/Dq2wgdnJhaW1lbnQgam95ZXV4ISBEw6ljb3V2cmV6IDI1IGZhY2V0dGVzIGRlIERpZXUgZGV2ZW51IGhvbW1lLlxuXG5Kb3lldXggTm/Dq2whXG5cblByb2ZpdGV6IGF1c3NpIGRlIGxvdHMgw6AgZGVzIHByaXggaW50w6lyZXNzYW50cy4gVW5lIHN1cGVyYmUgaWTDqWUgZGUgY2FkZWF1LlxuXG4jIyAyLiAqW05vw6tsIGRhbnMgbGVzIHlldXggZGUgSm9zZXBoXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9ub2VsLWRhbnMtbGVzLXlldXgtZGUtam9zZXBoP3ZhcmlhbnQ9NDcwMzAxNDc4NDIzODgpKlxuXG4gIVtdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvUFNYXzIwMjMxMTA3XzA3NDk0NF8yZjIwMTJiYWNkLmpwZylNYXJpZSwgdm91cyBsYSBjb25uYWlzc2V6LiBNYWlzIEpvc2VwaD8gQ29tbWVudCBjZSBzaW1wbGUgY2hhcnBlbnRpZXIgZGUgQmV0aGzDqWVtIGEtdC1pbCByZcOndSBsYSBtaXNzaW9uIGluc29saXRlIGRlIGRldmVuaXIgbGUgcMOocmUgYWRvcHRpZiBkZeKApiBKw6lzdXMsIGxlIHNhdXZldXIgZHUgbW9uZGU/XG5cbkNldHRlIGFubsOpZSwgcmV2aXNpdGV6IGxhIG5hdGl2aXTDqSFcblxuU3R1cMOpZmFjdGlvbiwgaW5jb21wcsOpaGVuc2lvbiwgY3JhaW50ZSwgam9pZSBldCBhZG9yYXRpb27igKYgcmV2aXZleiBsZXMgw6ltb3Rpb25zIGR1IHByZW1pZXIgTm/Dq2wgYXZlYyBjZSB0w6ltb2luIGRpc2NyZXQuXG5cblwiUHLDqXBhcmV6LXZvdXMgw6AgZMOpY291dnJpciBOb8OrbCBzb3VzIHVuIHRvdXQgbm91dmVsIGFuZ2xlIVwiXG5cbiMjIDMuICpbVW4gTm/Dq2wgdHLDqHMgYnJ1eWFudF0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvdW4tbm9lbC10cmVzLWJydXlhbnQtcmVsaWU/X3Bvcz0xJl9zaWQ9NTEzZjRkMjU5Jl9zcz1yKSpcblxuICFbXShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L1BTWF8yMDIzMTEwN18wNzQzMTFfZjc3ZjE3ZWNhZi5qcGcpU2Fpcy10dSBjcmllciBmb3J0LCB2cmFpbWVudCB0csOocyBmb3J0PyBTYWlzLXR1IGNodWNob3RlciBkb3VjZW1lbnQsIHRyw6hzIGRvdWNlbWVudD8gU2Fpcy10dSBxdWUgbGEgbnVpdCBkZSBsYSBuYWlzc2FuY2UgZGUgSsOpc3VzLCB0b3V0IMOpdGFpdCDDoCBsYSBmb2lzIHNpbGVuY2lldXggZXQgdHLDqHMgYnJ1eWFudD9cblxuQ2V0dGUgbm91dmVsbGUgdmVyc2lvbiBhbXVzYW50ZSBldCBvcmlnaW5hbGUgZGUgbOKAmWhpc3RvaXJlIGRlIE5vw6tsIGVzdCBhY2NvbXBhZ27DqWUgZOKAmWludml0YXRpb25zIMOgIGZhaXJlIGR1IGJydWl0LCBhZmluIHF1ZSBsZXMgZW5mYW50cyBwdWlzc2VudCBzZSBqb2luZHJlIMOgIGxhIGxlY3R1cmUgZGVzIHBhcmVudHMuXG5cbk1haXMgZWxsZSBtb250cmUgYXVzc2kgYXV4IGVuZmFudHMgcXXigJlhdSBjxZN1ciBkZSBs4oCZaGlzdG9pcmUgZGUgTm/Dq2wsIGlsIHkgYSB1bmUgZXhjZWxsZW50ZSBub3V2ZWxsZSBxdWkgbcOpcml0ZSBk4oCZw6p0cmUgY2hhbnTDqWUgZXQgYW5ub25jw6llIHBhcnRvdXQ6IGxlIEZpbHMgZGUgRGlldSwgSsOpc3VzLCBlc3QgbsOpIHBvdXIgcXVlIG5vdXMgcHVpc3Npb25zIMOqdHJlIGFtaXMgYXZlYyBEaWV1IHBvdXIgdG91am91cnMhXG5cbiMjIDQuICpbVW4gTm/Dq2wgdHLDqHMgYnJ1eWFudF0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvdW4tbm9lbC10cmVzLWJydXlhbnQtZnJhbmNhaXMtYW5nbGFpcz9fcG9zPTMmX3NpZD1lZTBmMTgxOGImX3NzPXIpKlsgKEZyYW7Dp2FpcyAtIEFuZ2xhaXMpXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy91bi1ub2VsLXRyZXMtYnJ1eWFudC1mcmFuY2Fpcy1hbmdsYWlzP19wb3M9MyZfc2lkPWVlMGYxODE4YiZfc3M9cilcblxuICFbXShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L1BTWF8yMDIzMTEwN18wNzUyMTNfMzFlZDY0MzI1YS5qcGcpTm91cyBhdm9ucyBzb3J0aSBjZSBsaXZyZSBlbiB2ZXJzaW9uIGJpbGluZ3VlIMOpZ2FsZW1lbnQgcG91ciBsZXMgZW5mYW50cyBkZSBmYW1pbGxlIGJpbGluZ3VlIG91IHRvdXQgc2ltcGxlbWVudCBwb3VyIGNldXggcXVpIHNvdWhhaXRlbnQgYXBwcmVuZHJlIGzigJlhbmdsYWlzLlxuXG4jIyA1LiAqKipbRm9ydHNdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL2ZvcnRzP19wb3M9MSZfc2lkPWQ1NDczMzZjMSZfc3M9cikqKipcblxuICFbXShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L2ZvcnRzXzNlMTA1MzBhYjQuanBlZylBcy10dSBkw6lqw6AgZW50ZW5kdSBwYXJsZXIgZGUgRGlldHJpY2ggQm9uaG9lZmZlcj8gT3UgZGUgRnLDqHJlIEFuZHLDqT8gROKAmUVyaWMgTGlkZGVsbD8gRGUgSm9obiBOZXd0b24gb3UgZGUgSmFja2llIFJvYmluc29uP1xuXG5K4oCZYWltZXJhaXMgcXVlIHR1IHB1aXNzZXMgZmFpcmUgbGV1ciBjb25uYWlzc2FuY2UsIGNhciBjaGFjdW4gZOKAmWVudHJlIGV1eCwgw6Agc2EgbWFuacOocmUsIGEgbWFycXXDqSBs4oCZaGlzdG9pcmUgZW4gcsOpYWxpc2FudCBkZXMgY2hvc2VzIHZyYWltZW50IGV4dHJhb3JkaW5haXJlcy4gQyfDqXRhaWVudCBkZXMgaG9tbWVzIGZvcnRzIHF1aSBvbnQgdGVudSBmZXJtZSBhdSBtaWxpZXUgZGVzIHBpcmVzIGFkdmVyc2l0w6lzIGRlIGxhIHZpZS4gRXQgcG91cnRhbnQsIHR1IHRlIHJlbmRyYXMgdml0ZSBjb21wdGUsIGVuIGxpc2FudCBjZXMgcGFnZXMsIHF14oCZaWxzIMOpdGFpZW50IHRvdXMgZGVzIGdlbnMgdG91dCDDoCBmYWlyZSBvcmRpbmFpcmVzLiBJbHMgw6l0YWllbnQgdnJhaW1lbnQgY29tbWUgdG9pIGV0IG1vaSEgRW4gbGlzYW50IGxldXJzIGhpc3RvaXJlcywgdHUgY29tcHJlbmRyYXMgcXXigJlpbHMgYXZhaWVudCB0b3VzIHJlw6d1IHVuZSBwdWlzc2FuY2UgZXh0cmFvcmRpbmFpcmUgcXVpIGxldXIgYSBwZXJtaXMgZGUgcsOpYWxpc2VyIGRlcyBjaG9zZXMgZm9ybWlkYWJsZXMuIE1haXMgY2V0dGUgcHVpc3NhbmNlIG5lIHZlbmFpdCBwYXMgZOKAmWV1eC1tw6ptZXM6IGVsbGUgdmVuYWl0IGRlIERpZXUuIElscyBzb250IHRvdXMgZGV2ZW51cyDCq0ZvcnRzwrsgZGUgZGlmZsOpcmVudGUgbWFuacOocmUgcGFyY2UgcXVlIERpZXUgbGVzIGF2YWl0IHBhcmZhaXRlbWVudCDDqXF1aXDDqXMgcG91ciBmYWlyZSBjZSBxdeKAmWlscyBkZXZhaWVudCBmYWlyZS5cblxuTGUgRGlldSBxdWkgYSBmb3J0aWZpw6kgV2lsbGlhbSBDYXJleSwgRWxrYSBkZXMgV2Fpd2FpLCBHZW9yZ2UgTGllbGUgZXQgYmllbiBk4oCZYXV0cmVzIGVuY29yZSBwZXV0IHRvaSBhdXNzaSB0ZSByZW5kcmUgZm9ydCBwb3VyIHF1ZSB0dSBwdWlzc2VzLCBmaWTDqGxlbWVudCwgZmFpcmUgY2UgcXVlIERpZXUgYXR0ZW5kIGRlIHRvaSBhdWpvdXJk4oCZaHVpwqAhIFB1aXMsIGRlbWFpbi4gRXQgYXByw6hzLWRlbWFpbi5cblxuSuKAmWVzcMOocmUgcXVlIHR1IHNlcmFzIGVuY291cmFnw6kgZXQgc3RpbXVsw6kgcGFyIGxhIHZpZSBkZSBjZXMgaG9tbWVzLiBNYWlzIGrigJllc3DDqHJlIHN1cnRvdXQgcXVlIHR1IHNlcmFzIGVuY291cmFnw6kgcGFyIGxlIERpZXUgZGUgY2VzIGhvbW1lcy4gSmUgcHJpZSBxdeKAmWVuIGTDqWNvdXZyYW50IGxhIGZpZMOpbGl0w6kgZGUgRGlldSBkYW5zIGxldXIgdmllLCB0dSBzb2lzIGZvcnRpZmnDqSwgc2FjaGFudCBxdeKAmWlsIHRyYXZhaWxsZSBhdXNzaSBmaWTDqGxlbWVudCBkYW5zIGxhIHRpZW5uZS5cblxuIyMgNi4gKioqW0NvdXJhZ2V1c2VzXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9jb3VyYWdldXNlcz92YXJpYW50PTQ2NTIwMzY1ODEwMDA0KSoqKlxuXG4gIVtdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvSU1HXzk3MzJfNDQzOWM2ZDhjNC5KUEcpQXMtdHUgZMOpasOgIGVudGVuZHUgcGFybGVyIGRlIENvcnJpZSB0ZW4gQm9vbT8gT3UgZOKAmUVsaXNhYmV0aCBFbGxpb3Q/IERlIEpvbmkgRWFyZWNrc29uIFRhZGE/IERlIEZhbm55IENyb3NieSBvdSBkZSBQYW5kaXRhIFJhbWFiYWk/XG5cbkrigJlhaW1lcmFpcyBxdWUgdHUgcHVpc3NlcyBmYWlyZSBsZXVyIGNvbm5haXNzYW5jZSwgY2FyIGNoYWN1bmUgZOKAmWVudHJlIGVsbGVzLCDDoCBzYSBtYW5pw6hyZSwgYSBtYXJxdcOpIGzigJloaXN0b2lyZSBlbiByw6lhbGlzYW50IGRlcyBjaG9zZXMgdnJhaW1lbnQgZXh0cmFvcmRpbmFpcmVzLiBDJ8OpdGFpZW50IGRlcyBmZW1tZXMgY291cmFnZXVzZXMgcXVpIG9udCB0ZW51IGZlcm1lIGF1IG1pbGlldSBkZXMgcGlyZXMgYWR2ZXJzaXTDqXMgZGUgbGEgdmllLiBFdCBwb3VydGFudCwgdHUgdGUgcmVuZHJhcyB2aXRlIGNvbXB0ZSwgZW4gbGlzYW50IGNlcyBwYWdlcywgcXXigJllbGxlcyDDqXRhaWVudCB0b3V0ZXMgZGVzIHBlcnNvbm5lcyB0b3V0IMOgIGZhaXJlIG9yZGluYWlyZXMuIEVsbGVzIMOpdGFpZW50IHZyYWltZW50IGNvbW1lIHRvaSBldCBtb2khIEVuIGxpc2FudCBsZXVycyBoaXN0b2lyZXMsIHR1IGNvbXByZW5kcmFzIHF14oCZZWxsZXMgYXZhaWVudCB0b3V0ZXMgcmXDp3UgdW5lIHB1aXNzYW5jZSBleHRyYW9yZGluYWlyZSBxdWkgbGV1ciBhIHBlcm1pcyBkZSByw6lhbGlzZXIgZGVzIGNob3NlcyBmb3JtaWRhYmxlcy4gTWFpcyBjZXR0ZSBwdWlzc2FuY2UgbmUgdmVuYWl0IHBhcyBk4oCZZWxsZXMtbcOqbWVzOiBlbGxlIHZlbmFpdCBkZSBEaWV1LiBFbGxlcyBzb250IHRvdXRlcyBkZXZlbnVlcyDigJxDb3VyYWdldXNlc+KAnSBkZSBkaWZmw6lyZW50ZSBtYW5pw6hyZSBwYXJjZSBxdWUgRGlldSBsZXMgYXZhaXQgcGFyZmFpdGVtZW50IMOpcXVpcMOpZXMgcG91ciBmYWlyZSBjZSBxdeKAmWlscyBkZXZhaWVudCBmYWlyZS5cblxuTGUgRGlldSBxdWkgYSBmb3J0aWZpw6kgQmV0c2llIHRlbiBCb29tLCBQaGlsbGlzIFdoZWF0bGV5LCBTb3BoaWUgU2Nob2xsIGV0IGJpZW4gZOKAmWF1dHJlcyBlbmNvcmUgcGV1dCB0b2kgYXVzc2kgdGUgcmVuZHJlIGZvcnQgZXQgY291cmFnZXV4IHBvdXIgcXVlIHR1IHB1aXNzZXMsIGZpZMOobGVtZW50LCBmYWlyZSBjZSBxdWUgRGlldSBhdHRlbmQgZGUgdG9pIGF1am91cmTigJlodWkhIFB1aXMsIGRlbWFpbi4gRXQgYXByw6hzLWRlbWFpbi5cblxuSuKAmWVzcMOocmUgcXVlIHR1IHNlcmFzIGVuY291cmFnw6kgZXQgc3RpbXVsw6kgcGFyIGxhIHZpZSBkZSBjZXMgZmVtbWVzLiBNYWlzIGrigJllc3DDqHJlIHN1cnRvdXQgcXVlIHR1IHNlcmFzIGVuY291cmFnw6kgcGFyIGxlIERpZXUgZGUgY2VzIGZlbW1lcy4gSmUgcHJpZSBxdeKAmWVuIGTDqWNvdXZyYW50IGxhIGZpZMOpbGl0w6kgZGUgRGlldSBkYW5zIGxldXIgdmllLCB0dSBzb2lzIGZvcnRpZmnDqSwgc2FjaGFudCBxdeKAmWlsIHRyYXZhaWxsZSBhdXNzaSBmaWTDqGxlbWVudCBkYW5zIGxhIHRpZW5uZS5cblxuIyMgKio3LiAqW1Ryb2lzIGhvbW1lcyBldCB1bmUgY291cm9ubmVdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzMtaG9tbWVzLWV0LXVuZS1jb3Vyb25uZT9fcG9zPTImX3NpZD02OGJlZjQ5NmImX3NzPXIpKioqXG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9JTUdfOTcxNF9lYTQ5Mzc2NDlkLkpQRylMZSByaWRlYXUgc2UgbMOodmU7IHRyb2lzIGhvbW1lcyBlbnRyZW50IGVuIHNjw6huZS4gUXVlbCByw7RsZSBjaG9pc2lyb250LWlscz8gQ2VsdWkgZHUgcm9pIFNhw7xsLCBs4oCZb2ludCBkZSBEaWV1IHF1aSBkZXZpZW5kcmEgZm91LCBldCBkb250IGxlIHBvdXZvaXIgZMOpdm9pbGVyYSBsZXMgZMOpdG91cnMgZHUgY8WTdXI/IENlbHVpIGRlIERhdmlkLCBsZSBiZXJnZXIgYXUgY8WTdXIgYnJpc8OpLCBxdWUgbGEgc291ZmZyYW5jZSBjb25kdWlyYSwgbWFsZ3LDqSBsdWksIHZlcnMgbGEgcm95YXV0w6k/IE91IGNlbHVpIGTigJlBYnNhbG9tLCBsZSBqZXVuZSByZWJlbGxlIG5vYmxlIGV0IGJlYXUsIGTDqXNpcmV1eCBkZSByw6lwYXJlciBsZXMgaW5qdXN0aWNlcywgbWFpcyBzZWNyw6h0ZW1lbnQgYXNzb2lmZsOpIGRlIHBvdXZvaXI/IE1haXMgY2h1dCEgTOKAmWhpc3RvaXJlIGNvbW1lbmNl4oCmXG5cbj4gQ2V0dGUgaGlzdG9pcmUgZXN0IHVuIHBvcnRyYWl0IG91LCBzaSB2b3VzIHByw6lmw6lyZXosIHVuZSBlc3F1aXNzZSBhdSBmdXNhaW4gZGUgbGEgc291bWlzc2lvbiBldCBkZSBs4oCZYXV0b3JpdMOpIGRhbnMgbGUgUm95YXVtZSBkZSBEaWV1LiBHLkUuXG5cblBvdXIgaW5mbywgQ2hyaXN0aWFuIEJsYW5jIMOgIHNpZ27DqSBsYSBwcsOpZmFjZSBkdSBsaXZyZS4gSuKAmWFpIMOpY3JpdCB1bmUgcG9zdGZhY2UgcMOpZGFnb2dpcXVlIHBvdXIgYWlkZXIgbGVzIGxlYWRlcnMgw6Agc+KAmWFwcHJvcHJpZXIgbGUgbWVzc2FnZSBkdSBsaXZyZS5cblxuIyMgOC4gKipbTGEgQmlibGUgZW4gY2FybmV0c10oaHR0cHM6Ly93d3cuaGVsbG9hc3NvLmNvbS9hc3NvY2lhdGlvbnMvYmxmLWVkaXRpb25zL2NvbGxlY3Rlcy9iaWJsZS1lbi1jYXJuZXRzLW5vdXZlYXUtdGVzdGFtZW50KSoqXG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9JTUdfOTcyNF8wYzc2ZTE0ODI4LkpQRylJbCB5IGEgMiBzZW1haW5lcywgbm91cyBhdm9ucyBsYW5jw6kgZW4gZmluYW5jZW1lbnQgcGFydGljaXBhdGlmIHVuZSDDqWRpdGlvbiBzcMOpY2lhbGUgZGUgbGEgQmlibGUgc3DDqWNpYWxlbWVudCBwb3VyIGxhIHByaXNlIGRlIG5vdGVzLlxuXG5D4oCZZXN0IFtMYSBCaWJsZSBlbiBjYXJuZXRzOiBOb3V2ZWF1IFRlc3RhbWVudF0oaHR0cHM6Ly93d3cuaGVsbG9hc3NvLmNvbS9hc3NvY2lhdGlvbnMvYmxmLWVkaXRpb25zL2NvbGxlY3Rlcy9iaWJsZS1lbi1jYXJuZXRzLW5vdXZlYXUtdGVzdGFtZW50KS4gQ+KAmWVzdCB1biBjb2ZmcmV0IHF1aSBjb250aWVudCAxOSBjYXJuZXRzICgxIGNhcm5ldCBwb3VyIGNoYXF1ZSBsaXZyZSBiaWJsaXF1ZSkuIE5vdHJlIGJ1dCBlc3QgZGUgY3LDqWVyIHVuZSDDqWRpdGlvbiBkZSBsYSBCaWJsZSBmYWNpbGUgw6AgZW1wb3J0ZXIgZXQgcGFyZmFpdGUgcG91ciBwcmVuZHJlIGRlcyBub3Rlcy5cblxuVm9pY2kgdW5lIHZpZMOpbyBxdWkgdm91cyBwcsOpc2VudGUgY2UgcHJvamV0OlxuXG5gPGlmcmFtZSB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9XCI0MDVcIiBzcmM9XCJodHRwczovL3d3dy55b3V0dWJlLmNvbS9lbWJlZC9XbjNYQ2lXX29WND9zaT0wZTVGUlZrbXpFZWFCSXZfXCIgdGl0bGU9XCJZb3VUdWJlIHZpZGVvIHBsYXllclwiIGZyYW1lYm9yZGVyPVwiMFwiIGFsbG93PVwiYWNjZWxlcm9tZXRlcjsgYXV0b3BsYXk7IGNsaXBib2FyZC13cml0ZTsgZW5jcnlwdGVkLW1lZGlhOyBneXJvc2NvcGU7IHBpY3R1cmUtaW4tcGljdHVyZTsgd2ViLXNoYXJlXCIgYWxsb3dmdWxsc2NyZWVuPjwvaWZyYW1lPmBcblxuQWlkZXotbm91cyDDoCBmaW5hbmNlciBjZXR0ZSBCaWJsZSBlbiBmYWlzYW50IHVuIGRvbiBvdSBlbiBwcsOpY29tbWFuZGFudCBbdW4gY29mZnJldCBpY2kuXShodHRwczovL3d3dy5oZWxsb2Fzc28uY29tL2Fzc29jaWF0aW9ucy9ibGYtZWRpdGlvbnMvY29sbGVjdGVzL2JpYmxlLWVuLWNhcm5ldHMtbm91dmVhdS10ZXN0YW1lbnQpIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiU3TDqXBoYW5lIEthcGl0YW5pdWsiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvc3RlcGhhbmVfa2FwaXRhbml1a19ibG9ndHBzZ19lNmUzMWI1ODRkLmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0lNR185NzQwXzNkMTVmYzc4OWMuSlBHIiwiaGVpZ2h0Ijo0MDAwLCJ3aWR0aCI6NjAwMCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiUmVjZW5zaW9uIGRlIGxpdnJlIn0seyJuYW1lIjoiQWN0dWFsaXTDqSBwZXJzbyJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiRW4gY2UgZGV1eGnDqG1lIGpvdXIgZGUgbOKAmUF2ZW50LCBq4oCZYWkgbGUgcGxhaXNpciBkZSB2b3VzIHByw6lzZW50ZXIgbGVzIDggbm91dmVhdXTDqXMgZGUgQkxGIMOJZGl0aW9ucyBwb3VyIE5vw6tsLiBVbmUgZGVzIG5vdXZlYXV0w6lzIGVzdCBqdXN0ZW1lbnQgdW4gdHLDqHMgYmVhdSBjYWxlbmRyaWVyIGRlIGzigJlBdmVudCBjb27Dp3UgcG91ciBsZXMgZmFtaWxsZXMuIExhIGh1aXRpw6htZSBub3V2ZWF1dMOpIGVzdCB1biBpbnRydXMuIEVsbGUgbuKAmWV4aXN0ZXJhIHBldXQtw6p0cmUgamFtYWlzLiBTYXVmIHNpIGFzc2V6IGRlIG1vbmRlIHkgY3JvaXQgZXQgc291dGllbnQgbGUgcHJvamV0LiBKZSB2b3VzIHByw6lzZW50ZSBsZXMgbm91dmVhdXTDqXMgZGUgQkxGIMOJZGl0aW9uczoifV19LHsiaWQiOiIzNjg4IiwidGl0bGUiOiJQZW5zw6llcyBzdXIgbGUgbGVhZGVyc2hpcCBjaHLDqXRpZW4gcG91ciBsYSByw6nDqWRpdGlvbiBkJ3VuIGNsYXNzaXF1ZSIsInNsdWciOiJwZW5zZWVzLXN1ci1sZS1sZWFkZXJzaGlwLWNocmV0aWVuIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDIzLTEwLTI4VDA0OjMwOjAwLjAwMFoiLCJib2R5IjoiUXXigJllc3QtY2UgcXVlIGrigJlhdXJhaXMgYWltw6kgbGlyZSBjZSBsaXZyZSBwbHVzIHTDtHQgZGFucyBtb24gbWluaXN0w6hyZSEgSWwgbeKAmWF1cmFpdCDDqXTDqSB1biBjb21wYWdub24gdHLDqHMgdXRpbGUuXG5cbmA8cD5Wb3RyZSBwcm9wcmUgbGVjdHVyZSBkZSA8YSBocmVmPVwiaHR0cHM6Ly9ibGZzdG9yZS5jb20vY29sbGVjdGlvbnMvcHJlY29tbWFuZGVzL3Byb2R1Y3RzLzMtaG9tbWVzLWV0LXVuZS1jb3Vyb25uZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+VHJvaXMgaG9tbWVzIGV0IHVuZSBjb3Vyb25uZTwvZW0+PC9hPiBhIGNlcnRhaW5lbWVudCBkw6lqw6AgZmFpdCByZW1vbnRlciDDoCBsYSBzdXJmYWNlIGRlcyBzb3V2ZW5pcnMgZGUgdm90cmUgdmllIGRlIGxlYWRlci4gUGV1dC3DqnRyZSBhLXQtZWxsZSBtw6ptZSBtaXMgbGUgZG9pZ3Qgc3VyIGRlcyDDqXBpc29kZXMgZG91bG91cmV1eCBkYW5zIGxhIHZpZSBkZSB2b3RyZSDDiWdsaXNlIG91IG9yZ2FuaXNhdGlvbi4gVm91cyBu4oCZw6p0ZXMgcGV1dC3DqnRyZSBwbHVzIGF1c3NpIHPDu3IgZOKAmWF2b2lyIGZhaXQgY2UgcXVpIMOpdGFpdCBqdXN0ZSDDoCBjZXMgbW9tZW50cy1sw6AuIEF2ZXotdm91cyB2w6lyaXRhYmxlbWVudCBhZ2kgY29tbWUgdW4gRGF2aWQ/IE91IMOpdGllei12b3VzIHBsdXTDtHQgY29tbWUgdW4gU2HDvGw/PC9wPmBcblxuYDxwPkNlIHNvbnQgZGUgYm9ubmVzIHF1ZXN0aW9ucyEgRGFucyBjZXMgcXVlbHF1ZXMgZGVybmnDqHJlcyBwYWdlcywgauKAmWFpbWVyYWlzIHZvdXMgYWlkZXIgw6AgeSB2b2lyIGVuY29yZSB1biBwZXUgcGx1cyBjbGFpci4gT24gbeKAmWEgcmVjb21tYW5kw6kgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL2NvbGxlY3Rpb25zL3ByZWNvbW1hbmRlcy9wcm9kdWN0cy8zLWhvbW1lcy1ldC11bmUtY291cm9ubmVcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+PGVtPlRyb2lzIGhvbW1lcyBldCB1bmUgY291cm9ubmU8L2VtPjwvYT4gw6AgcGx1c2lldXJzIHJlcHJpc2VzIHF1YW5kIGrigJlhaSBkw6ltYXJyw6kgbGUgbWluaXN0w6hyZS4gw4AgbOKAmcOpcG9xdWUsIGplIG7igJnDqXRhaXMgcGFzIHRyw6hzIGludMOpcmVzc8OpLiBMZSBnZW5yZSBsaXR0w6lyYWlyZSBt4oCZYXZhaXQgZMOpcm91dMOpIGV0IGplIG7igJlhdmFpcyBwYXMgbHUgcGx1cyBsb2luIHF1ZSBsZXMgcHJlbWnDqHJlcyBwYWdlcy4gUXVlbCBkb21tYWdlISBDYXIgcXVhbmQgamUgbOKAmWFpIGVuZmluIGx1LCBpbCBt4oCZYSBmcmFwcMOpLiBWb2ljaSB1biB0b3V0IHBldGl0IGxpdnJlIHRyw6hzIMOpbG9xdWVudCBldCBtw6ltb3JhYmxlISBRdWVsIGJlYXUgcGxhaWRveWVyIHBvdXIgZGVzIHBhc3RldXJzIGV0IGRlcyBsZWFkZXJzIGNocsOpdGllbnMgaHVtYmxlcyBldCBzb3VtaXMgw6AgRGlldS48L3A+YFxuXG4jIyAzIGRpZmZpY3VsdMOpcyBk4oCZYXBwbGljYXRpb25cblxuVm91cyBs4oCZYXZleiBwZXV0LcOqdHJlIHJlc3NlbnRpIGxvcnMgZGUgdm90cmUgbGVjdHVyZTogbGVzIGNob2l4IGxpdHTDqXJhaXJlcyBkZSBHZW5lIEVkd2FyZHMgcmVuZGVudCBs4oCZYXBwbGljYXRpb24gw6Agbm90cmUgdmllIHVuIHBldSBjb21wbGlxdcOpZS5cblxuUHJlbWnDqHJlbWVudCwgbGUgZ2VucmUgbGl0dMOpcmFpcmUgZXN0IHVuIG3DqWxhbmdlIGRlIHRow6nDonRyZSwgZGUgZmljdGlvbiBldCBk4oCZYWxsw6lnb3JpZSBjaHLDqXRpZW5uZS4gQ2UgY2hvaXggcHJvZHVpdCB1biBzdHlsZSBwdWlzc2FudCBldCBlZmZpY2FjZSwgbWFpcyBham91dGUgZGVzIMOpbMOpbWVudHMgY29tcGzDqHRlbWVudCBpbnZlbnTDqXMgw6AgZGVzIGZhaXRzIGhpc3RvcmlxdWVzIGV0IGJpYmxpcXVlcy4gTm91cyBub3VzIHJldHJvdXZvbnMgcGFyZm9pcyB1biBwZXUgZMOpYm91c3NvbMOpcyBldCBub3VzIGRldm9ucyBiaWVuIGNvbm5hw650cmUgbm90cmUgQmlibGUgcG91ciBub3VzIHkgcmV0cm91dmVyLlxuXG5EZXV4acOobWVtZW50LCBsZSBjaG9peCBk4oCZYXZvaXIgdW4gbmFycmF0ZXVyIG9tbmlzY2llbnQgZXN0IGV4Y2VsbGVudCBww6lkYWdvZ2lxdWVtZW50LiBNYWlzIGNlIG5hcnJhdGV1ciwgc2FjaGFudCBcInRvdXRcIiwgZmFpdCBxdWVscXVlcyBkw6ljbGFyYXRpb25zIHRyb3AgZm9ydGVzIHN1ciBjZSBxdWUgRGlldSBuZSBmYWl0IGphbWFpcyBvdSBjZSBxdeKAmWlsIGZhaXQgdG91am91cnMuIENlbGEgYXVnbWVudGUgbOKAmWVmZmljYWNpdMOpIGRlIGxhIHByb3NlLCBtYWlzIGRpbWludWUgbGVzIGJpZW5mYWl0cyBkZSBs4oCZZW5zZWlnbmVtZW50IGVuIGxhaXNzYW50IGRlcyBpbXByw6ljaXNpb25zIHNlIGdsaXNzZXIgZGFucyBsZSByw6ljaXQuXG5cbmA8cD5Ucm9pc2nDqG1lbWVudCwgaWwgeSBhIHVuIGZvc3PDqSBkaWZmaWNpbGUgw6AgZnJhbmNoaXIgZW50cmUgbm90cmUgbGVhZGVyc2hpcCBldCBjZWx1aSBkZSBjZXMgdHJvaXMgcm9pcyBlbiBJc3Jhw6tsLiBCaWVuIHPDu3IsIG5vdXMgc2F2b25zIHF1ZSBub3VzIG5lIHNvbW1lcyBwYXMgZGVzIHJvaXMsIG1haXMganVzcXXigJlvw7kgYXZvbnMtbm91cyBsZSBkcm9pdCBkZSBmYWlyZSBkZXMgcGFyYWxsw6hsZXMgdXRpbGVzIHNhbnMgw6p0cmUgZW4gdHJhaW4gZGUgZMOpcGFzc2VyIGNlcnRhaW5lcyBsaW1pdGVzPyBOb3VzIHkgcmV2aWVuZHJvbnMuIENlcyB0cm9pcyBjaG9peCBkZSBHZW5lIEVkd2FyZHMgbmUgc29udCBwYXMgZGUgbWF1dmFpcyBjaG9peCwgbWFpcyBpbHMgcmVuZGVudCBs4oCZYXBwcm9wcmlhdGlvbiBkZSBs4oCZZW5zZWlnbmVtZW50IHVuIHBldSBww6lyaWxsZXV4LiBKZSB2b3VzIHByb3Bvc2UgZG9uYyBkZSB0ZXJtaW5lciA8YSBocmVmPVwiaHR0cHM6Ly9ibGZzdG9yZS5jb20vY29sbGVjdGlvbnMvcHJlY29tbWFuZGVzL3Byb2R1Y3RzLzMtaG9tbWVzLWV0LXVuZS1jb3Vyb25uZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+VHJvaXMgaG9tbWVzIGV0IHVuZSBjb3Vyb25uZTwvZW0+PC9hPiBwYXIgdHJvaXMgcmFwcGVscyBiaWJsaXF1ZXMgZXNzZW50aWVscyBwb3VyIGxlcyBsZWFkZXJzLiBJbHMgbm91cyBhaWRlcm9udCDDoCBub3VzIGFwcHJvcHJpZXIgbGVzIGVuc2VpZ25lbWVudHMgZHUgbGl2cmUuPC9wPmBcblxuIyMjIDEuIE91aSBhdSBicmlzZW1lbnQsIG1haXMgcGFzIG7igJlpbXBvcnRlIGxlcXVlbFxuXG5EYW5zIG5vcyBtaWxpZXV4IMOpdmFuZ8OpbGlxdWVzLCBvbiBlbnRlbmQgc291dmVudCBkZXMgZXhob3J0YXRpb25zIHF1aSBmcsO0bGVudCBsZSBmYXRhbGlzbWU6IOKAnFByaW9ucyBldCBsYWlzc29ucyBEaWV1IGFnaXIh4oCdIENlbGEgc2VtYmxlIHBpZXV4LCBtYWlzIGNlIG7igJllc3QgcGFzIGJpYmxpcXVlLlxuXG5JbCBlc3QgZXNzZW50aWVsIGRlIG5lIHBhcyBjb25mb25kcmUgYnJpc2VtZW50IGV0IGZhdGFsaXNtZS4gTm91cyByaXNxdWVyaW9ucyBk4oCZYWJhbmRvbm5lciBsZSB0cm91cGVhdSBhdXggbG91cHMsIHRvdXQgZW4gY3JveWFudCBvYsOpaXIgw6AgRGlldSEgTGEgQmlibGUgZW5zZWlnbmUgdW5lIHNvdW1pc3Npb24gY29uZmlhbnRlIMOgIGxhIHNvdXZlcmFpbmV0w6kgZGUgbm90cmUgRGlldSwgbWFpcyBjZXR0ZSBzb3VtaXNzaW9uIG7igJllc3QgcGFzIGR1IGZhdGFsaXNtZS4gQmVhdWNvdXAgZGUgcGVyc29ubmVzIHJlbGlnaWV1c2VzIHBldXZlbnQgc2UgcsOpc2lnbmVyIGF2ZWMgZmF0YWxpc21lIMOgIHF1ZWxxdWUgY2hvc2UsIHNhbnMgcG91ciBhdXRhbnQgc2UgbGFpc3NlciBicmlzZXIuIEVsbGVzIHNvbnQgcsOpc2lnbsOpZXMsIG1haXMgcGFzIGNvbmZpYW50ZXMuIFLDqXNpZ27DqWVzLCBtYWlzIHBhcyBodW1ibGVtZW50IHNvdW1pc2VzIMOgIERpZXUuIExvcnNxdWUgbm91cyBhY2NlcHRvbnMgdW5lIGNob3NlIGRpZmZpY2lsZSBjb21tZSB2ZW5hbnQgZGUgbGEgbWFpbiBkZSBEaWV1LCBj4oCZZXN0IGFsb3JzIHF1ZSBub3VzIG5vdXMgbGFpc3NvbnMgYnJpc2VyLiBOb3VzIG5vdXMgc291bWV0dG9ucyDDoCBEaWV1LCBsdWkgZGlzYW50OiDigJxOb24gbWEgdm9sb250w6ksIG1haXMgbGEgdGllbm5lLuKAnSBNYWlzIGNlIG7igJllc3QgcGFzIGR1IGZhdGFsaXNtZS4gQ+KAmWVzdCB1biBhYmFuZG9uIGNvbmZpYW50LiBOb3VzIHNhdm9ucyBxdWUgRGlldSByw6hnbmUsIG5vdXMgYWltZSBldCBmZXJhIGxlIG1pZXV4IHBvdXIgc2EgZ2xvaXJlIGV0IHBvdXIgc29uIMOJZ2xpc2UuXG5cbkxhIHZpc2lvbiBiaWJsaXF1ZSBkdSBsZWFkZXJzaGlwIGV4aWdlIHF1ZSBub3VzIHByZW5pb25zIHVuIHLDtGxlIGFjdGlmLCB0b3V0IGVuIMOpdGFudCBwbGVpbmVtZW50IGNvbnNjaWVudHMgcXVlIG5vdHJlIHRyYXZhaWwgZXN0IHZhaW4gc2FucyBs4oCZYWN0aW9uIHB1aXNzYW50ZSBldCBzb3V2ZXJhaW5lIGRlIERpZXUuIFNpIG5vdXMgc29tbWVzIHBhc3RldXJzLCBub3VzIGRldm9ucyBkb25jIGFnaXIgaHVtYmxlbWVudCwgZGFucyBsYSBwcmnDqHJlIGV0IGxhIHNvdW1pc3Npb24gw6AgRGlldSwgcG91ciBsZSBiaWVuIGR1IHRyb3VwZWF1IOKAnHF1ZSBsZSBTYWludC1Fc3ByaXQgYSBjb25macOpIMOgIFxcW25vdHJlXFxdIGdhcmRl4oCdIChBY3RlcyAyMC4yOCkuIElsIG5lIHPigJlhZ2l0IHBhcyBkZSBqdXN0ZSBcInByaWVyIGV0IGxhaXNzZXIgRGlldSBhZ2lyXCIuIEFkb3B0b25zIHBsdXTDtHQgbOKAmWF0dGl0dWRlIGRlcyBiw6J0aXNzZXVycyBldCBkZXMgZ2FyZGVzIGR1IGPDqWzDqGJyZSBwc2F1bWUgMTI3LiBMZXMgYsOidGlzc2V1cnMgYsOidGlzc2VudCBlbiB2YWluIHNpIERpZXUgbmUgYsOidGl0IHBhcyBsYSBtYWlzb24sIG1haXMgaWxzIGLDonRpc3NlbnQgcXVhbmQgbcOqbWUhIExhIG1haXNvbiBuZSBzZSBiw6J0aXQgcGFzIHRvdXRlIHNldWxlLiBMZXMgZ2FyZGVzIHZlaWxsZW50IGVuIHZhaW4gc2kgRGlldSBuZSBnYXJkZSBwYXMgbGEgdmlsbGUuIE1haXMgaWxzIG1vbnRlbnQgcXVhbmQgbcOqbWUgbGEgZ2FyZGUhIElscyBuZSBsYWlzc2VudCBwYXMgbGVzIHBvcnRlcyBvdXZlcnRlcywgbcOqbWUgc2kgY+KAmWVzdCBEaWV1IGdhcmRlIGxhIHZpbGxlLiBEZSBtw6ptZSwgaW1pdG9ucyBs4oCZYXR0aXR1ZGUgZGUgSm9hYiBxdWkgw6l0YWJsaXQgdW5lIHN0cmF0w6lnaWUgZGUgZ3VlcnJlIGNvbnRyZSBsZXMgQW1tb25pdGVzLCBwdWlzIGRpdCDDoCBzb24gZnLDqHJlIEFiaWNoYcOvOiDigJxTb2lzIGZvcnQsIGZvcnRpZmlvbnMtbm91cyBwb3VyIG5vdHJlIHBldXBsZSBldCBwb3VyIGxlcyB2aWxsZXMgZGUgbm90cmUgRGlldSwgZXQgcXVlIGzigJnDiXRlcm5lbCBmYXNzZSBjZSBxdWkgbHVpIHNlbWJsZXJhIGJvbiHigJ0gKDIgU2FtdWVsIDEwLjEyIOKAkyBDT0wpLiBSaWVuIG7igJlpbmRpcXVlIGRhbnMgY2UgcGFzc2FnZSBxdWUgSm9hYiBtZXQgc2EgY29uZmlhbmNlIGRhbnMgc2Egc3RyYXTDqWdpZSBvdSBkYW5zIHNlcyBmb3JjZXMgYXJtw6llcy4gSWwgcGxhbmlmaWUsIGV4aG9ydGUgc29uIGZyw6hyZSDDoCDDqnRyZSBmb3J0LCBwdWlzIHPigJllbiByZW1ldCDDoCBEaWV1LiBWb2lsw6Agw6AgcXVvaSByZXNzZW1ibGUgdW5lIGh1bWJsZSBzb3VtaXNzaW9uLlxuXG5BZ2lzc29ucyBhdmVjIGZlcm1ldMOpLCBhdmVjIGZvaSwgZGFucyBsYSBwcmnDqHJlLCBwdWlzIGRpc29uczog4oCcUXVlIGzigJnDiXRlcm5lbCBmYXNzZSBjZSBxdWkgbHVpIHNlbWJsZXJhIGJvbiHigJ0gVm9pbMOgIGxhIHNvdW1pc3Npb24gcXVpIGdsb3JpZmllIERpZXUuIFZvaWzDoCBsZSBzZXVsIGJyaXNlbWVudCBxdWkgaG9ub3JlIGxlIFNlaWduZXVyLiBVbiBicmlzZW1lbnQgaHVtYmxlLCBtYWlzIHF1aSBhZ2l0LCBjb25maWFudCBxdWUg4oCcbm90cmUgRGlldSBlc3QgYXUgY2llbCwgaWwgZmFpdCB0b3V0IGNlIHF14oCZaWwgdmV1dOKAnSAoUHNhdW1lcyAxMTUuMykuXG5cbiMjIyAyLiBBdWN1biBk4oCZZW50cmUgbm91cyBu4oCZZXN0IGzigJlvaW50IGR1IFNlaWduZXVyXG5cbklsIHkgYSB1biB2cmFpIGZvc3PDqSBlbnRyZSBsYSBzaXR1YXRpb24gZGVzIHJvaXMgU2HDvGwgZXQgRGF2aWQgZXQgbGEgbsO0dHJlIGF1am91cmTigJlodWksIHF1ZSBub3VzIHNveW9ucyBwYXN0ZXVycywgYW5jaWVucywgZGlhY3JlcyBvdSBkaXJpZ2VhbnRzIGTigJlhc3NvY2lhdGlvbiwgb3UgZOKAmWVudHJlcHJpc2UuIFRvdXQgZOKAmWFib3JkLCDDqnRyZSBwYXN0ZXVyIGTigJl1bmUgw4lnbGlzZSwgY+KAmWVzdCB1biByw7RsZSBkZSBiZXJnZXIsIHBhcyBjZWx1aSBk4oCZdW4gcm9pLiDDinRyZSBsZWFkZXIgZOKAmXVuZSBlbnRyZXByaXNlIG91IGTigJl1bmUgYXNzb2NpYXRpb24sIGPigJllc3QgdW4gcsO0bGUgZOKAmWludGVuZGFudCwgY2VydGFpbmVtZW50IHBhcyBjZWx1aSBk4oCZdW4gcm9pLiBNYWlzIGJpZW4gcGx1cyBpbXBvcnRhbnQsIERpZXUgYSBmYWl0IGRlcyBwcm9tZXNzZXMgYXUgcm9pIERhdmlkIHF1aSBuZSBz4oCZYXBwbGlxdWVudCDDoCBhdWN1biBk4oCZZW50cmUgbm91cy4gRGlldSBhIGRpdCDDoCBEYXZpZCBxdeKAmWlsIMOpdGFibGlyYWl0IHNhIGZhbWlsbGUgcG91ciBxdeKAmWVsbGUgcsOoZ25lIMOpdGVybmVsbGVtZW50IHN1ciBJc3Jhw6tsICgyIFNhbXVlbCA3KS4gRGUgY2V0dGUgZHluYXN0aWUgZGV2YWl0IG5hw650cmUgbGUgTWVzc2llLlxuXG5EYW5zIEFjdGVzIDIsIGzigJlhcMO0dHJlIFBpZXJyZSBpbmRpcXVlIHF1ZSBEYXZpZCBhdmFpdCBiaWVuIGNvbXByaXMgY2VsYSBsb3JzcXXigJlpbCDDqWNyaXQgbGUgcHNhdW1lIDE2IHNvdXMgbOKAmWluc3BpcmF0aW9uIGR1IFNhaW50LUVzcHJpdCBldCBwcm9waMOpdGlzZSBxdeKAmXVuIGRlIHNlcyBkZXNjZW5kYW50cyBuZSB2ZXJyYWl0IHBhcyBsYSBjb3JydXB0aW9uIGV0IHLDqGduZXJhaXQgw6l0ZXJuZWxsZW1lbnQuIEF1Y3VuIGNocsOpdGllbiBuaSBhdWN1biBsZWFkZXIgbuKAmWEgcmXDp3UgZGUgcHJvbWVzc2VzIGF1c3NpIGZvcnRlcyBldCBhdXNzaSBjbGFpcmVzLiBPdWksIG5vdXMgYXZvbnMgcmXDp3UgbGUgU2FpbnQtRXNwcml0LCBldCBjZXJ0YWlucyBwYXJsZW50IGTigJlvbmN0aW9uLiBNYWlzIERhdmlkIMOpdGFpdCBjaG9pc2kgcGFyIERpZXUgcG91ciDDqnRyZSBsZSBww6hyZSBkdSBNZXNzaWUuIExlIHJvaSBEYXZpZCBwcsOpZmlndXJlIGxlIFNlaWduZXVyIErDqXN1cy1DaHJpc3QsIGxlIFJvaSDDqXRlcm5lbCBkZSBs4oCZdW5pdmVycy4gTm9zIG1pbmlzdMOocmVzIHNvbnQgdGVtcG9yYWlyZXMuIE5lIG5vdXMgcHJlbm9ucyBwYXMgcG91ciBsZSBNZXNzaWUgZXQgw6l2aXRvbnMgZGVzIHRlcm1lcyBjb21tZSBcImhvbW1lIGRlIERpZXVcIiBvdSBcIm9pbnQgZGUgRGlldVwiLlxuXG4jIyMgMy4gTm91cyBzb21tZXMgYXZhbnQgdG91dCBkZXMgc2Vydml0ZXVycyBoZXVyZXV4XG5cbkNlIHRyb2lzacOobWUgcG9pbnQgZXN0LCBlbiBxdWVscXVlIHNvcnRlLCBs4oCZYW50aWRvdGUgZGVzIGRldXggcHJlbWllcnMuIE5vdXMgdHLDqWJ1Y2hvbnMgdG91cyBkZSBkaXZlcnNlcyBtYW5pw6hyZXMuIFNpIHZvdXMgw6p0ZXMgdGVudMOpIHBhciBsZSBmYXRhbGlzbWUgb3UgbGEgcGFzc2l2aXTDqSBkdSBwcmVtaWVyIHBvaW50LCB2b3VzIG7igJnDqnRlcyBwcm9iYWJsZW1lbnQgcGFzIGNvdXBhYmxlIGR1IGRldXhpw6htZS4gRXQgaW52ZXJzZW1lbnQ6IHNpIHZvdXMgcmVtYXJxdWV6IHF1ZSB2b3VzIHZvdXMgZXN0aW1leiBzb3V2ZW50IG1laWxsZXVycyBxdWUgbGVzIGF1dHJlcywgdm91cyBu4oCZw6p0ZXMgcHJvYmFibGVtZW50IHBhcyB0cm9wIGZhdGFsaXN0ZS4gTOKAmWFudGlkb3RlIGF1IGZhdGFsaXNtZSBldCBhdSBcIm1lc3NpYW5pc21lXCIgZXN0IGxlIG3Dqm1lLiBOb3VzIGRldm9ucyBub3VzIHJhcHBlbGVyIHF1ZSBub3VzIHNvbW1lcyBkZXMgcMOpY2hldXJzLCBzYXV2w6lzIHBhciBncsOiY2UsIMOgIHF1aSBpbCBhIMOpdMOpIGZhaXQgbOKAmWhvbm5ldXIgZGUgc2VydmlyIGxlcyBhdXRyZXMuIEV0IGRhbnMgdG91dCBub3RyZSBzZXJ2aWNlLCBub3VzIGNoZXJjaG9ucyDDoCBpbWl0ZXIgSsOpc3VzLCBub3RyZSBTZXJ2aXRldXIgc3VwcsOqbWUuIErDqXN1cyBhIGNsYWlyZW1lbnQgZGl0IHF14oCZaWwgbuKAmcOpdGFpdCBwYXMgdmVudSBwb3VyIHNlIGZhaXJlIHNlcnZpciwgbWFpcyBwb3VyIHNlcnZpciBldCBkb25uZXIgc2EgdmllIGVuIHJhbsOnb24gcG91ciBiZWF1Y291cCAoTWFyYyAxMC40NSkuIERlIG3Dqm1lLCBs4oCZYXDDtHRyZSBQYXVsIHJhcHBlbGxlIHF1ZSBKw6lzdXMsIGxlIHNldWwgaG9tbWUgcXVpIMOpdGFpdCBvYmplY3RpdmVtZW50IHN1cMOpcmlldXIgw6AgdG91cyBsZXMgYXV0cmVzIGhvbW1lcywgbm91cyBhIGNvbnNpZMOpcsOpcyBzdXDDqXJpZXVycyDDoCBsdWktbcOqbWUgZXQgYSBwcmlzIGxhIGNvbmRpdGlvbiBkZSBzZXJ2aXRldXIsIHPigJlhYmFpc3NhbnQganVzcXXigJnDoCBzdWJpciBsYSBtb3J0IChQaGlsaXBwaWVucyAyLjEtMTEpLiBDb25jcsOodGVtZW50LCBkYW5zIHZvdHJlIGNyaXNlIGRlIG1pbmlzdMOocmUgYWN0dWVsbGUsIHF1ZWxsZSBlc3QgbGEgYm9ubmUgY2hvc2Ugw6AgZmFpcmU/IFZvdXMgdGFpcmUgZXQgZnVpcj8gUmVzdGVyIGV0IGNvbWJhdHRyZT8gTGEgcsOpcG9uc2UgZXN0IGJpZW4gc8O7cjog4oCcw4dhIGTDqXBlbmQu4oCdIFBvc2V6LXZvdXMgcGx1dMO0dCBjZSBnZW5yZSBkZSBxdWVzdGlvbjogUG91cnF1b2kgdm91ZHJpZXotdm91cyBmdWlyPyBQb3VycXVvaSB2b3Vkcmllei12b3VzIHZvdXMgYmF0dHJlP1xuXG5Ew6ljaWRlei12b3VzIGRlIGZ1aXIgaHVtYmxlbWVudCBlbiDDqXRhbnQgc291bWlzIMOgIERpZXU/IE91IHBhciBmYWNpbGl0w6ksIHZvaXJlIGF2ZWMgZmF0YWxpc21lPyBBdmV6LXZvdXMgY2hvaXNpIGRlIHJlc3RlciBldCBjb21iYXR0cmUgZGUgbWFuacOocmUgaHVtYmxlIGV0IHNvdW1pc2Ugw6AgRGlldSwgcG91ciBzZXJ2aXIgYXUgYmllbiBkdSB0cm91cGVhdT8gT3UgbGUgZmFpdGVzLXZvdXMsIHBhcmNlIHF1ZSB2b3VzIGRldmV6IGTDqWZlbmRyZSB2b3RyZSByb3lhdW1lPyBBcHLDqHMgdG91dCwgdW4gXCJob21tZSBkZSBEaWV1XCIgbuKAmWVzdCBwbHVzIHJpZW4gc2FucyBcInNvblwiIHJveWF1bWUgZXQgXCJzZXNcIiBmaWTDqGxlcy5cblxuIyMgQ29uY2x1c2lvblxuXG5RdWUgY2UgbGl2cmUgbm91cyBhaWRlIMOgIG1pZXV4IGNvbXByZW5kcmUgbGVzIG3DqWNhbmlzbWVzIGRlIG5vdHJlIGPFk3VyLiBOb3VzIGZhaXNvbnMgdG91cyBmYWNlIMOgIGRlcyB0ZW50YXRpb25zLiBMZSBmYWlibGUsIMOgIGzigJlvcmd1ZWlsIGRlIHNlIGRpcmUgaHVtYmxlIGV0IGJyaXPDqSwgYWxvcnMgcXXigJlpbCBlc3QgZW4gcsOpYWxpdMOpIGZhdGFsaXN0ZSBldCBsw6JjaGU7IGV0IGxlIGZvcnQsIMOgIGzigJlvcmd1ZWlsIGRlIHNlIHByZW5kcmUgcG91ciB1biBwZXRpdCBtZXNzaWUuIE5vdXMgc29tbWVzIGFwcGVsw6lzIMOgIHNlcnZpciBmaWTDqGxlbWVudCBsZSBwZXVwbGUgZGUgRGlldSBlbiBsZSBub3Vycmlzc2FudCBwYXIgbGEgcGFyb2xlIGRlIERpZXUsIHZlaWxsYW50IHN1ciBsZXMgw6JtZXMgcXVpIG5vdXMgc29udCBjb25macOpZXMgZXQgcG91ciBsZXNxdWVsbGVzIG5vdXMgcmVuZHJvbnMgZGVzIGNvbXB0ZXMgYXUgU2VpZ25ldXIuIFF1ZSBub3RyZSBzZXJ2aWNlIHB1aXNzZSDDqnRyZSB1biBzZXJ2aWNlIGh1bWJsZSwgcmVtcGxpIGRlIHByacOocmVzIGNvbmZpYW50ZXMsIGRhbnMgdW5lIGF0dGl0dWRlIGRlIGpvaWUgcHJvZm9uZGUuIFNveW9ucyDDqW1lcnZlaWxsw6lzIHBhciBEaWV1ISBJbCBlc3QgbGEgc2V1bGUgc291cmNlIMOgIGxhcXVlbGxlIHB1aXNlciBub3RyZSBqb2llLCBxdWVsbGVzIHF1ZSBzb2llbnQgbm9zIGNpcmNvbnN0YW5jZXMuIE7igJlvdWJsaW9ucyBwYXMgbGVzIHRvdXQgZGVybmllcnMgbW90cyBkZSBs4oCZYXDDtHRyZSBQYXVsLCBhdXggcmVzcG9uc2FibGVzIGRlIGzigJnDiWdsaXNlIGTigJnDiXBow6hzZS4gSWwgbGV1ciByYXBwZWxsZSBsZXMgcGFyb2xlcyBtw6ptZXMgZGUgSsOpc3VzOiDigJxJbCB5IGEgcGx1cyBkZSBib25oZXVyIMOgIGRvbm5lciBxdeKAmcOgIHJlY2V2b2lyLuKAnVxuXG5gPHA+Q2V0IGFydGljbGUgZXN0IGxhIHBvc3RmYWNlIGR1IGxpdnJlIDxhIGhyZWY9XCJodHRwczovL2JsZnN0b3JlLmNvbS9jb2xsZWN0aW9ucy9wcmVjb21tYW5kZXMvcHJvZHVjdHMvMy1ob21tZXMtZXQtdW5lLWNvdXJvbm5lXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPjxlbT5Ucm9pcyBob21tZXMgZXQgdW5lIGNvdXJvbm5lPC9lbT48L2E+IGRlIEdlbmUgRWR3YXJkcyBxdeKAmW9uIG3igJlhIGRlbWFuZMOpIGTigJnDqWNyaXJlIHBvdXIgbGEgcsOpw6lkaXRpb24gcXVpIHNvcnQgbGUgOCBub3ZlbWJyZSAyMDIzLjwvcD5gXG5cblxcXG4iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJTdMOpcGhhbmUgS2FwaXRhbml1ayIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9zdGVwaGFuZV9rYXBpdGFuaXVrX2Jsb2d0cHNnX2U2ZTMxYjU4NGQuanBnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvVHJvaXNfaG9tbWVzX2V0X3VuZV9jb3Vyb25uZV9jN2Q0NTkxMDg2LnBuZyIsImhlaWdodCI6NzMzLCJ3aWR0aCI6MTEwMCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiTWFuZGF0IGN1bHR1cmVsIn0seyJuYW1lIjoiUmVjZW5zaW9uIGRlIGxpdnJlIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJRdeKAmWVzdC1jZSBxdWUgY+KAmWVzdCBkaWZmaWNpbGUgZOKAmcOqdHJlIHVuIHJlc3BvbnNhYmxlIGNocsOpdGllbiEgUXVlIHR1IHNvaXMgcGFzdGV1ciBvdSBxdWUgdHUgc29pcyBkaXJpZ2VhbnQgZOKAmWVudHJlcHJpc2UsIGPigJllc3QgdnJhaW1lbnQgdW5lIHDDqXJpb2RlIGRpZmZpY2lsZSBwb3VyIGxlcyBsZWFkZXJzLiBD4oCZw6l0YWl0IGRvbmMgYXZlYyBqb2llIHF1ZSBq4oCZYWkgw6ljcml0IGxhIHBvc3RmYWNlIHBvdXIgbGEgcsOpw6lkaXRpb24gZHUgcGV0aXQgbGl2cmUgY2xhc3NpcXVlIDx1PipbVHJvaXMgaG9tbWVzIGV0IHVuZSBjb3Vyb25uZV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vY29sbGVjdGlvbnMvcHJlY29tbWFuZGVzL3Byb2R1Y3RzLzMtaG9tbWVzLWV0LXVuZS1jb3Vyb25uZSk8L3U+Ki4gSuKAmXkgcmFjb250ZSBtb24gcHJvcHJlIGJlc29pbiBwb3VyIGNlIGxpdnJlIGV0IGplIGRvbm5lIDMgY29uc2VpbHMgcG91ciBtZXR0cmUgZW4gcHJhdGlxdWUgbGVzIGVuc2VpZ25lbWVudHMgZHUgbGl2cmUuIn1dfSx7ImlkIjoiMzY0MiIsInRpdGxlIjoiTGVzIG5vdXZlYXV0w6lzIGRlIGxhIHJlbnRyw6llIMOgIEJMRiDDiWRpdGlvbnMiLCJzbHVnIjoibGVzLW5vdXZlYXV0ZXMtZGUtbGEtcmVudHJlZS1hLWJsZi1lZGl0aW9ucyIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyMy0xMC0xNFQxMDowMDowMC4wMDBaIiwiYm9keSI6IlZvaWNpIHVuZSBjb3VydGUgcHLDqXNlbnRhdGlvbiAob2ZmaWNpZWxsZSkgZGUgY2hhcXVlIHRpdHJlLiBNYWlzIGF2YW50LCBtYSB2ZXJzaW9uIHVsdHJhY291cnRlOlxuXG5MZSAqKnByZW1pZXIqKiBlc3QgW3VuIGxpdnJlIGTigJnDqXZhbmfDqWxpc2F0aW9uXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9xdWVzdC1jZS1xdWUtdHUtY3JvaXM/X3Bvcz0xJl9zaWQ9YzMxMzEzYjA5Jl9zcz1yKSBwdWJsacOpIGVuIHBhcnRlbmFyaWF0IGF2ZWMgVFBTRyBldCBtZWlsbGV1cmUgdmVudGUgZGUgbOKAmWFubsOpZSAoZW4gc2V1bGVtZW50IDYgc2VtYWluZXMhKSBhdmVjIDUwMDAgZXhlbXBsYWlyZXMgdmVuZHVzIGTDqWrDoC5cblxuTGUgKipzZWNvbmQqKiBlc3QgW3VuIHN1cGVyIGxpdnJlIHBvdXIgZW5mYW50cyBkZSBOYW5jeSBHdXRocmllXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9qZS12b2lzLWplc3VzLWRhbnMtbGFuY2llbi10ZXN0YW1lbnQ/X3Bvcz0xJl9zaWQ9MDc0ODdkYzI0Jl9zcz1yKSBxdWkgbW9udHJlIGF1eCBwZXRpdHMgY29tbWVudCB1bmUgdmluZ3RhaW5lIGTigJloaXN0b2lyZXMgZGUgbOKAmUFuY2llbiBUZXN0ZW1lbnQgcG9pbnRlbnQgdmVycyBKw6lzdXMuXG5cbkxlICoqdHJvaXNpw6htZSoqIGVzdCBbdHLDqHMgc3DDqWNpYWxdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL2NyZWVzLXBvdXItc2VtZXJ2ZWlsbGVyP19wb3M9MSZfc2lkPWYyOGQ5MjUwYiZfc3M9ciksIGPigJllc3Qgbm9uIHNldWxlbWVudCBtYSBjb3V2ZXJ0dXJlIHByw6lmw6lyw6llIGRlIDIwMjMsIGPigJllc3QgYXVzc2kgdW4gZGVzIGxpdnJlcyBhdSBjxZN1ciBkZSBtYSBtaXNzaW9uIGRlIHZpZSBldCBkZSBtYSB2aWUgcHJvZmVzc2lvbm5lbGxlIMOgIEJMRiDDiWRpdGlvbnMuXG5cbkxlICoqcXVhdHJpw6htZSoqIGVzdCBbbGEgdmVyc2lvbiBwb3VyIGFkb3NdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEwLXF1ZXN0aW9ucy1xdWUtdHUtZGV2cmFpcy10ZS1wb3Nlci1zdXItbGUtY2hyaXN0aWFuaXNtZT9fcG9zPTEmX3NpZD04OWE2ODVhZDgmX3NzPXImdmFyaWFudD00MjU4MTkyNDkzNzkyNikgZHUgdGl0cmUgcHJvdm9jYW50LCAqMTIgcmFpc29ucyBkZSBuZSBwbHVzIGNyb2lyZSBhdSBjaHJpc3RpYW5pc21lKiAoUmViZWNjYSBNY0xhdWdobGluKS4gVW4gc3VwZXJiZSBvdXZyYWdlIGTigJlhcG9sb2fDqXRpcXVlIHF1aSBlc3QgW2Fib3Jkw6kgZW4gY2UgbW9tZW50IGVuIDEyIMOpcGlzb2RlcyBkYW5zIGxlIHBvZGNhc3QgXSgvcG9kY2FzdHMvY2hyZXRpZW5uZS9kaWV1LWVzdC1pbC1ob21vcGhvYmUjc2VyaWUpKltDaHLDqXRpZW5uZV0oL3BvZGNhc3RzL2NocmV0aWVubmUvZGlldS1lc3QtaWwtaG9tb3Bob2JlI3NlcmllKSogc3VyIFRQU0cuXG5cbkxlICoqY2lucXVpw6htZSoqIGVzdCBbbGEgcHJvY2hhaW5lIHNhaXNvbiBkdSBraXQgUGV0aXQgZ3JvdXBlXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9wYWNrLXBldGl0LWdyb3VwZS1sZS1kZXZlbG9wcGVtZW50LXBlcnNvbm5lbD9fcG9zPTQmX3NpZD02MGRiMzhhYTUmX3NzPXIpLCBwb3VyIGFib3JkZXIgbGEgZm9pIGNocsOpdGllbm5lIGF2ZWMgc2VzIGFtaXMgZGFucyBkZXMgcGV0aXRzIGdyb3VwZXMgaW5mb3JtZWxzLiBVbiBnZW5yZSBkZSBwcsOpLcOpdmFuZ8OpbGlzYXRpb24gcXVpIHByZW5kIGNvbW1lIHBvaW50IGRlIGTDqXBhcnQgbGVzIHByw6lvY2N1cGF0aW9ucyBkZSBub3MgcHJvY2hlcy5cblxuPGJyPlxuXG4jIyMgMS4gKltRdeKAmWVzdC1jZSBxdWUgdHUgY3JvaXM/XShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9xdWVzdC1jZS1xdWUtdHUtY3JvaXM/X3Bvcz0xJl9zaWQ9YzMxMzEzYjA5Jl9zcz1yKSpcblxuPGJyPlxuXG4gIVtdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvMTY5MzgzNzMwMTQyNV9taW5fMTNlYTFiOTAyMS5qcGcpPGJyPlxuXG4qW1F1J2VzdC1jZSBxdWUgdHUgY3JvaXM/XShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9xdWVzdC1jZS1xdWUtdHUtY3JvaXM/X3Bvcz0xJl9zaWQ9YzMxMzEzYjA5Jl9zcz1yKSogZXN0IHVuIGxpdnJlIHF1aSBwcsOpc2VudGUgbCdlc3NlbnRpZWwgZGUgbGEgZm9pIGNocsOpdGllbm5lIGVuIHF1ZWxxdWVzIHBhZ2VzLiBJbCBwZXJtZXQgw6AgdG91dGVzIGxlcyBwZXJzb25uZXMgY3VyaWV1c2VzIGRlIHNhdm9pciBjZSBxdWUgbGVzIGNocsOpdGllbnMgY3JvaWVudCB2cmFpbWVudCwgZGUgZMOpY291dnJpciBsZSBjxZN1ciBkdSBjaHJpc3RpYW5pc21lIGV0IGxlIHB1aXNzYW50IG1lc3NhZ2UgZGUgbCfDiXZhbmdpbGUuXG5cblVuIGxpdnJlIGQnw6l2YW5nw6lsaXNhdGlvbiBhY2Nlc3NpYmxlIMOgIHRvdXMsIHF1ZSB2b3VzIHBvdXZleiBvZmZyaXIgw6AgdG91cyB2b3MgYW1pcyFcblxuQ2UgbGl2cmUgZXhpc3RlIHBvdXI6XG5cbjEuIFRlZGR5LCB2b3RyZSBhbWkgYXRow6llLCBxdWkgZXN0IHBsdXTDtHQgbW9xdWV1ciB2aXMtw6AtdmlzIGRlIGxhIGZvaSwgbWFpcyBwYXJ0YW50IGRlIGxpcmUgdW4gcGV0aXQgbGl2cmUgcGFyIGN1cmlvc2l0w6kgcG91ciBzYXZvaXIgY2UgcXVlIHZvdXMgY3JveWV6LlxuMi4gU3lsdmFpbiwgMTkgYW5zLCBxdWkgdmllbnQgZGUgZGV2ZW5pciBjaHLDqXRpZW4gZW4gZW50ZW5kYW50IHBhcmxlciBkZSBKw6lzdXMgcGFyIHVuIGFtaSBldCBxdWkgdmV1dCBlbiBzYXZvaXIgcGx1cyBzdXIgbGEgZm9pIGNocsOpdGllbm5lLlxuMy4gVmFuZXNzYSwgbGEgdHJlbnRhaW5lLCBk4oCZb3JpZ2luZSBjYXRob2xpcXVlLCBxdWkgZnLDqXF1ZW50ZSB2b3RyZSDDqWdsaXNlIGRlcHVpcyBxdWVscXVlcyBzZW1haW5lcywgcXVpIGVzdCBpbnRyaWd1w6llIGV0IHZvdWRyYWl0IGNyZXVzZXIgbGEgZm9pIHByb3Rlc3RhbnRlLlxuNC4gSGFzc2FuZSwgbGUgZ8OpcmFudCBkZSB2b3RyZSBrZWJhYi4gSWwgZXN0IG11c3VsbWFuLCBtYWlzIHNlIGRlbWFuZGUgY2UgcXVlIHZvdXMgY3JveWV6IGVuIHRhbnQgcXVlIGNocsOpdGllbi5cblxuIyMjIDIuICpbSmUgdm9pcyBKw6lzdXMgZGFucyBs4oCZQW5jaWVuIFRlc3RhbWVudF0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvamUtdm9pcy1qZXN1cy1kYW5zLWxhbmNpZW4tdGVzdGFtZW50P19wb3M9MSZfc2lkPTA3NDg3ZGMyNCZfc3M9cikqXG5cbjxicj5cblxuICFbXShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0LzE2OTU0OTUzMTAwODBfNTEwZGQxNzg4NS5qcGcpPGJyPlxuXG5Kw6lzdXMgZXN0IHByw6lzZW50IHBhcnRvdXQgZGFucyBs4oCZYW5jaWVuIFRlc3RhbWVudCBzaSBvbiBzYWl0IGNlIHF1ZSBs4oCZb24gY2hlcmNoZS4gU29uIG9tYnJlIHBsYW5lIHN1ciB0b3V0ZXMgbGVzIHBhZ2VzLCBkYW5zIHRvdXRlcyBsZXMgaGlzdG9pcmVz4oCmXG5cblVuIHRleHRlIGFkbWlyYWJsZW1lbnQgaWxsdXN0csOpIHBvdXIgcGVybWV0dHJlIGF1eCBlbmZhbnRzIGRlIHZvaXIgZGVzIG9tYnJlcyBkZSBKw6lzdXMgw6AgdHJhdmVycyB0b3V0ZSBsYSBncmFuZGUgaGlzdG9pcmUgZGUgbOKAmUFuY2llbiBUZXN0YW1lbnQuIENlcnRhaW5zIHLDqWNpdHMgc29udCBjb25udXMsIGTigJlhdXRyZSBtb2lucywgbWFpcyBwYXJ0b3V0IGzigJllbmZhbnQgcGV1dCBkw6ljb3V2cmlyIErDqXN1cywgbOKAmUFnbmVhdSBkZSBsYSBQw6JxdWUsIGxlIExpb24gZGUgSnVkYSwgbGUgcGFpbiBkZSB2aWUsIGxlIFNhdXZldXIgZHUgbW9uZGUsIGV0IGJpZW4gZOKAmWF1dHJlcyBlbmNvcmUuXG5cbiMjIyAzLiAqW0Nyw6nDqXMgcG91ciBz4oCZw6ltZXJ2ZWlsbGVyXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9jcmVlcy1wb3VyLXNlbWVydmVpbGxlcj9fcG9zPTEmX3NpZD1mMjhkOTI1MGImX3NzPXIpKlxuXG48YnI+XG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9DUkVFX1BPVVJfU19FTUVSVkVJTExFUl8wMV80MWMzYWY3NGVhLmpwZyk8YnI+XG5cbioqRXN0LWNlIHF1ZSBEaWV1IHZvdXMgw6ltZXJ2ZWlsbGUgZW5jb3JlPyoqXG5cbkF2ZWMgbGUgdGVtcHMsIG5vdHJlIERpZXUgZGV2aWVudCBwYXJmb2lzIHBldGl0LCBkaXN0YW50LCBwYXMgdHLDqHMgaW1wcmVzc2lvbm5hbnQgw6Agbm9zIHlldXguLi4gTm91cyBwZXJkb25zIGxlIHNlbnMgZGUgbOKAmcOpbWVydmVpbGxlbWVudCBmYWNlIMOgIGxhIHNwbGVuZGV1ciBkZSBsYSBnbG9pcmUgZGUgRGlldS4gTm91cyBwYXJ0b25zIGRvbmMgY29uc3RhbW1lbnQgw6AgbGEgcmVjaGVyY2hlIGRlIHBldGl0cyBib25oZXVycyBkZSBzdWJzdGl0dXRpb24sIGRlcyBwZXRpdHMgYm9uaGV1cnMgcXVpLCBub3VzIGzigJllc3DDqXJvbnMsIHBvdXJyb250IG5vdXMgcHJvY3VyZXIgdW4gY2VydGFpbiBzZW50aW1lbnQgZOKAmcOpbWVydmVpbGxlbWVudCBkb250IG5vdXMgYXZvbnMgdGFudCBiZXNvaW4uIE5vdXMgc29tbWVzIHRyw6hzIHNvdXZlbnQgZMOpw6d1cy4uLiBcblxuRGlldSBhIGVuIGVmZmV0IHNvaWduZXVzZW1lbnQgY29uw6d1IGxlIG1vbmRlIGFmaW4gcXXigJlpbCByZWZsw6h0ZSBzYSBnbG9pcmUuIFRvdXRlcyBsZXMgY2hvc2VzIG1lcnZlaWxsZXVzZXMgZGUgc2EgY3LDqWF0aW9uIG5lIHBldXZlbnQgcXXigJlvcmllbnRlciBub3RyZSByZWdhcmQgdmVycyBsdWksIGxlIERpZXUgbWVydmVpbGxldXggcXVpIGxlcyBhIGNyw6nDqWVzLiBUYW50IHF1ZSBub3RyZSBjxZN1ciBu4oCZZXN0IHBhcyB0b3RhbGVtZW50IMOpbWVydmVpbGzDqSBwYXIgRGlldSwgbm91cyB2aXZyb25zIGRhbnMgdW5lIHBvdXJzdWl0ZSBjb25zdGFudGUgZGUgbm9zIHLDqnZlcyBldCBlc3BvaXJzIMOpcGjDqW3DqHJlcy4gTOKAmcOqdHJlIGh1bWFpbiBhIMOpdMOpIGNyw6nDqSBwb3VyIHPigJnDqW1lcnZlaWxsZXIhIENldCDDqW1lcnZlaWxsZW1lbnQgZGV2YW50IERpZXUgbm91cyBjb25kdWl0IGRyb2l0IMOgIGzigJlhZG9yYXRpb24gZGUgRGlldS4gQ2UgbGl2cmUgdm91cyBhaWRlcmEgw6Agdm9pciBsZSBtb25kZSBjb21tZSBEaWV1IGxlIHZvaXQgZXQgw6Agdm91cyBkw6lsZWN0ZXIgZGUgc2EgZ2xvaXJlIMOpcG91c3RvdWZsYW50ZSBkYW5zIHRvdXMgbGVzIGdlc3RlcyBkZSB2b3RyZSBxdW90aWRpZW4uXG5cbiMjIyA0LiAqWzEwIFF1ZXN0aW9ucyBxdWUgdHUgZGV2cmFpcyB0ZSBwb3NlciBzdXIgbGUgY2hyaXN0aWFuaXNtZV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvMTAtcXVlc3Rpb25zLXF1ZS10dS1kZXZyYWlzLXRlLXBvc2VyLXN1ci1sZS1jaHJpc3RpYW5pc21lP19wb3M9MSZfc2lkPTg5YTY4NWFkOCZfc3M9ciZ2YXJpYW50PTQyNTgxOTI0OTM3OTI2KSpcblxuPGJyPlxuXG4gIVtdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvMTBfUVVFU1RJT05fUVVFX1RVX0RFVlJBSVNfVEVfUE9TRVJfU1VSX0xFX0NIUklTVElBTklTTUVfMDFfZmNkY2ZkOTdlNi5qcGcpPGJyPlxuXG4qKkF1IGx5Y8OpZSwgYXUgY29sbMOoZ2UsIGF2ZWMgdGVzIGFtaXMgb3Ugc3VyIGxlcyByw6lzZWF1eCBzb2NpYXV4LCB0dSBlcyBzYW5zIGRvdXRlIGNvbmZyb250w6kgw6AgZGVzIHF1ZXN0aW9ucyBkaWZmaWNpbGVzIHN1ciBsYSBmb2kgY2hyw6l0aWVubmU6KipcblxuKiBDb21tZW50IHNhdm9pciBzaSBsYSBCaWJsZSBkaXQgdnJhaT9cbiogUG91cnF1b2kgbmUgc3Vpcy1qZSBwYXMgbGlicmUgZOKAmWFpbWVyIHF1aSBqZSB2ZXV4P1xuKiBRdWUgZGl0IGxhIEJpYmxlIHN1ciBsZXMgcGVyc29ubmVzIHRyYW5zZ2VucmVzP1xuKiBDb21tZW50IMOqdHJlIHPDu3IgZOKAmWFsbGVyIGF1IHBhcmFkaXMgYXByw6hzIGxhIG1vcnQ/XG5cblNpIHR1IHRlIHBvc2VzIGNlIGdlbnJlIGRlIHF1ZXN0aW9ucywgY2UgbGl2cmUgYSDDqXTDqSDDqWNyaXQgcG91ciB0b2khXG5cblJlYmVjY2EgTWNMYXVnaGxpbiBu4oCZYSBwYXMgcGV1ciBkZSByw6lwb25kcmUgYXV4IHF1ZXN0aW9ucyBkaWZmaWNpbGVzLiBCaWJsZSBlbiBtYWluLCBlbGxlIGFib3JkZSB0b3VzIGxlcyBzdWpldHMgYnLDu2xhbnRzLCBzYW5zIHRhYm91cywgbWFpcyBhdmVjIGh1bWlsaXTDqSwgZG91Y2V1ciBldCBodW1vdXIuIExlcyBxdWVzdGlvbnMgcXVlIHR1IHRlIHBvc2VzIHNvbnQgaW1wb3J0YW50ZXMuIE9zZSByw6lmbMOpY2hpciBwYXIgdG9pLW3Dqm1lIGV0IGNoZXJjaGVyIGxlcyByw6lwb25zZXMgw6AgY2VzIHF1ZXN0aW9ucyBlc3NlbnRpZWxsZXMuXG5cbiMjIyA1LiAqW1BldGl0IEdyb3VwZTogTGUgZMOpdmVsb3BwZW1lbnQgcGVyc29ubmVsXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9wYWNrLXBldGl0LWdyb3VwZS1sZS1kZXZlbG9wcGVtZW50LXBlcnNvbm5lbD9fcG9zPTQmX3NpZD02MGRiMzhhYTUmX3NzPXIpKlxuXG48YnI+XG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC8xNjk1NDk1MzEwMDczX2UwYzNhMGU4NWIuanBnKTxicj5cblxuQ2V0IG91dGlsIHBlcm1ldCBk4oCZYW5pbWVyIG5ldWYgcmVuY29udHJlcy4gQ2hhcXVlIHJlbmNvbnRyZSBhYm9yZGUgdW5lIHRow6ltYXRpcXVlIHByb3ByZSBhdSBkw6l2ZWxvcHBlbWVudCBwZXJzb25uZWwgZXQgcGVybWV0IGRlIGNvbm5hw650cmUgbGEgcG9zaXRpb24gZGUgbGEgQmlibGUgc3VyIGNlIHN1amV0LiBQbHV0w7R0IHF1ZSBkZSBwcm92b3F1ZXIgZOKAmWVtYmzDqWUgdW5lIGNyaXRpcXVlIGZyb250YWxlIGRlcyBtw6l0aG9kZXMgZHUgZMOpdmVsb3BwZW1lbnQgcGVyc29ubmVsIHF1aSBwb3VycmFpdCBwcm92b3F1ZXIgdW5lIHLDqWFjdGlvbiBkZSByZWpldCByYXBpZGUgZGUgbGEgcGFydCBkZSBub24tY2hyw6l0aWVucywgbGVzIGF1dGV1cnMgb250IGltYWdpbsOpIHVuZSBtb250w6llIGVuIHB1aXNzYW5jZSBwcm9ncmVzc2l2ZSBkZSBs4oCZYW5ub25jZSBkZSBs4oCZw4l2YW5naWxlLiBM4oCZb2JqZWN0aWYgZXN0IHF1ZSwgYXUgZnVyIGV0IMOgIG1lc3VyZSBkZXMgcmVuY29udHJlcywgbGVzIHBhcnRpY2lwYW50cyBzb2llbnQgYW1lbsOpcyDDoCBjcml0aXF1ZXIgZXV4LW3Dqm1lcyBsZXMgcsOpcG9uc2VzIGFwcG9ydMOpZXMgcGFyIGxlIGTDqXZlbG9wcGVtZW50IHBlcnNvbm5lbCBkZXZhbnQgbGEgcGVydGluZW5jZSBkdSBtZXNzYWdlIGJpYmxpcXVlLiBcblxuTGVzIG5ldWYgdGjDqG1lcyBhYm9yZMOpcyBzb250OlxuXG5TZXNzaW9uIDE6IE1lcyB2YWxldXJzXG5cbuKAi1Nlc3Npb24gMjogTWVzIGNyb3lhbmNlc1xuXG5TZXNzaW9uIDM6IE1hIG1pc3Npb24gZGUgdmllXG5cbuKAi1Nlc3Npb24gNDogTW9uIGJvbmhldXJcblxuU2Vzc2lvbiA1OiBMYSBtw6lkaXRhdGlvblxuXG5TZXNzaW9uIDY6IENoYW5nZXIsIMOqdHJlIHRyYW5zZm9ybcOpISBcblxuU2Vzc2lvbiA3OiBNZSBsaWLDqXJlclxuXG7igItTZXNzaW9uIDg6IExhIHJlbmFpc3NhbmNlXG5cblNlc3Npb24gOTogTGEgbWVpbGxldXJlIHZlcnNpb24gZGUgbW9pLW3Dqm1lIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiU3TDqXBoYW5lIEthcGl0YW5pdWsiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvc3RlcGhhbmVfa2FwaXRhbml1a19ibG9ndHBzZ19lNmUzMWI1ODRkLmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0LzE2OTU0OTUzMTAxMDJfZTg0MTgwZDEyYS5qcGciLCJoZWlnaHQiOjEwMDAsIndpZHRoIjoxNTAwLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJSZWNlbnNpb24gZGUgbGl2cmUifSx7Im5hbWUiOiJBY3R1YWxpdMOpIHBlcnNvIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJK4oCZYWkgcsOpY2VtbWVudCBlbnRlbmR1IHF1ZWxxdeKAmXVuIHLDqXN1bWVyIMOgIHBlcmZlY3Rpb24gbW9uIHNlbnRpbWVudCBzdXIgbW9uIGpvYjogamUgbeKAmWFtdXNlIHRlbGxlbWVudCBjaGFxdWUgam91ci4gQ+KAmWVzdCB1bmUgYsOpbsOpZGljdGlvbiBpbmNyb3lhYmxlIGV0IHVuZSBqb2llIGTigJlhbGxlciBhdSB0cmF2YWlsLiBEYW5zIHVuIG1vbmRlIG51bcOpcmlxdWUsIGrigJlhaW1lIHRyYXZhaWxsZXIgc3VyIGRlcyBvYmpldHMgcGh5c2lxdWVzLiBEYW5zIHVuIG1vbmRlIGRlIHN1cGVyZmljaWFsaXTDqSwgamUgc3VpcyBlbmNoYW50w6kgZOKAmWF2b2lyIGxhIG1pc3Npb24gZOKAmWVuY291cmFnZXIgbm9zIGF1dGV1cnMgZXQgw6lkaXRldXJzIMOgIMOpY3JpcmUgcG91ciBhaWRlciBs4oCZw4lnbGlzZSDDoCBub3VycmlyIHVuZSBwYXNzaW9uIHBvdXIgRGlldS4gVm9pY2kgbm9zIDUgbm91dmVhdXTDqXMgcsOpY2VudGVzLCBkb250IGxlIG5vdXZlYXUgbGl2cmUgZGUgQmVuIEVnZ2VuIHF1aSBhIGTDqWrDoCDDqXTDqSB2ZW5kdSDDoCBwbHVzIGRlIDUwMDAgZXhlbXBsYWlyZXMgZW4gMiBtb2lzLiJ9LHt9XX0seyJpZCI6IjM1ODkiLCJ0aXRsZSI6IkF1dGV1cnMgZXQgw6lkaXRldXJzIMOpdmFuZ8OpbGlxdWVzOiBsZXMgc2VjcmV0cyBkZXMgbcOpdGllcnMiLCJzbHVnIjoiYXV0ZXVycy1lZGl0ZXVycy1ldmFuZ2VsaXF1ZXMiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjMtMDYtMjZUMTA6MDA6MDAuMDAwWiIsImJvZHkiOiJgPGlmcmFtZSB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9XCI0MDBcIiBzcmM9XCJodHRwczovL3d3dy55b3V0dWJlLmNvbS9lbWJlZC9COHNxZi1GX2Y4RVwiIHRpdGxlPVwiWW91VHViZSB2aWRlbyBwbGF5ZXJcIiBmcmFtZWJvcmRlcj1cIjBcIiBhbGxvdz1cImFjY2VsZXJvbWV0ZXI7IGF1dG9wbGF5OyBjbGlwYm9hcmQtd3JpdGU7IGVuY3J5cHRlZC1tZWRpYTsgZ3lyb3Njb3BlOyBwaWN0dXJlLWluLXBpY3R1cmU7IHdlYi1zaGFyZVwiIGFsbG93ZnVsbHNjcmVlbj48L2lmcmFtZT5gXG5cblZvaWNpIGxlcyBxdWVzdGlvbnMgcG9zw6llcyBwYXIgUGFzY2FsOlxuXG4xXFwuIFN0w6lwaGFuZSwgcGFybGUtbm91cyBkdSBtw6l0aWVyIGQnw6lkaXRldXIuXG5cbiogRW4gcXVvaSBjb25zaXN0ZSB0b24gdHJhdmFpbD9cbiogUXVlbHMgc29udCBsZXMgcHJpbmNpcGF1eCBkw6lmaXM/XG4qIExlIG1hcmNow6kgZHUgbGl2cmUgY2hyw6l0aWVuIGVzdC1pbCByZW50YWJsZT8gQ29tbWVudCBsYSByZWxhdGlvbiBhdmVjIGxlcyBhdXRyZXMgw6lkaXRldXJzIHNlIHZpdC1lbGxlP1xuKiBRdWVsbGUgYSDDqXTDqSB0YSBwbHVzIGdyYW5kZSBlcnJldXIgb3UgdG9uIHBpcmUgY291cD9cbiogUXVlbCBlc3QgbCdhY2NvbXBsaXNzZW1lbnQgZG9udCB0dSBlcyBsZSBwbHVzIGZpZXI/XG5cbjJcXC4gUmFwaGHDq2wsIHBhcmxlLW5vdXMgZHUgbcOpdGllciBkJ2F1dGV1ciBjaHLDqXRpZW4uXG5cbiogUXVlbHMgc29udCBsZXMgcHJpbmNpcGF1eCBkw6lmaXMgcG91ciB1biBhdXRldXIgY2hyw6l0aWVuP1xuKiBRdWVsbGVzIHNvbnQgdGVzIHByaW5jaXBhbGVzIHNvdXJjZXMgZCdpbnNwaXJhdGlvbiBwb3VyIMOpY3JpcmUgZGVzIGFydGljbGVzLCBkZXMgc2VybW9ucyBvdSB1biBsaXZyZT9cbiogRMOpY3Jpcy1ub3VzIHRhIG3DqXRob2RlIGRlIHRyYXZhaWwgcG91ciBhcnJpdmVyIMOgIMOpY3JpcmUgZXQgw6AgZmluYWxpc2VyIHRlcyBwcm9qZXRzIGQnw6ljcml0dXJlPyBRdWVscyBvdXRpbHMgdXRpbGlzZXMtdHU/XG4qIFF1J2FzLXR1IHRyb3V2w6kgZCdhZ3LDqWFibGUgZW4gdHJhdmFpbGxhbnQgYXZlYyB1biDDqWRpdGV1ciBldCBxdSdlc3QtY2UgcXVpIHBvdXJyYWl0IMOqdHJlIGFtw6lsaW9yw6k/XG4qIFRyYXZhaWxsZXMtdHUgc3VyIHVuIGxpdnJlIHByw6lzZW50ZW1lbnQgZXQgYXMtdHUgZGVzIHByb2pldHMgZCfDqWNyaXR1cmUgcG91ciBsZSBmdXR1cj9cblxuXFxcbiIsImF1dGhvciI6eyJmdWxsTmFtZSI6IlN0w6lwaGFuZSBLYXBpdGFuaXVrIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3N0ZXBoYW5lX2thcGl0YW5pdWtfYmxvZ3Rwc2dfZTZlMzFiNTg0ZC5qcGciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9jb3JhbV9kZW9fYXV0ZXVyX2VkaXRldXJfOWU3ODhiM2Q1NC5wbmciLCJoZWlnaHQiOjg3NCwid2lkdGgiOjE0OTIsImFsdGVybmF0aXZlVGV4dCI6IiIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IkFjdHVhbGl0w6kgcGVyc28ifV0sIm1vZHVsZXMiOlt7Il9fdHlwZW5hbWUiOiJDb21wb25lbnRNb2R1bGVMZWFkIiwiY29udGVudCI6IioqSuKAmWFpbWUgYmVhdWNvdXAgbGUgcG9kY2FzdCBkZSBQYXNjYWwgRGVuYXVsdCAqW0NvcmFtIERlb10oaHR0cHM6Ly93d3cueW91dHViZS5jb20vd2F0Y2g/dj1COHNxZi1GX2Y4RSkuKiBD4oCZw6l0YWl0IHVuIHByaXZpbMOoZ2UgcG91ciBSYXBoYcOrbCBDaGFycmllciBldCBtb2kgZOKAmcOqdHJlIHNlcyBpbnZpdMOpcyBwb3VyIGzigJnDqXBpc29kZSBkZSBjZXR0ZSBzZW1haW5lLiBQYXNjYWwgZXN0IHVuIHN1cGVyIGjDtHRlIGV0IGzigJnDqXF1aXBlIHRlY2huaXF1ZSBlc3QgaW1wcmVzc2lvbm5hbnRlLiBOb3VzIHBhcmxvbnMgZGVzIG3DqXRpZXJzIGTigJlhdXRldXIgZXQgZOKAmcOpZGl0ZXVyIChhaW5zaSBxdWUgZGVzIG1vdXRvbnMgQkxGISkuKioifV19LHsiaWQiOiIzNTc5IiwidGl0bGUiOiJIb21tYWdlIMOgIEdlb3JnZSBWZXJ3ZXIgKHZpZMOpbyBzb3VzLXRpdHLDqWUgZW4gZnJhbsOnYWlzKSIsInNsdWciOiJob21tYWdlLXZlcndlciIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyMy0wNi0xOFQxNzozMDowMC4wMDBaIiwiYm9keSI6IlNpIHZvdXMgbmUgbGUgY29ubmFpc3NleiBwYXMsIGNldHRlIHZpZMOpbyBk4oCZaG9tbWFnZSB2b3VzIHBlcm1ldHRyYSBlbiBzZXVsZW1lbnQgNCBtaW51dGVzIGRlIGNvbXByZW5kcmUgdW4gcGV1IGxhIHBlcnNvbm5hbGl0w6kgZGUgY2UgZ3JhbmQgcGV0aXQgbW9uc2lldXIuXG5cbmBgYGphdmFzY3JpcHRcbjxpZnJhbWUgdGl0bGU9XCJ2aW1lby1wbGF5ZXJcIiBzcmM9XCJodHRwczovL3BsYXllci52aW1lby5jb20vdmlkZW8vODE3ODA4NDUzP2g9ZmFlNzVkZDE0N1wiIHdpZHRoPVwiMTAwMFwiIGhlaWdodD1cIjYwMFwiIGZyYW1lYm9yZGVyPVwiMFwiICAgIGFsbG93ZnVsbHNjcmVlbj48L2lmcmFtZT5cbmBgYFxuXG5TaSB2b3VzIHZvdWxleiBhbGxlciBwbHVzIGxvaW4sIGVzc2F5ZXogZGUgbGlyZSBs4oCZdW4gZGUgc2VzIHBldGl0cyBsaXZyZXM6XG5cbiogSuKAmWFpIGJlYXVjb3VwIGFpbcOpICpbUsOpdm9sdXRpb24gZGUgbOKAmWFtb3VyXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9sYS1yZXZvbHV0aW9uLWRlLWwtYW1vdXI/X3Bvcz04Jl9zaWQ9NmY0NGM0M2EzJl9zcz1yKSouXG4qIFZvdXMgcG91dmV6IGF1c3NpIGxpcmUgKltDYXAgc3VyIGxlIGJ1dF0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvY2FwLXN1ci1sZS1idXQ/X3Bvcz00Jl9zaWQ9NmY0NGM0M2EzJl9zcz1yKSouXG5cblxcXG4iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJTdMOpcGhhbmUgS2FwaXRhbml1ayIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9zdGVwaGFuZV9rYXBpdGFuaXVrX2Jsb2d0cHNnX2U2ZTMxYjU4NGQuanBnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvR2VvcmdlX1Zlcndlcl9PTV8xMjAweDBfY19kZWZhdWx0X2UwMTEwMDQ3MWEuanBnIiwiaGVpZ2h0Ijo5MDAsIndpZHRoIjoxMjAwLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiLDiXZhbmfDqWxpc2F0aW9uIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJHZW9yZ2UgVmVyd2VyIMOpdGFpdCBsZSBmb25kYXRldXIgZOKAmU9ww6lyYXRpb24gTW9iaWxpc2F0aW9uLCB1bmUgZ3JhbmRlIG1pc3Npb24gY2hyw6l0aWVubmUgcXVpIHRyYXZhaWxsZSBkYW5zIHBsdXMgZGUgMTAwIHBheXMuIEPigJllc3QgdW4gaG9tbWUgcXVpIG3igJlhIGJlYXVjb3VwIGluZmx1ZW5jw6kgcGFyIHNhIHNpbXBsaWNpdMOpIChpbCBuZSBwb3Nzw6lkYWl0IHByZXNxdWUgcmllbiksIHNvbiBodW1pbGl0w6kgKGlsIGNvbmZlc3NhaXQgcsOpZ3VsacOocmVtZW50IHB1YmxpcXVlbWVudCBzb24gcMOpY2jDqSBldCBzZXMgw6ljaGVjcykgZXQgc29uIHrDqGxlIHBvdXIgZmFpcmUgY29ubmHDrnRyZSBsYSBCb25uZSBOb3V2ZWxsZSBkdSBwYXJkb24gZGVzIHDDqWNow6lzIGVuIErDqXN1cy4ifV19LHsiaWQiOiIzNTY4IiwidGl0bGUiOiI2IG5vdXZlYXV0w6lzIGRlIG1haSDDoCBCTEYgw4lkaXRpb25zICIsInNsdWciOiI2LW5vdXZlYXV0ZXMtZGUtbWFpLWEtYmxmLWVkaXRpb25zIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDIzLTA2LTE2VDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiVm9pY2kgdW5lIGNvdXJ0ZSBwcsOpc2VudGF0aW9uIGRlIGNlcyA2IG5vdXZlYXV4IGxpdnJlcywgZG9udCA0IGRlIEJMRiDDiWRpdGlvbnMgZXQgMiBkZSBub3MgYW1pcyBDcnVjaWZvcm1lLlxuXG4jIyBbMS4gXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9sYS1ncmFjZS1kZS1kaWV1LXN1ZmZpdD9fcG9zPTEmX3NpZD05NGMwNTEzZmImX3NzPXIpKltMYSBncsOiY2UgZGUgRGlldSBzdWZmaXRdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL2xhLWdyYWNlLWRlLWRpZXUtc3VmZml0P19wb3M9MSZfc2lkPTk0YzA1MTNmYiZfc3M9cikqXG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9MQV9HUkFDRV9ERV9ESUVVX1NVRkZJVF8wMV9iN2M3YmFlMWYwLmpwZykqKklsIG5vdXMgYXJyaXZlIMOgIHRvdXMgZGUgbWFsIGFnaXIsIGRlIGJsZXNzZXIgdW4gYW1pLCBkZSBkaXJlIHVuIG1lbnNvbmdlLCBkZSBub3VzIG1ldHRyZSBlbiBjb2zDqHJlLi4uIGV0IG5vdXMgbmUgcG91dm9ucyBwYXMgcsOpcGFyZXIgdG91dCBjZSBxdWUgbm91cyBhdm9ucyBjYXNzw6kuKipcblxuTWFpcyBKw6lzdXMsIGx1aSwgbGUgcGV1dC5cblxuSWwgdmV1dCBub3VzIGFwcHJlbmRyZSDDoCByZWNldm9pciBsYSBncsOiY2UgZGUgRGlldSBxdWkgbm91cyByZW5kIGNhcGFibGVzIGRlIGZhaXJlIGxlIGJpZW4uIEPigJllc3QgdW4gY2FkZWF1IHZyYWltZW50IGZvcm1pZGFibGUhXG5cbiMjIDIuICpbTGUgZ3JhbmQgYWNjdWVpbCBmb3JtaWRhYmxlXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9sZS1ncmFuZC1hY2N1ZWlsLWZvcm1pZGFibGU/X3Bvcz00Jl9zaWQ9MWM1ZDc0MTliJl9zcz1yKSpcblxuICFbXShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L2xlX2dyYW5kX2FjY2V1aWxfZm9ybWlkYWJsZV8wM19lYjA5OWM3OGQ0LmpwZykqKkNvbW1lbnTCoGFpZGVyIG5vcyBlbmZhbnRzIMOgIGFpbWVyIGNvbW1lIErDqXN1cz8qKlxuXG5UcsOocyBzb3V2ZW50LCBub3VzIG5lIHRyYWl0b25zIHBhcyB0b3V0IGxlIG1vbmRlIGRlIGxhIG3Dqm1lIG1hbmnDqHJlLi4uIFNpIHF1ZWxxdeKAmXVuIHBvcnRlIGRlcyB2w6p0ZW1lbnRzIHN0eWzDqXMsIGVzdCB0csOocyBmb3J0IGVuIHNwb3J0LCBvdSBvYnRpZW50IGRlIGJvbnMgcsOpc3VsdGF0cyDDoCBs4oCZw6ljb2xlLi4uIG9uIGEgdG91dCBkZSBzdWl0ZSBlbnZpZSBxdeKAmWlsIGRldmllbm5lIG5vdHJlIGFtaSwgbuKAmWVzdC1jZSBwYXM/XG5cbkxhIEJpYmxlIHJhY29udGUgY29tbWVudCBsZXMgcHJlbWllcnMgY2hyw6l0aWVucyBvbnQgZMO7IGFwcHJlbmRyZSDDoCBhaW1lciB0b3V0IGxlIG1vbmRlLCBzYW5zIGZhaXJlIGRlIGZhdm9yaXRpc21lLiBFbiBlZmZldCwgY+KAmWVzdCBhaW5zaSBxdWUgSsOpc3VzIGFpbWFpdCBsb3JzcXXigJlpbCDDqXRhaXQgc3VyIGxhIHRlcnJlLiBJbCBhY2N1ZWlsbGFpdCB0b3V0IGxlIG1vbmRlLCBzYW5zIGZhaXJlIGRlIGRpc3RpbmN0aW9uLiBOb3VzIGF1c3NpLCBub3VzIGRldm9ucyBhaW1lciBjb21tZSBsdWkuXG5cbj4gVW4gbGl2cmUgZXhjZXB0aW9ubmVsIHN1ciB1biBzdWpldCBlc3NlbnRpZWwsIHF1aSBz4oCZYWRyZXNzZSBhdXRhbnQgYXV4IGFkdWx0ZXMgcXXigJlhdXggZW5mYW50czogdW4gdnJhaSBjb3VwIGRlIG1hw650cmUhXG4+XG4+IDxjaXRlPkxhdXJhIFdpZmxlcjwvY2l0ZT5cblxuIyMgMy4gKltDaMOpcmlyIHNhIGZlbW1lXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9jaGVyaXItc2EtZmVtbWUtcmVlZGl0aW9uP19wb3M9MSZfc2lkPWE3YmU0ODY3MyZfc3M9cikqXG5cbiAhW10oaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9DSEVSSVJfU0FfRkVNTUVfMDFfYThlMWE3NDc5My5qcGcpKipBdmV6LXZvdXMgZMOpasOgIGx1IGxlIENhbnRpcXVlIGRlcyBDYW50aXF1ZXM/IEplIHZldXggZGlyZSwgdnJhaW1lbnQgbHU/IFZvdXMgZGV2cmllei4gUGFyY2UgcXVlIMOnYSBwYXJsZSBkZSBzZW5zdWFsaXTDqS4gROKAmcOpcm90aXNtZS4gRGUgc2V4ZS4qKlxuXG5VbiBob21tZSBldCB1bmUgZmVtbWUgcXVpIGTDqXNpcmVudCBwbHVzIHF1ZSB0b3V0IMOqdHJlIGVuc2VtYmxlLiBFdCBxdWkgY8OpbMOoYnJlbnQgbGUgZG9uIGRlIERpZXU6IGxhIHNleHVhbGl0w6kuXG5cblZvdXMgYXVzc2ksIHZvdXMgw6p0ZXMgYXBwZWzDqXMgw6Agdml2cmUgdW5lIHRlbGxlIGNvbW11bmlvbiBkYW5zIHZvdHJlIGNvdXBsZS4gSWwgbmUgc2VyYSBwYXMgaWNpIHF1ZXN0aW9uIGRlIHRlY2huaXF1ZSwgb3UgZGUgcGVyZm9ybWFuY2U7IHBsdXTDtHQgZOKAmXVuZSByZWxhdGlvbiBxdWkgcmVmbMOodGUgY2VsbGUgZGUgSsOpc3VzIGV0IHNvbiDDiWdsaXNlLiBEYW5zIGNlIGxpdnJlLCBwYXMgZOKAmWFzdHVjZXMgcG91ciBwaW1lbnRlciB2b3RyZSB2aWUgc2V4dWVsbGUsIG1haXMgdW5lIGNvbmNlcHRpb24gcmVub3V2ZWzDqWUgZGUgbGEgcmVsYXRpb24gZOKAmWFtb3VyIGVudHJlIMOpcG91eDogbGEgc2V4dWFsaXTDqSBzZWxvbiBEaWV1LlxuXG5BbG9ycywgcmVsZXZleiBsZSBkw6lmaSBkZSByZWNvbnF1w6lyaXIgbGUgY8WTdXIg4oCTZXQgbGUgY29ycHPigJMgZGUgdm90cmUgZmVtbWUhIETDqWNvdXZyZXogY29tbWVudCBnbG9yaWZpZXIgRGlldSDDoCB0cmF2ZXJzIGzigJlpbnRpbWl0w6kgcXVlIHZvdXMgcGFydGFnZXouXG5cbkV0IHBvdXIgZmluaXIsIHVuIG1vdCBkZSAqKkNhcm9seW4gTWFoYW5leSoqIGFkcmVzc8OpIGF1eCDDqXBvdXNlcy5cblxuIyMgNC4gKltTYWNyw6lzIGTDqXNhY2NvcmRzIV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvc2FjcmVzLWRlc2FjY29yZHM/X3Bvcz0xJl9zaWQ9MDViYWI2NjhmJl9zcz1yKSpcblxuICFbXShodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L1NhY3JlX2Rlc2FjY29yZHNfMDJfZjRkMmUwYWMxMi5qcGcpKipRdWVscyBzb250IGxlcyBjcml0w6hyZXMgcG91ciBjaG9pc2lyIHVuZSDDiWdsaXNl4oCmIG91IGxhIHF1aXR0ZXI/XG5Db21tZW50IGfDqXJlciB1biBkw6lzYWNjb3JkIHNhbnMgZGl2aXNlciBs4oCZw4lnbGlzZT9cbkNvbW1lbnQgcsOpYWdpciBmYWNlIMOgIHVuIGNocsOpdGllbiBxdWkgY3JvaXQgZGVzIGNob3NlcyBkaWZmw6lyZW50ZXM/KipcblxuTGUgcGFybGVyIGVuIGxhbmd1ZXMsIGxhIHByb3Bow6l0aWUsIGxlIG1pbmlzdMOocmUgZsOpbWluaW4sIGxlIG1pbGzDqW5pdW0sIGxlIGJhcHTDqm1lLCBsYSBjw6huZSwgbGEgbG91YW5nZSwgbOKAmcOpdmFuZ8OpbGlzYXRpb27igKYgY29tYmllbiBk4oCZw4lnbGlzZXMgb3UgZGUgY3JveWFudHMgc2Ugc29udC1pbHMgZMOpasOgIGTDqWNoaXLDqXMgc3VyIGNlcyBxdWVzdGlvbnM/XG5cbkNvbW1lbnQgcsOpYWdpciBmYWNlIMOgIGNlcyBkw6lzYWNjb3Jkcz8gRG9pdmVudC1pbHMgbm91cyBwb3Vzc2VyIMOgIGRvdXRlciBkZSBsYSBmb2kgZGUgY2VydGFpbnM/IFBldXQtb24gZW5jb3JlIGNvbGxhYm9yZXIgbWFsZ3LDqSBub3MgZGlmZsOpcmVuY2VzP1xuXG5TYWNyw6llcyBxdWVzdGlvbnMsIHNhY3LDqXMgZMOpc2FjY29yZHMhXG5cbkxhIEJpYmxlIGFwcGVsbGUgbGVzIGNocsOpdGllbnMgw6AgYW5ub25jZXIsIGTDqWZlbmRyZSBldCB2aXZyZSBs4oCZw4l2YW5naWxlLiBNYWlzIHRyb3Agc291dmVudCwgbm91cyBub3VzIGxhaXNzb25zIHBpw6lnZXIgcGFyIG5vcyBkaWZmw6lyZW5jZXMgZXQgbm91cyBmcmFnaWxpc29ucyBs4oCZxZN1dnJlIGRlIGzigJnDiXZhbmdpbGUuXG5cbsOAIHRyYXZlcnMgdW5lIG3DqXRob2RlIHNpbXBsZSBldCBhZGFwdMOpZSDDoCBjaGFxdWUgc2l0dWF0aW9uLCAqKkphbWVzIEhlbHkgSHV0Y2hpbnNvbioqIG5vdXMgbW9udHJlIGNvbW1lbnQgbmF2aWd1ZXIgw6AgdHJhdmVycyBub3MgZMOpc2FjY29yZHMgZW4gdHJpYW50IG5vcyBjb252aWN0aW9ucyBzZWxvbiBs4oCZw4l2YW5naWxlLlxuXG5JbCBlc3QgcG9zc2libGUgZGUgdml2cmUgbm9zIGRpZmbDqXJlbmNlcyBkZSBjb252aWN0aW9ucyDDoCBsYSBnbG9pcmUgZGUgRGlldS4gTOKAmcOJdmFuZ2lsZSwgw6dhIGNyw6llIGRlcyBhY2NvcmRzIVxuXG5Wb2ljaSB1biBtb3QgZGUgbOKAmWF1dGV1ciBwb3VyIHZvdXMgZW5jb3VyYWdlciDDoCBsaXJlIGNlIGxpdnJlOlxuXG5gYGBqYXZhc2NyaXB0XG48aWZyYW1lIHdpZHRoPVwiNTYwXCIgaGVpZ2h0PVwiMzE1XCIgc3JjPVwiaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvMVBhRXV6TUthT0lcIiB0aXRsZT1cIllvdVR1YmUgdmlkZW8gcGxheWVyXCIgZnJhbWVib3JkZXI9XCIwXCIgYWxsb3c9XCJhY2NlbGVyb21ldGVyOyBhdXRvcGxheTsgY2xpcGJvYXJkLXdyaXRlOyBlbmNyeXB0ZWQtbWVkaWE7IGd5cm9zY29wZTsgcGljdHVyZS1pbi1waWN0dXJlOyB3ZWItc2hhcmVcIiBhbGxvd2Z1bGxzY3JlZW4+PC9pZnJhbWU+XG5gYGBcblxuXFxcbiMjIDUuICpbVW4gw6lsb2dlIMOgIGxhIHNhZ2Vzc2VdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL3VuLWVsb2dlLWEtbGEtc2FnZXNzZT9fcG9zPTEmX3NpZD05MzQ2OTY2OWYmX3NzPXIpKlxuXG4gIVtdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvVW5fZWxvZ2VfYV9sYV9zYWdlc3NlXzAyXzI4MjQ2NWFmMzAuanBnKSoqTGVzIHBlcnNvbm5lcyDDomfDqWVzIHNvbnQgY29vbCwgZXQgdm91cyBlbiBhdmV6IGJlc29pbiBkYW5zIHZvdHJlIHZpZS4gVW4gamVhbiBtb3VsYW50IGV0IHVuIHRhdG91YWdlIMOgIGxhIG1vZGUgbmUgdm91cyBzdWZmaXJvbnQgcGFzIHNpIHZvdXMgc291aGFpdGV6IGV4ZXJjZXIgdW4gbWluaXN0w6hyZSBmaWTDqGxlIMOgIGxvbmcgdGVybWUuKipcblxuKltVbiDDqWxvZ2Ugw6AgbGEgc2FnZXNzZV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvdW4tZWxvZ2UtYS1sYS1zYWdlc3NlP19wb3M9MSZfc2lkPTkzNDY5NjY5ZiZfc3M9cikqIGVzdCB1biBwbGFpZG95ZXIgcG91ciBkZXMgcmVsYXRpb25zIGRlIG1lbnRvcmF0IGludGVudGlvbm5lbGxlcyBhdSBzZWluIGRlIGzigJnDiWdsaXNlIGxvY2FsZSwgc3DDqWNpYWxlbWVudCBlbnRyZSBsZXMgamV1bmVzLCBxdWkgbuKAmWVuIHNhdmVudCBwYXMgYXV0YW50IHF14oCZaWxzIGxlIHBlbnNlbnQsIGV0IGxlcyBwZXJzb25uZXMgw6Jnw6llcywgcXVpIHBvc3PDqGRlbnQgdW5lIHNhZ2Vzc2UgcXVlIHNldWxlcyBkZXMgYW5uw6llcyBkZSB2aWUgZXQgZOKAmWV4cMOpcmllbmNlIHBlcm1ldHRlbnQgZOKAmWFjcXXDqXJpci5cblxuPiBK4oCZYXVyYWlzIGFpbcOpIHF1ZSBjZSBsaXZyZSBzb2l0IHB1Ymxpw6kgcGx1cyB0w7R0IOKAkyBkaXNvbnMsIGlsIHkgYSB0cmVudGUgYW5zLiBJbCBlc3QgcGV1dC3DqnRyZSBkZSB0YWlsbGUgbW9kZXN0ZSwgbWFpcyBzb24gcG90ZW50aWVsIGRlIGJpZW5mYWl0cyBlc3QgaW5lc3RpbWFibGUuXG4+XG4+IDxjaXRlPkNvbnJhZCBNYmV3ZSwgcGFzdGV1ciwgS2Fid2F0YSBCYXB0aXN0IENodXJjaCwgTHVzYWthLCBaYW1iaWU8L2NpdGU+XG5cbiMjIDYuICpbRGlldSBleGlzdGUtdC1pbCByw6llbGxlbWVudD9dKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL2RpZXUtZXhpc3RlLXQtaWwtcmVlbGxlbWVudC05bWFya3MtcHJlbWllcnMtcGFzP19wb3M9MiZfc2lkPWIwNTk3YWExYyZfc3M9cikqIFxcWzlNYXJrczogUHJlbWllcnMgcGFzXFxdIFxuXG4gIVtdKGh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvRGlldV9leGlzdGVfdF9pbF9yZWVsbGVtZW50XzAyXzJhMjI3NzgzODUuanBnKVxuXG4qKlNpIHZvdXMgYXZleiByw6ljZW1tZW50IGTDqWNvdXZlcnQgbGUgY2hyaXN0aWFuaXNtZSwgb3Ugc2kgdm91cyBlbiBhdmV6IHVuIHBldSBlbnRlbmR1IHBhcmxlciBldCBxdWUgdm91cyBu4oCZYXZleiB0b3Vqb3VycyBwYXMgdHJvdXbDqSBkZSByw6lwb25zZSDDoCBjZXJ0YWluZXMgZ3JhbmRlcyBxdWVzdGlvbnMsIGxhIHPDqXJpZSDigJxQcmVtaWVyIHBhc+KAnSBlc3QgdW4gb3V0aWwgaWTDqWFsIHBvdXIgdm91cyBhaWRlciDDoCBhbGxlciBwbHVzIGxvaW4gZGFucyB2b3RyZSBkw6ltYXJjaGUgc3Bpcml0dWVsbGUuKipcblxuQ29tbWVuw6dvbnMgcGFyIGxhIHF1ZXN0aW9uIGxhIHBsdXMgaW1wb3J0YW50ZSBkZSB0b3V0ZXM6IERpZXUgZXhpc3RlLXQtaWw/IENvbW1lbnQgcG91dm9ucy1ub3VzIHNhdm9pciBz4oCZaWwgZXhpc3RlPyBT4oCZaWwgZXhpc3RlLCBjb21tZW50IGVzdC1pbD8gUXXigJllc3QtY2UgcXVlIGNlbGEgc2lnbmlmaWUgcG91ciBub3VzPyBDZSBwZXRpdCBsaXZyZSB2b3VzIGFpZGVyYSDDoCByw6lmbMOpY2hpciDDoCBjZXMgcXVlc3Rpb25zIGNydWNpYWxlcyBldCByw6lwb25kcmEgw6AgcGx1c2lldXJzIGRlIHZvcyBpbnRlcnJvZ2F0aW9ucy5cblxuKipQb2ludHMgZm9ydHMqKlxuXG4qIFVuIGxpdnJlIGNvdXJ0LCBjbGFpciBldCBwcmF0aXF1ZSBzdXIgbGUgZGlzY2lwdWxhdDtcbiogVW5lIGNvbGxlY3Rpb24gZGUgcGV0aXRzIG91dnJhZ2VzIGRlIHRow6lvbG9naWUgcHJhdGlxdWUgdHLDqHMgYXBwcsOpY2nDqXM7XG4qIExhIGdyYW5kZSBleHDDqXJpZW5jZSBkZSBs4oCZYXV0ZXVyIHN1ciBsYSBmb3JtYXRpb24gZGUgZGlzY2lwbGVzLlxuXG5cXFxuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiU3TDqXBoYW5lIEthcGl0YW5pdWsiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvc3RlcGhhbmVfa2FwaXRhbml1a19ibG9ndHBzZ19lNmUzMWI1ODRkLmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L2Vuc2VtYmxlXzAxXzM2NTg2YmJjMjEuanBnIiwiaGVpZ2h0Ijo0MDAwLCJ3aWR0aCI6NjAwMCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiUmVjZW5zaW9uIGRlIGxpdnJlIn0seyJuYW1lIjoiQWN0dWFsaXTDqSBwZXJzbyJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiQ2VjaSBlc3QgbW9uIHByZW1pZXIgYXJ0aWNsZSBzdXIgbGUgbm91dmVhdSBzaXRlIFRQU0chIE1vbiByw7RsZSDDoCBCTEYgw4lkaXRpb25zIGNvbnRpbnVlIMOgIG1lIHBhc3Npb25uZXIgKGV0IG3igJlvY2N1cGVyKS4gTm91cyBhdm9ucyBwdWJsacOpIDYgbm91dmVhdXTDqXMgZW4gbWFpLCBkb250IHVuIHN1cGVyIGxpdnJlIHBvdXIgZW5mYW50cyBzdXIgbOKAmWFjY3VlaWwgZGUgdG91cyBkYW5zIGzigJnDiWdsaXNlIChiYXPDqSBzdXIgbGUgdGV4dGUgZGUgSmFjcXVlcyAyIGF1IHN1amV0IGRlcyByaWNoZXMpIGV0IHVuZSBzdWl0ZSBhdSBnw6luaWFsIOKAnCpbUXVvaSBxdWUgdHUgZmFzc2VzIGplIHZldXggcXVlIHR1IHNhY2hlc10oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvcXVvaS1xdWUtdHUtZmFzc2VzLWplLXZldXgtcXVlLXR1LXNhY2hlcz8pKuKAnSBkZSBNZWxpc3NhIEtydWdlci4ifSx7fV19LHsiaWQiOiIzNDQwIiwidGl0bGUiOiJEw6ljb3V2cmV6IGxlcyA2IHByw6lkaWNhdGlvbnMgZGUgbGEgY29uZsOpcmVuY2VzIFDDonF1ZXMgSUJHIDIwMjMiLCJzbHVnIjoiZGVjb3V2cmV6LWxlcy02LXByZWRpY2F0aW9ucy1kZS1sYS1jb25mZXJlbmNlcy1wYXF1ZXMtaWJnLTIwMjMiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjMtMDQtMTVUMDM6NTA6MDAuMDAwWiIsImJvZHkiOiJK4oCZYWkgZW50ZW5kdSBiZWF1Y291cCBkZSBiaWVuIHN1ciBsYSBxdWFsaXTDqSBkZXMgcHLDqWRpY2F0aW9ucy4gTWFpcyBjZWxsZSBzdXIgbGEgc291ZmZyYW5jZSBt4oCZYSDDqXTDqSB0b3V0IHBhcnRpY3VsacOocmVtZW50IG1lbnRpb25uw6llOlxuXG5Qb3VyIGNldHRlIMOpZGl0aW9uIGxlIHRow6htZSDDqXRhaXQgwqvCoEZvcnRpZmnDqcKgwrsgZXQgZW52aXJvbiAxMjAwIGpldW5lcyDDqXRhaWVudCByYXNzZW1ibMOpcyBwb3VyIMOpY291dGVyIGxlcyBwbMOpbmnDqHJlcyBkZSBNYXRoaWFzIEh1cnDDqSwgQmVuamFtaW4gRWdnZW4sIFJhcGhhw6tsIENoYXJyaWVyLCBEYXZpZCBOaWJsYWNrLCBNYW51IFJlbmFyZCBldCBEYXZpZCBTYXV0ZWwuXG5cbkxlcyB2b2ljaSBlbiBpbnTDqWdyYWxpdMOpIGV0IGdyYXR1aXRlbWVudDpcblxuYDxpZnJhbWUgbG9hZGluZz1cImxhenlcIiBjbGFzcz1cInlvdXR1YmUtcGxheWVyXCIgd2lkdGg9XCI2NDBcIiBoZWlnaHQ9XCIzNjBcIiBzcmM9XCJodHRwczovL3d3dy55b3V0dWJlLmNvbS9lbWJlZC9DQk4wRmZfNjFkcz92ZXJzaW9uPTMmYW1wO3JlbD0xJmFtcDtzaG93c2VhcmNoPTAmYW1wO3Nob3dpbmZvPTEmYW1wO2l2X2xvYWRfcG9saWN5PTEmYW1wO2ZzPTEmYW1wO2hsPWZyLUZSJmFtcDthdXRvaGlkZT0yJmFtcDt3bW9kZT10cmFuc3BhcmVudFwiIGFsbG93ZnVsbHNjcmVlbj1cInRydWVcIiBzdHlsZT1cImJvcmRlcjowO1wiIHNhbmRib3g9XCJhbGxvdy1zY3JpcHRzIGFsbG93LXNhbWUtb3JpZ2luIGFsbG93LXBvcHVwcyBhbGxvdy1wcmVzZW50YXRpb25cIj48L2lmcmFtZT5gXG5cbmA8aWZyYW1lIGxvYWRpbmc9XCJsYXp5XCIgY2xhc3M9XCJ5b3V0dWJlLXBsYXllclwiIHdpZHRoPVwiNjQwXCIgaGVpZ2h0PVwiMzYwXCIgc3JjPVwiaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvNC14NUh0eDVZSG8/dmVyc2lvbj0zJmFtcDtyZWw9MSZhbXA7c2hvd3NlYXJjaD0wJmFtcDtzaG93aW5mbz0xJmFtcDtpdl9sb2FkX3BvbGljeT0xJmFtcDtmcz0xJmFtcDtobD1mci1GUiZhbXA7YXV0b2hpZGU9MiZhbXA7d21vZGU9dHJhbnNwYXJlbnRcIiBhbGxvd2Z1bGxzY3JlZW49XCJ0cnVlXCIgc3R5bGU9XCJib3JkZXI6MDtcIiBzYW5kYm94PVwiYWxsb3ctc2NyaXB0cyBhbGxvdy1zYW1lLW9yaWdpbiBhbGxvdy1wb3B1cHMgYWxsb3ctcHJlc2VudGF0aW9uXCI+PC9pZnJhbWU+YFxuXG5gPGlmcmFtZSBsb2FkaW5nPVwibGF6eVwiIGNsYXNzPVwieW91dHViZS1wbGF5ZXJcIiB3aWR0aD1cIjY0MFwiIGhlaWdodD1cIjM2MFwiIHNyYz1cImh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL2VtYmVkL1B4N21pRDdkVnNBP3ZlcnNpb249MyZhbXA7cmVsPTEmYW1wO3Nob3dzZWFyY2g9MCZhbXA7c2hvd2luZm89MSZhbXA7aXZfbG9hZF9wb2xpY3k9MSZhbXA7ZnM9MSZhbXA7aGw9ZnItRlImYW1wO2F1dG9oaWRlPTImYW1wO3dtb2RlPXRyYW5zcGFyZW50XCIgYWxsb3dmdWxsc2NyZWVuPVwidHJ1ZVwiIHN0eWxlPVwiYm9yZGVyOjA7XCIgc2FuZGJveD1cImFsbG93LXNjcmlwdHMgYWxsb3ctc2FtZS1vcmlnaW4gYWxsb3ctcG9wdXBzIGFsbG93LXByZXNlbnRhdGlvblwiPjwvaWZyYW1lPmBcblxuYDxpZnJhbWUgbG9hZGluZz1cImxhenlcIiBjbGFzcz1cInlvdXR1YmUtcGxheWVyXCIgd2lkdGg9XCI2NDBcIiBoZWlnaHQ9XCIzNjBcIiBzcmM9XCJodHRwczovL3d3dy55b3V0dWJlLmNvbS9lbWJlZC9yZXNvWVo5MUQyWT92ZXJzaW9uPTMmYW1wO3JlbD0xJmFtcDtzaG93c2VhcmNoPTAmYW1wO3Nob3dpbmZvPTEmYW1wO2l2X2xvYWRfcG9saWN5PTEmYW1wO2ZzPTEmYW1wO2hsPWZyLUZSJmFtcDthdXRvaGlkZT0yJmFtcDt3bW9kZT10cmFuc3BhcmVudFwiIGFsbG93ZnVsbHNjcmVlbj1cInRydWVcIiBzdHlsZT1cImJvcmRlcjowO1wiIHNhbmRib3g9XCJhbGxvdy1zY3JpcHRzIGFsbG93LXNhbWUtb3JpZ2luIGFsbG93LXBvcHVwcyBhbGxvdy1wcmVzZW50YXRpb25cIj48L2lmcmFtZT5gXG5cbmA8aWZyYW1lIGxvYWRpbmc9XCJsYXp5XCIgY2xhc3M9XCJ5b3V0dWJlLXBsYXllclwiIHdpZHRoPVwiNjQwXCIgaGVpZ2h0PVwiMzYwXCIgc3JjPVwiaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvbW1zSlJqRzloWW8/dmVyc2lvbj0zJmFtcDtyZWw9MSZhbXA7c2hvd3NlYXJjaD0wJmFtcDtzaG93aW5mbz0xJmFtcDtpdl9sb2FkX3BvbGljeT0xJmFtcDtmcz0xJmFtcDtobD1mci1GUiZhbXA7YXV0b2hpZGU9MiZhbXA7d21vZGU9dHJhbnNwYXJlbnRcIiBhbGxvd2Z1bGxzY3JlZW49XCJ0cnVlXCIgc3R5bGU9XCJib3JkZXI6MDtcIiBzYW5kYm94PVwiYWxsb3ctc2NyaXB0cyBhbGxvdy1zYW1lLW9yaWdpbiBhbGxvdy1wb3B1cHMgYWxsb3ctcHJlc2VudGF0aW9uXCI+PC9pZnJhbWU+YFxuXG5gPGlmcmFtZSBsb2FkaW5nPVwibGF6eVwiIGNsYXNzPVwieW91dHViZS1wbGF5ZXJcIiB3aWR0aD1cIjY0MFwiIGhlaWdodD1cIjM2MFwiIHNyYz1cImh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL2VtYmVkL3JpeUZzWkVHaHBRP3ZlcnNpb249MyZhbXA7cmVsPTEmYW1wO3Nob3dzZWFyY2g9MCZhbXA7c2hvd2luZm89MSZhbXA7aXZfbG9hZF9wb2xpY3k9MSZhbXA7ZnM9MSZhbXA7aGw9ZnItRlImYW1wO2F1dG9oaWRlPTImYW1wO3dtb2RlPXRyYW5zcGFyZW50XCIgYWxsb3dmdWxsc2NyZWVuPVwidHJ1ZVwiIHN0eWxlPVwiYm9yZGVyOjA7XCIgc2FuZGJveD1cImFsbG93LXNjcmlwdHMgYWxsb3ctc2FtZS1vcmlnaW4gYWxsb3ctcG9wdXBzIGFsbG93LXByZXNlbnRhdGlvblwiPjwvaWZyYW1lPmBcblxuICBcbk1BOiBodHRwczovL3d3dy5pYmcuY2MvcGFxdWVzLWliZy8iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJTdMOpcGhhbmUgS2FwaXRhbml1ayIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9zdGVwaGFuZV9rYXBpdGFuaXVrX2Jsb2d0cHNnX2U2ZTMxYjU4NGQuanBnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvQ2FwdHVyZV9kZWNyYW5fMjAyM18wNF8xNF9hXzIyXzU5XzEzX2UxNjgxODA3OTMwNjE1XzJjMzQ1Nzg5Y2EucG5nIiwiaGVpZ2h0Ijo2NzEsIndpZHRoIjoxMTAwLCJhbHRlcm5hdGl2ZVRleHQiOm51bGwsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IlDDonF1ZXMifSx7Im5hbWUiOiJDb25mw6lyZW5jZSJ9XSwibW9kdWxlcyI6W3t9LHsiX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiTmUgcmF0ZXogcGFzIGxlIHJlcGxheSBkZSBsYSBncmFuZGUgY29uZsOpcmVuY2UgcG91ciBqZXVuZXMgUMOicXVlcyBJQkcgMjAyMy4gTGVzIG1lc3NhZ2VzIGRlcyA2IHBsw6luacOocmVzIHZpZW5uZW50IGQnw6p0cmUgcHVibGnDqXMgc3VyIFlvdVR1YmUuXG4ifV19XX19Cg==", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}