"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/categories/vocation/[vocation]",{

/***/ "./components/shared/Featured.js":
/*!***************************************!*\
  !*** ./components/shared/Featured.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Featured; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _utils_image_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/image-utils */ \"./utils/image-utils.js\");\n/* harmony import */ var _atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./atoms/Buttons/BigCta */ \"./components/shared/atoms/Buttons/BigCta.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  padding: 0 var(--border-space);\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  column-gap: 24px;\\n  padding-bottom: 80px;\\n  width: 100%;\\n  min-height: 400px;\\n  background-color: \",\n        ';\\n  z-index: 100;\\n\\n  .fw-featured-author {\\n    font-family: \"Lora\", sans-serif;\\n    font-style: italic;\\n    letter-spacing: 1px;\\n    opacity: 0.4;\\n  }\\n\\n  .fw-featured-image {\\n    position: relative;\\n    width: 100%;\\n    aspect-ratio: 1/1;\\n    grid-column: 1/5;\\n\\n    img {\\n      object-fit: cover;\\n    }\\n  }\\n\\n  .text-content {\\n    position: relative;\\n    grid-column: 1/5;\\n    color: ',\n        \";\\n    background-color: \",\n        \";\\n  }\\n\\n  .fw-featured-type {\\n    font-family: Switzer, Arial, sans-serif;\\n    opacity: 0.48;\\n    margin-top: 48px;\\n    margin-bottom: 56px;\\n    font-size: 16px;\\n    font-weight: 400;\\n    text-transform: uppercase;\\n    letter-spacing: 0.02em;\\n  }\\n\\n  .fw-featured-title {\\n    margin-top: 8px;\\n    margin-bottom: 0;\\n    font-size: 30px;\\n    font-weight: 500;\\n    line-height: 95%;\\n  }\\n\\n  .fw-featured-lead {\\n    margin-top: 8px;\\n    font-size: 17px;\\n    margin-right: 32px;\\n  }\\n\\n  .fw-featured-buttons {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  @media \",\n        \" {\\n    flex-direction: row;\\n    min-height: 400px;\\n    padding-bottom: 96px;\\n\\n    .fw-featured-image {\\n      position: relative;\\n      grid-column: 1/3;\\n    }\\n\\n    .text-content {\\n      margin-top: 0;\\n      margin-left: 32px;\\n      grid-column: 3/5;\\n      display: flex;\\n      flex-direction: column;\\n      justify-content: space-between;\\n    }\\n\\n    .fw-featured-type {\\n      font-size: 20px;\\n      margin-top: 46px;\\n    }\\n\\n    .fw-featured-title {\\n      font-size: 46px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-lead {\\n      font-familly: Switzer, sans-serif;\\n      font-size: 20px;\\n      font-weight: 400;\\n      opacity: 0.72;\\n      //font-size: 18px;\\n      margin-top: 24px;\\n    }\\n\\n    .fw-featured-buttons {\\n      display: flex;\\n      flex-direction: row;\\n      gap: 24px;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n// import { getPostRoute } from \"utils/posts.utils\";\n// import AnimatedTextButton from \"./Buttons/AnimatedTextButton\";\n\n\nfunction Featured(param) {\n    let { content } = param;\n    // Debug: Log the content to see what data we're receiving\n    console.log(\"Featured component - content data:\", content);\n    console.log(\"Featured component - type field:\", content === null || content === void 0 ? void 0 : content.type);\n    const { image, title, description, cta = null, cta2 = null, color, type: rawType, postRef, route, _formatted// From CornerStone\n     } = content;\n    // Fix for corrupted type field: if type contains author name instead of content type, use default\n    const validContentTypes = [\n        \"article\",\n        \"podcast\",\n        \"video\",\n        \"webinar\",\n        \"livre\",\n        \"formation\"\n    ];\n    const isValidType = validContentTypes.includes(rawType === null || rawType === void 0 ? void 0 : rawType.toLowerCase());\n    // If type is invalid (contains author name), use fallback logic\n    let type;\n    if (!isValidType && rawType) {\n        console.log(\"⚠️ Invalid type detected:\", rawType, \"- using fallback\");\n        // Default to 'article' for featured content\n        type = \"ARTICLE\";\n    } else if (rawType) {\n        // Convert to uppercase for display\n        type = rawType.toUpperCase();\n    } else {\n        // No type provided, default to article\n        type = \"ARTICLE\";\n    }\n    console.log(\"Featured component - final type for display:\", type);\n    console.log(\"Featured component - timestamp:\", Date.now()); // Force refresh\n    const link = (cta === null || cta === void 0 ? void 0 : cta.url) || ((route === null || route === void 0 ? void 0 : route.startsWith(\"/\")) ? route : \"/\" + route);\n    const link2 = (cta2 === null || cta2 === void 0 ? void 0 : cta2.url) || null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Section, {\n        backgroundColor: color === null || color === void 0 ? void 0 : color.background,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"fw-featured-image\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: (0,_utils_image_utils__WEBPACK_IMPORTED_MODULE_4__.withRealSrc)(image),\n                    fill: true,\n                    priority: true,\n                    alt: \"\",\n                    sizes: \"50vw\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                        className: \"fw-featured-type\",\n                        children: type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(PostAuthor, {\n                                post: postRef,\n                                content: content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                                className: \"fw-featured-title\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                className: \"fw-featured-lead\",\n                                children: description || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.lead) || (_formatted === null || _formatted === void 0 ? void 0 : _formatted.body)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"fw-featured-buttons\",\n                                children: [\n                                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta === null || cta === void 0 ? void 0 : cta.name) || \"D\\xe9couvrir\",\n                                        link: link,\n                                        outline: (cta === null || cta === void 0 ? void 0 : cta.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    link2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (cta2 === null || cta2 === void 0 ? void 0 : cta2.name) || \"D\\xe9couvrir\",\n                                        link: link2,\n                                        outline: (cta2 === null || cta2 === void 0 ? void 0 : cta2.outline) || false,\n                                        theme: \"light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = Featured;\nconst PostAuthor = (param)=>{\n    let { post, content } = param;\n    var _post_author;\n    // Logique de fallback simple - les données sont maintenant enrichies côté serveur\n    let authorName = null;\n    if (post === null || post === void 0 ? void 0 : (_post_author = post.author) === null || _post_author === void 0 ? void 0 : _post_author.fullName) {\n        // Données Strapi via postRef (featured personnalisés)\n        authorName = post.author.fullName;\n    } else if (post === null || post === void 0 ? void 0 : post.author) {\n        // Données Strapi via postRef mais author est une string\n        authorName = post.author;\n    } else if (content === null || content === void 0 ? void 0 : content.author) {\n        // Données Meilisearch (cornerStones) OU featured enrichi côté serveur\n        authorName = content.author;\n    }\n    if (!authorName) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"fw-featured-author\",\n        children: authorName\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\Featured.js\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PostAuthor;\nconst Section = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"Featured__Section\",\n    componentId: \"sc-b9c56640-0\"\n})(_templateObject(), (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", (p)=>p.color ? p.color : \"var(--c-soft-cream)\", (p)=>p.backgroundColor ? p.backgroundColor : \"var(--c-dark-green)\", styles_device__WEBPACK_IMPORTED_MODULE_3__.device.tablet);\n_c2 = Section;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Featured\");\n$RefreshReg$(_c1, \"PostAuthor\");\n$RefreshReg$(_c2, \"Section\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/Featured.js\n"));

/***/ })

});