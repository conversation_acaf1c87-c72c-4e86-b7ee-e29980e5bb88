import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import styled from "styled-components";
import { Featured } from "components/shared";
import { device } from "styles/device";
import { FilterTopicsString } from "utils/filterSearchString";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import CornerStoneCard from "components/shared/Card/CornerStoneCard";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import { topicsPostsfetcher } from "utils/fetcher";
import CategoriesHeader from "components/shared/categories/CategoriesHeader";
import { MeiliA<PERSON> } from "api/meili-client";

const postPerPage = 15; // Number items per page

export default function PageMinistry({ ministry, fallback }) {

  const nbHits = fallback?.posts?.totalHits || 0;
  const posts = fallback?.posts?.hits;
  const cornerStonesFeatured = fallback?.cornerStonesFeatured || []

  // DEBUG: Log des données reçues
  console.log('🔍 PAGE MINISTRY DATA:', {
    ministrySlug: ministry?.slug,
    postsCount: posts?.length,
    cornerStonesCount: cornerStonesFeatured?.length,
    firstPost: posts?.[0],
    firstCornerStone: cornerStonesFeatured?.[0],
    secondCornerStone: cornerStonesFeatured?.[1],
    thirdCornerStone: cornerStonesFeatured?.[2]
  });

  if (!ministry) return null;

  return (
    <PageWrapper>
      <CategoriesHeader category={ministry} type={"ministère"}/>
      <MainContent>
        <section>
          {cornerStonesFeatured[0] && (
            <Featured content={cornerStonesFeatured[0]}/>
          )}
        </section>
        <SectionPosts className={"site-padding"}>
          <p className="label-type">{posts?.length > 0 ? "Dernières ressources" : ""}</p>
          <div className="posts-container">
            <LeftContent>
              <ul className={"list-container"}>
                {posts?.map((post, key) => {
                  return (
                    <li key={`post-${key}`} className={"post-card-li"}>
                      <HorizontalReversePostCard
                        post={post}
                        options={{
                          showLead: true,
                          showDate: true,
                          showAuthor: true
                        }}
                      />
                    </li>
                  );
                })}
              </ul>
              <SSRPaginate
                nbHits={nbHits}
                baseUrl={`/categories/ministere/${ministry.slug}/ressources?page=`}
                currentPage={1}
                options={{
                  postPerPage: postPerPage,
                }}
              />
            </LeftContent>
            <RightContent>
              <div className="cornerstone-container">
                {cornerStonesFeatured[1] &&
                  <CornerStoneCard
                    post={cornerStonesFeatured[1]}
                    options={{
                      showAuthor: true,
                      showBlur: true,
                      aspectRatio: 16 / 9
                    }}
                  />
                }
                {cornerStonesFeatured[2] &&
                  <CornerStoneCard
                    post={cornerStonesFeatured[2]}
                    options={{
                      showAuthor: true,
                      showBlur: true,
                      aspectRatio: 16 / 9
                    }}
                  />
                }
              </div>
            </RightContent>
          </div>
        </SectionPosts>

        <section>
          {cornerStonesFeatured[3] && (
            <Featured content={cornerStonesFeatured[3]}/>
          )}
        </section>
      </MainContent>
    </PageWrapper>
  );
}

export async function getStaticProps({ params }) {

  const ministry = await client.query({
    query: MINISTRY_QUERY,
    variables: { slug: params.ministry }
  }).then(response => {
    return response.data.topicGroups[0]
  })

  if (!ministry) {
    return {
      notFound: true,
    };
  }


  // Put all topic into an array
  let topics = [];

  ministry?.parent?.children && topics.push(...ministry.children.topics)
  ministry?.topics && topics.push(...ministry.topics)

  // Clear duplicate topics
  topics = topics.filter(
    (topic, index, self) =>
      self.findIndex((topic2) => topic2.id === topic.id) === index
  );

  //fetch posts
  const filterTopicsString = FilterTopicsString(topics);
  let posts = []
  let cornerStonesFeatured = [null, null, null, null]
  if (filterTopicsString.length > 0) {
    posts = await topicsPostsfetcher({}, filterTopicsString, postPerPage);
    //fetch corner stone for compleate featured data
    const filterTopicsAnd = filterTopicsString.length > 0 ? " AND " : "";
    const filterTopicsStringCornerStone =
      `cs=true ${filterTopicsAnd} (${filterTopicsString})`;
    const cornerStones = await MeiliApi.searchHighlight("", {
      filter: filterTopicsStringCornerStone,
      sort: ["date:desc"],
      limit: 4
    });

    const fullFeatured = ministry?.featured?.filter((f) => !f.inColumn);
    const columnFeatured = ministry?.featured?.filter((f) => f.inColumn);

    // Remplace les CornerStones par les Featured si ils existent.
    cornerStonesFeatured[0] = fullFeatured[0] ? fullFeatured[0] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;
    cornerStonesFeatured[1] = columnFeatured[0] ? columnFeatured[0] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;
    cornerStonesFeatured[2] = columnFeatured[1] ? columnFeatured[1] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;
    cornerStonesFeatured[3] = fullFeatured[1] ? fullFeatured[1] : cornerStones?.hits[0] ? cornerStones?.hits.shift() : null;

    // Supprime les posts qui on les mêmes route que les cornerstones et featured
    posts.hits = posts?.hits?.filter(post => !cornerStonesFeatured?.find(csfeatured => csfeatured?.route === post?.route || csfeatured?.cta?.url === post?.route));
  }

  return {
    props: {
      ministry,
      fallback: {
        posts,
        cornerStonesFeatured
      },
    },
    revalidate: 10,
  };
}

export async function getStaticPaths() {
  const ministries = await client.query({
    query: SLUG_QUERY,
    variables: { type: "ministere" }
  }).then(response => {
    return response.data.topicGroups
  })

  const paths = ministries.map((ministry) => ( {
    params: {
      ministry: ministry.slug,
    }
  } ))

  return {
    paths: paths,
    fallback: true,
  }
}

const SLUG_QUERY = gql`
    query Ministries($type: String!){
        topicGroups(where: { type: $type }){
            slug
        }
    }
`
const MINISTRY_QUERY = gql`
    query Ministries($slug: String!) {
        topicGroups(where: { slug: $slug} ){
            name
            slug
            description
            cover {
                formats
            }
            topics {
                id
                name
                postCount
            }
            children {
                id
                name
                slug
                topics {
                    id
                    name
                    postCount
                }
            }
            featured {
                title
                description
                inColumn
                image {
                    url
                    height
                    width
                    alternativeText
                    provider
                }
                cta {
                    name
                    url
                }
                color {
                    foreground
                    background
                }
                type
                postRef {
                    author {
                        fullName
                    }
                }
            }
        }
    }
`;

const SectionPosts = styled.section`
  margin-top: 48px;
  .posts-container {
    display: block;
  }
  @media ${device.desktop} {
    margin-top: 96px;
    .posts-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`;
const LeftContent = styled.article`
  width: 100%;
  margin-bottom: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }

  .post-card-li {
    list-style: none;
    padding-right: 0;
  }

  @media ${device.desktop} {
    width: 66.7%;
    margin-bottom: 164px;
    .post-card-li {
      padding-right: 142px;
    }
  }
`;
const RightContent = styled.div`
  position: relative;
  width: 100%;

  .cornerstone-container {
    /* position: sticky;
    top: 60px; */
  }

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }

  @media ${device.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${device.desktop} {
    width: 33.3%;
  }
`;

const PageWrapper = styled.div`
  .label-type {
    font-size: 20px;
    font-family: Stelvio, sans-serif;
    margin: 0 0 16px 0;

    font-weight: 500;
    letter-spacing: 4%;
    line-height: 32px;
  }
`;
const MainContent = styled.div`
  margin-top: 0;
`;