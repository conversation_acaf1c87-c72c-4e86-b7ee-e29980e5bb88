/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/debug-authors"],{

/***/ "./node_modules/cross-fetch/dist/browser-polyfill.js":
/*!***********************************************************!*\
  !*** ./node_modules/cross-fetch/dist/browser-polyfill.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("(function(self) {\n\nvar irrelevant = (function (exports) {\n\n  var support = {\n    searchParams: 'URLSearchParams' in self,\n    iterable: 'Symbol' in self && 'iterator' in Symbol,\n    blob:\n      'FileReader' in self &&\n      'Blob' in self &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in self,\n    arrayBuffer: 'ArrayBuffer' in self\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~]/i.test(name)) {\n      throw new TypeError('Invalid character in header field name')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsText(blob);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      this._bodyInit = body;\n      if (!body) {\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          return consumed(this) || Promise.resolve(this._bodyArrayBuffer)\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      };\n    }\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal;\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    preProcessedHeaders.split(/\\r?\\n/).forEach(function(line) {\n      var parts = line.split(':');\n      var key = parts.shift().trim();\n      if (key) {\n        var value = parts.join(':').trim();\n        headers.append(key, value);\n      }\n    });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = 'statusText' in options ? options.statusText : 'OK';\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''});\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = self.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        resolve(new Response(body, options));\n      };\n\n      xhr.onerror = function() {\n        reject(new TypeError('Network request failed'));\n      };\n\n      xhr.ontimeout = function() {\n        reject(new TypeError('Network request failed'));\n      };\n\n      xhr.onabort = function() {\n        reject(new exports.DOMException('Aborted', 'AbortError'));\n      };\n\n      xhr.open(request.method, request.url, true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr && support.blob) {\n        xhr.responseType = 'blob';\n      }\n\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value);\n      });\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!self.fetch) {\n    self.fetch = fetch;\n    self.Headers = Headers;\n    self.Request = Request;\n    self.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n})({});\n})(typeof self !== 'undefined' ? self : this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/cross-fetch/dist/browser-polyfill.js\n"));

/***/ }),

/***/ "./node_modules/meilisearch/dist/bundles/meilisearch.umd.js":
/*!******************************************************************!*\
  !*** ./node_modules/meilisearch/dist/bundles/meilisearch.umd.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("(function (global, factory) {\n   true ? factory(exports, __webpack_require__(/*! cross-fetch/polyfill */ \"./node_modules/cross-fetch/dist/browser-polyfill.js\")) :\n  0;\n})(this, (function (exports) { 'use strict';\n\n  // Type definitions for meilisearch\n  // Project: https://github.com/meilisearch/meilisearch-js\n  // Definitions by: qdequele <<EMAIL>> <https://github.com/meilisearch>\n  // Definitions: https://github.com/meilisearch/meilisearch-js\n  // TypeScript Version: ^3.8.3\n\n  /*\r\n   * SEARCH PARAMETERS\r\n   */\n  var MatchingStrategies = {\n    ALL: 'all',\n    LAST: 'last'\n  };\n\n  /******************************************************************************\r\n  Copyright (c) Microsoft Corporation.\r\n\r\n  Permission to use, copy, modify, and/or distribute this software for any\r\n  purpose with or without fee is hereby granted.\r\n\r\n  THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n  PERFORMANCE OF THIS SOFTWARE.\r\n  ***************************************************************************** */\r\n  /* global Reflect, Promise */\r\n\r\n  var extendStatics = function(d, b) {\r\n      extendStatics = Object.setPrototypeOf ||\r\n          ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n          function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n      return extendStatics(d, b);\r\n  };\r\n\r\n  function __extends(d, b) {\r\n      if (typeof b !== \"function\" && b !== null)\r\n          throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n      extendStatics(d, b);\r\n      function __() { this.constructor = d; }\r\n      d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n  }\r\n\r\n  var __assign = function() {\r\n      __assign = Object.assign || function __assign(t) {\r\n          for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n              s = arguments[i];\r\n              for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n          }\r\n          return t;\r\n      };\r\n      return __assign.apply(this, arguments);\r\n  };\r\n\r\n  function __awaiter(thisArg, _arguments, P, generator) {\r\n      function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n      return new (P || (P = Promise))(function (resolve, reject) {\r\n          function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n          function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n          function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n          step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n      });\r\n  }\r\n\r\n  function __generator(thisArg, body) {\r\n      var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n      return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n      function verb(n) { return function (v) { return step([n, v]); }; }\r\n      function step(op) {\r\n          if (f) throw new TypeError(\"Generator is already executing.\");\r\n          while (_) try {\r\n              if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n              if (y = 0, t) op = [op[0] & 2, t.value];\r\n              switch (op[0]) {\r\n                  case 0: case 1: t = op; break;\r\n                  case 4: _.label++; return { value: op[1], done: false };\r\n                  case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                  case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                  default:\r\n                      if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                      if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                      if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                      if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                      if (t[2]) _.ops.pop();\r\n                      _.trys.pop(); continue;\r\n              }\r\n              op = body.call(thisArg, _);\r\n          } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n          if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n      }\r\n  }\n\n  var MeiliSearchCommunicationError =\n  /** @class */\n  function (_super) {\n    __extends(MeiliSearchCommunicationError, _super);\n\n    function MeiliSearchCommunicationError(message, body, url, stack) {\n      var _this = this;\n\n      var _a, _b, _c;\n\n      _this = _super.call(this, message) || this; // Make errors comparison possible. ex: error instanceof MeiliSearchCommunicationError.\n\n      Object.setPrototypeOf(_this, MeiliSearchCommunicationError.prototype);\n      _this.name = 'MeiliSearchCommunicationError';\n\n      if (body instanceof Response) {\n        _this.message = body.statusText;\n        _this.statusCode = body.status;\n      }\n\n      if (body instanceof Error) {\n        _this.errno = body.errno;\n        _this.code = body.code;\n      }\n\n      if (stack) {\n        _this.stack = stack;\n        _this.stack = (_a = _this.stack) === null || _a === void 0 ? void 0 : _a.replace(/(TypeError|FetchError)/, _this.name);\n        _this.stack = (_b = _this.stack) === null || _b === void 0 ? void 0 : _b.replace('Failed to fetch', \"request to \".concat(url, \" failed, reason: connect ECONNREFUSED\"));\n        _this.stack = (_c = _this.stack) === null || _c === void 0 ? void 0 : _c.replace('Not Found', \"Not Found: \".concat(url));\n      } else {\n        if (Error.captureStackTrace) {\n          Error.captureStackTrace(_this, MeiliSearchCommunicationError);\n        }\n      }\n\n      return _this;\n    }\n\n    return MeiliSearchCommunicationError;\n  }(Error);\n\n  var MeiliSearchApiError =\n  /** @class */\n  function (_super) {\n    __extends(class_1, _super);\n\n    function class_1(error, status) {\n      var _this = _super.call(this, error.message) || this; // Make errors comparison possible. ex: error instanceof MeiliSearchApiError.\n\n\n      Object.setPrototypeOf(_this, MeiliSearchApiError.prototype);\n      _this.name = 'MeiliSearchApiError';\n      _this.code = error.code;\n      _this.type = error.type;\n      _this.link = error.link;\n      _this.message = error.message;\n      _this.httpStatus = status;\n\n      if (Error.captureStackTrace) {\n        Error.captureStackTrace(_this, MeiliSearchApiError);\n      }\n\n      return _this;\n    }\n\n    return class_1;\n  }(Error);\n\n  function httpResponseErrorHandler(response) {\n    return __awaiter(this, void 0, void 0, function () {\n      var responseBody;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (!!response.ok) return [3\n            /*break*/\n            , 5];\n            responseBody = void 0;\n            _a.label = 1;\n\n          case 1:\n            _a.trys.push([1, 3,, 4]);\n\n            return [4\n            /*yield*/\n            , response.json()];\n\n          case 2:\n            // If it is not possible to parse the return body it means there is none\n            // In which case it is a communication error with the Meilisearch instance\n            responseBody = _a.sent();\n            return [3\n            /*break*/\n            , 4];\n\n          case 3:\n            _a.sent(); // Not sure on how to test this part of the code.\n\n            throw new MeiliSearchCommunicationError(response.statusText, response, response.url);\n\n          case 4:\n            // If the body is parsable, then it means Meilisearch returned a body with\n            // information on the error.\n            throw new MeiliSearchApiError(responseBody, response.status);\n\n          case 5:\n            return [2\n            /*return*/\n            , response];\n        }\n      });\n    });\n  }\n\n  function httpErrorHandler(response, stack, url) {\n    if (response.name !== 'MeiliSearchApiError') {\n      throw new MeiliSearchCommunicationError(response.message, response, url, stack);\n    }\n\n    throw response;\n  }\n\n  var MeiliSearchError =\n  /** @class */\n  function (_super) {\n    __extends(MeiliSearchError, _super);\n\n    function MeiliSearchError(message) {\n      var _this = _super.call(this, message) || this; // Make errors comparison possible. ex: error instanceof MeiliSearchError.\n\n\n      Object.setPrototypeOf(_this, MeiliSearchError.prototype);\n      _this.name = 'MeiliSearchError';\n\n      if (Error.captureStackTrace) {\n        Error.captureStackTrace(_this, MeiliSearchError);\n      }\n\n      return _this;\n    }\n\n    return MeiliSearchError;\n  }(Error);\n\n  var MeiliSearchTimeOutError =\n  /** @class */\n  function (_super) {\n    __extends(MeiliSearchTimeOutError, _super);\n\n    function MeiliSearchTimeOutError(message) {\n      var _this = _super.call(this, message) || this; // Make errors comparison possible. ex: error instanceof MeiliSearchTimeOutError.\n\n\n      Object.setPrototypeOf(_this, MeiliSearchTimeOutError.prototype);\n      _this.name = 'MeiliSearchTimeOutError';\n\n      if (Error.captureStackTrace) {\n        Error.captureStackTrace(_this, MeiliSearchTimeOutError);\n      }\n\n      return _this;\n    }\n\n    return MeiliSearchTimeOutError;\n  }(Error);\n\n  /**\r\n   * Removes undefined entries from object\r\n   */\n\n  function removeUndefinedFromObject(obj) {\n    return Object.entries(obj).reduce(function (acc, curEntry) {\n      var key = curEntry[0],\n          val = curEntry[1];\n      if (val !== undefined) acc[key] = val;\n      return acc;\n    }, {});\n  }\n\n  function sleep(ms) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4\n            /*yield*/\n            , new Promise(function (resolve) {\n              return setTimeout(resolve, ms);\n            })];\n\n          case 1:\n            return [2\n            /*return*/\n            , _a.sent()];\n        }\n      });\n    });\n  }\n\n  function addProtocolIfNotPresent(host) {\n    if (!(host.startsWith('https://') || host.startsWith('http://'))) {\n      return \"http://\".concat(host);\n    }\n\n    return host;\n  }\n\n  function addTrailingSlash(url) {\n    if (!url.endsWith('/')) {\n      url += '/';\n    }\n\n    return url;\n  }\n\n  var PACKAGE_VERSION = '0.30.0';\n\n  function toQueryParams(parameters) {\n    var params = Object.keys(parameters);\n    var queryParams = params.reduce(function (acc, key) {\n      var _a, _b, _c;\n\n      var value = parameters[key];\n\n      if (value === undefined) {\n        return acc;\n      } else if (Array.isArray(value)) {\n        return __assign(__assign({}, acc), (_a = {}, _a[key] = value.join(','), _a));\n      } else if (value instanceof Date) {\n        return __assign(__assign({}, acc), (_b = {}, _b[key] = value.toISOString(), _b));\n      }\n\n      return __assign(__assign({}, acc), (_c = {}, _c[key] = value, _c));\n    }, {});\n    return queryParams;\n  }\n\n  function constructHostURL(host) {\n    try {\n      host = addProtocolIfNotPresent(host);\n      host = addTrailingSlash(host);\n      return host;\n    } catch (e) {\n      throw new MeiliSearchError('The provided host is not valid.');\n    }\n  }\n\n  function createHeaders(config) {\n    var agentHeader = 'X-Meilisearch-Client';\n    var packageAgent = \"Meilisearch JavaScript (v\".concat(PACKAGE_VERSION, \")\");\n    var contentType = 'Content-Type';\n    config.headers = config.headers || {};\n    var headers = Object.assign({}, config.headers); // Create a hard copy and not a reference to config.headers\n\n    if (config.apiKey) {\n      headers['Authorization'] = \"Bearer \".concat(config.apiKey);\n    }\n\n    if (!config.headers[contentType]) {\n      headers['Content-Type'] = 'application/json';\n    } // Creates the custom user agent with information on the package used.\n\n\n    if (config.clientAgents && Array.isArray(config.clientAgents)) {\n      var clients = config.clientAgents.concat(packageAgent);\n      headers[agentHeader] = clients.join(' ; ');\n    } else if (config.clientAgents && !Array.isArray(config.clientAgents)) {\n      // If the header is defined but not an array\n      throw new MeiliSearchError(\"Meilisearch: The header \\\"\".concat(agentHeader, \"\\\" should be an array of string(s).\\n\"));\n    } else {\n      headers[agentHeader] = packageAgent;\n    }\n\n    return headers;\n  }\n\n  var HttpRequests =\n  /** @class */\n  function () {\n    function HttpRequests(config) {\n      this.headers = createHeaders(config);\n\n      try {\n        var host = constructHostURL(config.host);\n        this.url = new URL(host);\n      } catch (e) {\n        throw new MeiliSearchError('The provided host is not valid.');\n      }\n    }\n\n    HttpRequests.prototype.request = function (_a) {\n      var method = _a.method,\n          url = _a.url,\n          params = _a.params,\n          body = _a.body,\n          config = _a.config;\n      return __awaiter(this, void 0, void 0, function () {\n        var constructURL, queryParams_1, response, parsedBody, e_1, stack;\n        return __generator(this, function (_b) {\n          switch (_b.label) {\n            case 0:\n              constructURL = new URL(url, this.url);\n\n              if (params) {\n                queryParams_1 = new URLSearchParams();\n                Object.keys(params).filter(function (x) {\n                  return params[x] !== null;\n                }).map(function (x) {\n                  return queryParams_1.set(x, params[x]);\n                });\n                constructURL.search = queryParams_1.toString();\n              }\n\n              _b.label = 1;\n\n            case 1:\n              _b.trys.push([1, 4,, 5]);\n\n              return [4\n              /*yield*/\n              , fetch(constructURL.toString(), __assign(__assign({}, config), {\n                method: method,\n                body: JSON.stringify(body),\n                headers: this.headers\n              })).then(function (res) {\n                return httpResponseErrorHandler(res);\n              })];\n\n            case 2:\n              response = _b.sent();\n              return [4\n              /*yield*/\n              , response.json()[\"catch\"](function () {\n                return undefined;\n              })];\n\n            case 3:\n              parsedBody = _b.sent();\n              return [2\n              /*return*/\n              , parsedBody];\n\n            case 4:\n              e_1 = _b.sent();\n              stack = e_1.stack;\n              httpErrorHandler(e_1, stack, constructURL.toString());\n              return [3\n              /*break*/\n              , 5];\n\n            case 5:\n              return [2\n              /*return*/\n              ];\n          }\n        });\n      });\n    };\n\n    HttpRequests.prototype.get = function (url, params, config) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.request({\n                method: 'GET',\n                url: url,\n                params: params,\n                config: config\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n\n    HttpRequests.prototype.post = function (url, data, params, config) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.request({\n                method: 'POST',\n                url: url,\n                body: data,\n                params: params,\n                config: config\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n\n    HttpRequests.prototype.put = function (url, data, params, config) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.request({\n                method: 'PUT',\n                url: url,\n                body: data,\n                params: params,\n                config: config\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n\n    HttpRequests.prototype.patch = function (url, data, params, config) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.request({\n                method: 'PATCH',\n                url: url,\n                body: data,\n                params: params,\n                config: config\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n\n    HttpRequests.prototype[\"delete\"] = function (url, data, params, config) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.request({\n                method: 'DELETE',\n                url: url,\n                body: data,\n                params: params,\n                config: config\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n\n    return HttpRequests;\n  }();\n\n  var EnqueuedTask =\n  /** @class */\n  function () {\n    function EnqueuedTask(task) {\n      this.taskUid = task.taskUid;\n      this.indexUid = task.indexUid;\n      this.status = task.status;\n      this.type = task.type;\n      this.enqueuedAt = new Date(task.enqueuedAt);\n    }\n\n    return EnqueuedTask;\n  }();\n\n  var Task =\n  /** @class */\n  function () {\n    function Task(task) {\n      this.indexUid = task.indexUid;\n      this.status = task.status;\n      this.type = task.type;\n      this.uid = task.uid;\n      this.details = task.details;\n      this.canceledBy = task.canceledBy;\n      this.error = task.error;\n      this.duration = task.duration;\n      this.startedAt = new Date(task.startedAt);\n      this.enqueuedAt = new Date(task.enqueuedAt);\n      this.finishedAt = new Date(task.finishedAt);\n    }\n\n    return Task;\n  }();\n\n  var TaskClient =\n  /** @class */\n  function () {\n    function TaskClient(config) {\n      this.httpRequest = new HttpRequests(config);\n    }\n    /**\r\n     * Get one task\r\n     *\r\n     * @param  {number} uid - unique identifier of the task\r\n     *\r\n     * @returns { Promise<Task> }\r\n     */\n\n\n    TaskClient.prototype.getTask = function (uid) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, taskItem;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"tasks/\".concat(uid);\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              taskItem = _a.sent();\n              return [2\n              /*return*/\n              , new Task(taskItem)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get tasks\r\n     *\r\n     * @param  {TasksQuery} [parameters={}] - Parameters to browse the tasks\r\n     *\r\n     * @returns {Promise<TasksResults>} - Promise containing all tasks\r\n     */\n\n\n    TaskClient.prototype.getTasks = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, tasks;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"tasks\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url, toQueryParams(parameters))];\n\n            case 1:\n              tasks = _a.sent();\n              return [2\n              /*return*/\n              , __assign(__assign({}, tasks), {\n                results: tasks.results.map(function (task) {\n                  return new Task(task);\n                })\n              })];\n          }\n        });\n      });\n    };\n    /**\r\n     * Wait for a task to be processed.\r\n     *\r\n     * @param {number} taskUid Task identifier\r\n     * @param {WaitOptions} options Additional configuration options\r\n     *\r\n     * @returns {Promise<Task>} Promise returning a task after it has been processed\r\n     */\n\n\n    TaskClient.prototype.waitForTask = function (taskUid, _a) {\n      var _b = _a === void 0 ? {} : _a,\n          _c = _b.timeOutMs,\n          timeOutMs = _c === void 0 ? 5000 : _c,\n          _d = _b.intervalMs,\n          intervalMs = _d === void 0 ? 50 : _d;\n\n      return __awaiter(this, void 0, void 0, function () {\n        var startingTime, response;\n        return __generator(this, function (_e) {\n          switch (_e.label) {\n            case 0:\n              startingTime = Date.now();\n              _e.label = 1;\n\n            case 1:\n              if (!(Date.now() - startingTime < timeOutMs)) return [3\n              /*break*/\n              , 4];\n              return [4\n              /*yield*/\n              , this.getTask(taskUid)];\n\n            case 2:\n              response = _e.sent();\n              if (![\"enqueued\"\n              /* TASK_ENQUEUED */\n              , \"processing\"\n              /* TASK_PROCESSING */\n              ].includes(response.status)) return [2\n              /*return*/\n              , response];\n              return [4\n              /*yield*/\n              , sleep(intervalMs)];\n\n            case 3:\n              _e.sent();\n\n              return [3\n              /*break*/\n              , 1];\n\n            case 4:\n              throw new MeiliSearchTimeOutError(\"timeout of \".concat(timeOutMs, \"ms has exceeded on process \").concat(taskUid, \" when waiting a task to be resolved.\"));\n          }\n        });\n      });\n    };\n    /**\r\n     * Waits for multiple tasks to be processed\r\n     *\r\n     * @param {number[]} taskUids Tasks identifier list\r\n     * @param {WaitOptions} options Wait options\r\n     *\r\n     * @returns {Promise<Task[]>} Promise returning a list of tasks after they have been processed\r\n     */\n\n\n    TaskClient.prototype.waitForTasks = function (taskUids, _a) {\n      var _b = _a === void 0 ? {} : _a,\n          _c = _b.timeOutMs,\n          timeOutMs = _c === void 0 ? 5000 : _c,\n          _d = _b.intervalMs,\n          intervalMs = _d === void 0 ? 50 : _d;\n\n      return __awaiter(this, void 0, void 0, function () {\n        var tasks, _i, taskUids_1, taskUid, task;\n\n        return __generator(this, function (_e) {\n          switch (_e.label) {\n            case 0:\n              tasks = [];\n              _i = 0, taskUids_1 = taskUids;\n              _e.label = 1;\n\n            case 1:\n              if (!(_i < taskUids_1.length)) return [3\n              /*break*/\n              , 4];\n              taskUid = taskUids_1[_i];\n              return [4\n              /*yield*/\n              , this.waitForTask(taskUid, {\n                timeOutMs: timeOutMs,\n                intervalMs: intervalMs\n              })];\n\n            case 2:\n              task = _e.sent();\n              tasks.push(task);\n              _e.label = 3;\n\n            case 3:\n              _i++;\n              return [3\n              /*break*/\n              , 1];\n\n            case 4:\n              return [2\n              /*return*/\n              , tasks];\n          }\n        });\n      });\n    };\n    /**\r\n     * Cancel a list of enqueued or processing tasks.\r\n     * @memberof Tasks\r\n     * @method cancelTasks\r\n     * @param {CancelTasksQuery} [parameters={}] - Parameters to filter the tasks.\r\n     *\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    TaskClient.prototype.cancelTasks = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"tasks/cancel\";\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url, {}, toQueryParams(parameters))];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete a list tasks.\r\n     * @memberof Tasks\r\n     * @method deleteTasks\r\n     * @param {DeleteTasksQuery} [parameters={}] - Parameters to filter the tasks.\r\n     *\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    TaskClient.prototype.deleteTasks = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"tasks\";\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url, {}, toQueryParams(parameters))];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n\n    return TaskClient;\n  }();\n\n  /*\r\n   * Bundle: MeiliSearch / Indexes\r\n   * Project: MeiliSearch - Javascript API\r\n   * Author: Quentin de Quelen <<EMAIL>>\r\n   * Copyright: 2019, MeiliSearch\r\n   */\n\n  var Index =\n  /** @class */\n  function () {\n    /**\r\n     * @param {Config} config Request configuration options\r\n     * @param {string} uid UID of the index\r\n     * @param {string} [primaryKey] Primary Key of the index\r\n     */\n    function Index(config, uid, primaryKey) {\n      this.uid = uid;\n      this.primaryKey = primaryKey;\n      this.httpRequest = new HttpRequests(config);\n      this.tasks = new TaskClient(config);\n    } ///\n    /// SEARCH\n    ///\n\n    /**\r\n     * Search for documents into an index\r\n     * @memberof Index\r\n     * @method search\r\n     * @template T\r\n     * @param {string | null} query? Query string\r\n     * @param {SearchParams} options? Search options\r\n     * @param {Partial<Request>} config? Additional request configuration options\r\n     * @returns {Promise<SearchResponse<T>>} Promise containing the search response\r\n     */\n\n\n    Index.prototype.search = function (query, options, config) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/search\");\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url, removeUndefinedFromObject(__assign({\n                q: query\n              }, options)), undefined, config)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Search for documents into an index using the GET method\r\n     * @memberof Index\r\n     * @method search\r\n     * @template T\r\n     * @param {string | null} query? Query string\r\n     * @param {SearchParams} options? Search options\r\n     * @param {Partial<Request>} config? Additional request configuration options\r\n     * @returns {Promise<SearchResponse<T>>} Promise containing the search response\r\n     */\n\n\n    Index.prototype.searchGet = function (query, options, config) {\n      var _a, _b, _c, _d, _e;\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, parseFilter, getParams;\n        return __generator(this, function (_f) {\n          switch (_f.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/search\");\n\n              parseFilter = function parseFilter(filter) {\n                if (typeof filter === 'string') return filter;else if (Array.isArray(filter)) throw new MeiliSearchError('The filter query parameter should be in string format when using searchGet');else return undefined;\n              };\n\n              getParams = __assign(__assign({\n                q: query\n              }, options), {\n                filter: parseFilter(options === null || options === void 0 ? void 0 : options.filter),\n                sort: (_a = options === null || options === void 0 ? void 0 : options.sort) === null || _a === void 0 ? void 0 : _a.join(','),\n                facets: (_b = options === null || options === void 0 ? void 0 : options.facets) === null || _b === void 0 ? void 0 : _b.join(','),\n                attributesToRetrieve: (_c = options === null || options === void 0 ? void 0 : options.attributesToRetrieve) === null || _c === void 0 ? void 0 : _c.join(','),\n                attributesToCrop: (_d = options === null || options === void 0 ? void 0 : options.attributesToCrop) === null || _d === void 0 ? void 0 : _d.join(','),\n                attributesToHighlight: (_e = options === null || options === void 0 ? void 0 : options.attributesToHighlight) === null || _e === void 0 ? void 0 : _e.join(',')\n              });\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url, removeUndefinedFromObject(getParams), config)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _f.sent()];\n          }\n        });\n      });\n    }; ///\n    /// INDEX\n    ///\n\n    /**\r\n     * Get index information.\r\n     * @memberof Index\r\n     * @method getRawInfo\r\n     *\r\n     * @returns {Promise<IndexObject>} Promise containing index information\r\n     */\n\n\n    Index.prototype.getRawInfo = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, res;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid);\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              res = _a.sent();\n              this.primaryKey = res.primaryKey;\n              this.updatedAt = new Date(res.updatedAt);\n              this.createdAt = new Date(res.createdAt);\n              return [2\n              /*return*/\n              , res];\n          }\n        });\n      });\n    };\n    /**\r\n     * Fetch and update Index information.\r\n     * @memberof Index\r\n     * @method fetchInfo\r\n     * @returns {Promise<this>} Promise to the current Index object with updated information\r\n     */\n\n\n    Index.prototype.fetchInfo = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.getRawInfo()];\n\n            case 1:\n              _a.sent();\n\n              return [2\n              /*return*/\n              , this];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get Primary Key.\r\n     * @memberof Index\r\n     * @method fetchPrimaryKey\r\n     * @returns {Promise<string | undefined>} Promise containing the Primary Key of the index\r\n     */\n\n\n    Index.prototype.fetchPrimaryKey = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var _a;\n\n        return __generator(this, function (_b) {\n          switch (_b.label) {\n            case 0:\n              _a = this;\n              return [4\n              /*yield*/\n              , this.getRawInfo()];\n\n            case 1:\n              _a.primaryKey = _b.sent().primaryKey;\n              return [2\n              /*return*/\n              , this.primaryKey];\n          }\n        });\n      });\n    };\n    /**\r\n     * Create an index.\r\n     * @memberof Index\r\n     * @method create\r\n     * @template T\r\n     * @param {string} uid Unique identifier of the Index\r\n     * @param {IndexOptions} options Index options\r\n     * @param {Config} config Request configuration options\r\n     * @returns {Promise<EnqueuedTask>} Newly created Index object\r\n     */\n\n\n    Index.create = function (uid, options, config) {\n      if (options === void 0) {\n        options = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, req, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes\";\n              req = new HttpRequests(config);\n              return [4\n              /*yield*/\n              , req.post(url, __assign(__assign({}, options), {\n                uid: uid\n              }))];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update an index.\r\n     * @memberof Index\r\n     * @method update\r\n     * @param {IndexOptions} data Data to update\r\n     * @returns {Promise<this>} Promise to the current Index object with updated information\r\n     */\n\n\n    Index.prototype.update = function (data) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid);\n              return [4\n              /*yield*/\n              , this.httpRequest.patch(url, data)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete an index.\r\n     * @memberof Index\r\n     * @method delete\r\n     * @returns {Promise<void>} Promise which resolves when index is deleted successfully\r\n     */\n\n\n    Index.prototype[\"delete\"] = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid);\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    }; ///\n    /// TASKS\n    ///\n\n    /**\r\n     * Get the list of all the tasks of the index.\r\n     *\r\n     * @memberof Indexes\r\n     * @method getTasks\r\n     * @param {TasksQuery} [parameters={}] - Parameters to browse the tasks\r\n     *\r\n     * @returns {Promise<TasksResults>} - Promise containing all tasks\r\n     */\n\n\n    Index.prototype.getTasks = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.getTasks(__assign(__assign({}, parameters), {\n                indexUids: [this.uid]\n              }))];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get one task of the index.\r\n     *\r\n     * @memberof Indexes\r\n     * @method getTask\r\n     * @param {number} taskUid - Task identifier\r\n     *\r\n     * @returns {Promise<Task>} - Promise containing a task\r\n     */\n\n\n    Index.prototype.getTask = function (taskUid) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.getTask(taskUid)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Wait for multiple tasks to be processed.\r\n     *\r\n     * @memberof Indexes\r\n     * @method waitForTasks\r\n     * @param {number[]} taskUids - Tasks identifier\r\n     * @param {WaitOptions} waitOptions - Options on timeout and interval\r\n     *\r\n     * @returns {Promise<Task[]>} - Promise containing an array of tasks\r\n     */\n\n\n    Index.prototype.waitForTasks = function (taskUids, _a) {\n      var _b = _a === void 0 ? {} : _a,\n          _c = _b.timeOutMs,\n          timeOutMs = _c === void 0 ? 5000 : _c,\n          _d = _b.intervalMs,\n          intervalMs = _d === void 0 ? 50 : _d;\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_e) {\n          switch (_e.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.waitForTasks(taskUids, {\n                timeOutMs: timeOutMs,\n                intervalMs: intervalMs\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _e.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Wait for a task to be processed.\r\n     *\r\n     * @memberof Indexes\r\n     * @method waitForTask\r\n     * @param {number} taskUid - Task identifier\r\n     * @param {WaitOptions} waitOptions - Options on timeout and interval\r\n     *\r\n     * @returns {Promise<Task>} - Promise containing an array of tasks\r\n     */\n\n\n    Index.prototype.waitForTask = function (taskUid, _a) {\n      var _b = _a === void 0 ? {} : _a,\n          _c = _b.timeOutMs,\n          timeOutMs = _c === void 0 ? 5000 : _c,\n          _d = _b.intervalMs,\n          intervalMs = _d === void 0 ? 50 : _d;\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_e) {\n          switch (_e.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.waitForTask(taskUid, {\n                timeOutMs: timeOutMs,\n                intervalMs: intervalMs\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _e.sent()];\n          }\n        });\n      });\n    }; ///\n    /// STATS\n    ///\n\n    /**\r\n     * get stats of an index\r\n     * @memberof Index\r\n     * @method getStats\r\n     * @returns {Promise<IndexStats>} Promise containing object with stats of the index\r\n     */\n\n\n    Index.prototype.getStats = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/stats\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    }; ///\n    /// DOCUMENTS\n    ///\n\n    /**\r\n     * get documents of an index\r\n     * @memberof Index\r\n     * @method getDocuments\r\n     * @template T\r\n     * @param {DocumentsQuery<T>} [parameters={}] Parameters to browse the documents\r\n     * @returns {Promise<DocumentsResults<T>>>} Promise containing Document responses\r\n     */\n\n\n    Index.prototype.getDocuments = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, fields;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents\");\n\n              fields = function () {\n                var _a;\n\n                if (Array.isArray(parameters === null || parameters === void 0 ? void 0 : parameters.fields)) {\n                  return (_a = parameters === null || parameters === void 0 ? void 0 : parameters.fields) === null || _a === void 0 ? void 0 : _a.join(',');\n                }\n\n                return undefined;\n              }();\n\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url, removeUndefinedFromObject(__assign(__assign({}, parameters), {\n                fields: fields\n              })))];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get one document\r\n     * @memberof Index\r\n     * @method getDocument\r\n     * @template T\r\n     * @param {string | number} documentId Document ID\r\n     * @param {DocumentQuery<T>} [parameters={}] Parameters applied on a document\r\n     * @returns {Promise<Document<T>>} Promise containing Document response\r\n     */\n\n\n    Index.prototype.getDocument = function (documentId, parameters) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, fields;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents/\").concat(documentId);\n\n              fields = function () {\n                var _a;\n\n                if (Array.isArray(parameters === null || parameters === void 0 ? void 0 : parameters.fields)) {\n                  return (_a = parameters === null || parameters === void 0 ? void 0 : parameters.fields) === null || _a === void 0 ? void 0 : _a.join(',');\n                }\n\n                return undefined;\n              }();\n\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url, removeUndefinedFromObject(__assign(__assign({}, parameters), {\n                fields: fields\n              })))];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Add or replace multiples documents to an index\r\n     * @memberof Index\r\n     * @method addDocuments\r\n     * @template T\r\n     * @param {Array<Document<T>>} documents Array of Document objects to add/replace\r\n     * @param {DocumentOptions} options? Options on document addition\r\n     *\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.addDocuments = function (documents, options) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents\");\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url, documents, options)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Add or replace multiples documents to an index in batches\r\n     * @memberof Index\r\n     * @method addDocumentsInBatches\r\n     * @template T\r\n     * @param {Array<Document<T>>} documents Array of Document objects to add/replace\r\n     * @param {number} batchSize Size of the batch\r\n     * @param {DocumentOptions} options? Options on document addition\r\n     * @returns {Promise<EnqueuedTasks>} Promise containing array of enqueued task objects for each batch\r\n     */\n\n\n    Index.prototype.addDocumentsInBatches = function (documents, batchSize, options) {\n      if (batchSize === void 0) {\n        batchSize = 1000;\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var updates, i, _a, _b;\n\n        return __generator(this, function (_c) {\n          switch (_c.label) {\n            case 0:\n              updates = [];\n              i = 0;\n              _c.label = 1;\n\n            case 1:\n              if (!(i < documents.length)) return [3\n              /*break*/\n              , 4];\n              _b = (_a = updates).push;\n              return [4\n              /*yield*/\n              , this.addDocuments(documents.slice(i, i + batchSize), options)];\n\n            case 2:\n              _b.apply(_a, [_c.sent()]);\n\n              _c.label = 3;\n\n            case 3:\n              i += batchSize;\n              return [3\n              /*break*/\n              , 1];\n\n            case 4:\n              return [2\n              /*return*/\n              , updates];\n          }\n        });\n      });\n    };\n    /**\r\n     * Add or update multiples documents to an index\r\n     * @memberof Index\r\n     * @method updateDocuments\r\n     * @param {Array<Document<Partial<T>>>} documents Array of Document objects to add/update\r\n     * @param {DocumentOptions} options? Options on document update\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateDocuments = function (documents, options) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, documents, options)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Add or update multiples documents to an index in batches\r\n     * @memberof Index\r\n     * @method updateDocuments\r\n     * @template T\r\n     * @param {Array<Document<T>>} documents Array of Document objects to add/update\r\n     * @param {number} batchSize Size of the batch\r\n     * @param {DocumentOptions} options? Options on document update\r\n     * @returns {Promise<EnqueuedTasks>} Promise containing array of enqueued task objects for each batch\r\n     */\n\n\n    Index.prototype.updateDocumentsInBatches = function (documents, batchSize, options) {\n      if (batchSize === void 0) {\n        batchSize = 1000;\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var updates, i, _a, _b;\n\n        return __generator(this, function (_c) {\n          switch (_c.label) {\n            case 0:\n              updates = [];\n              i = 0;\n              _c.label = 1;\n\n            case 1:\n              if (!(i < documents.length)) return [3\n              /*break*/\n              , 4];\n              _b = (_a = updates).push;\n              return [4\n              /*yield*/\n              , this.updateDocuments(documents.slice(i, i + batchSize), options)];\n\n            case 2:\n              _b.apply(_a, [_c.sent()]);\n\n              _c.label = 3;\n\n            case 3:\n              i += batchSize;\n              return [3\n              /*break*/\n              , 1];\n\n            case 4:\n              return [2\n              /*return*/\n              , updates];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete one document\r\n     * @memberof Index\r\n     * @method deleteDocument\r\n     * @param {string | number} documentId Id of Document to delete\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.deleteDocument = function (documentId) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents/\").concat(documentId);\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete multiples documents of an index\r\n     * @memberof Index\r\n     * @method deleteDocuments\r\n     * @param {string[] | number[]} documentsIds Array of Document Ids to delete\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.deleteDocuments = function (documentsIds) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents/delete-batch\");\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url, documentsIds)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete all documents of an index\r\n     * @memberof Index\r\n     * @method deleteAllDocuments\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.deleteAllDocuments = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/documents\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// SETTINGS\n    ///\n\n    /**\r\n     * Retrieve all settings\r\n     * @memberof Index\r\n     * @method getSettings\r\n     * @returns {Promise<Settings>} Promise containing Settings object\r\n     */\n\n\n    Index.prototype.getSettings = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update all settings\r\n     * Any parameters not provided will be left unchanged.\r\n     * @memberof Index\r\n     * @method updateSettings\r\n     * @param {Settings} settings Object containing parameters with their updated values\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateSettings = function (settings) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings\");\n              return [4\n              /*yield*/\n              , this.httpRequest.patch(url, settings)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueued = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset settings.\r\n     * @memberof Index\r\n     * @method resetSettings\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetSettings = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// PAGINATION SETTINGS\n    ///\n\n    /**\r\n     * Get the pagination settings.\r\n     * @memberof Index\r\n     * @method getPagination\r\n     * @returns {Promise<PaginationSetting>} Promise containing object of pagination settings\r\n     */\n\n\n    Index.prototype.getPagination = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/pagination\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the pagination settings.\r\n     * @memberof Index\r\n     * @method updatePagination\r\n     * @param {PaginationSettings} pagination Pagination object\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updatePagination = function (pagination) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/pagination\");\n              return [4\n              /*yield*/\n              , this.httpRequest.patch(url, pagination)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the pagination settings.\r\n     * @memberof Index\r\n     * @method resetPagination\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetPagination = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/pagination\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    }; ///\n    /// SYNONYMS\n    ///\n\n    /**\r\n     * Get the list of all synonyms\r\n     * @memberof Index\r\n     * @method getSynonyms\r\n     * @returns {Promise<object>} Promise containing object of synonym mappings\r\n     */\n\n\n    Index.prototype.getSynonyms = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/synonyms\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the list of synonyms. Overwrite the old list.\r\n     * @memberof Index\r\n     * @method updateSynonyms\r\n     * @param {Synonyms} synonyms Mapping of synonyms with their associated words\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateSynonyms = function (synonyms) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/synonyms\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, synonyms)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the synonym list to be empty again\r\n     * @memberof Index\r\n     * @method resetSynonyms\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetSynonyms = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/synonyms\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// STOP WORDS\n    ///\n\n    /**\r\n     * Get the list of all stop-words\r\n     * @memberof Index\r\n     * @method getStopWords\r\n     * @returns {Promise<string[]>} Promise containing array of stop-words\r\n     */\n\n\n    Index.prototype.getStopWords = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/stop-words\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the list of stop-words. Overwrite the old list.\r\n     * @memberof Index\r\n     * @method updateStopWords\r\n     * @param {StopWords} stopWords Array of strings that contains the stop-words.\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateStopWords = function (stopWords) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/stop-words\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, stopWords)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the stop-words list to be empty again\r\n     * @memberof Index\r\n     * @method resetStopWords\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetStopWords = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/stop-words\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// RANKING RULES\n    ///\n\n    /**\r\n     * Get the list of all ranking-rules\r\n     * @memberof Index\r\n     * @method getRankingRules\r\n     * @returns {Promise<string[]>} Promise containing array of ranking-rules\r\n     */\n\n\n    Index.prototype.getRankingRules = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/ranking-rules\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the list of ranking-rules. Overwrite the old list.\r\n     * @memberof Index\r\n     * @method updateRankingRules\r\n     * @param {RankingRules} rankingRules Array that contain ranking rules sorted by order of importance.\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateRankingRules = function (rankingRules) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/ranking-rules\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, rankingRules)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the ranking rules list to its default value\r\n     * @memberof Index\r\n     * @method resetRankingRules\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetRankingRules = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/ranking-rules\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// DISTINCT ATTRIBUTE\n    ///\n\n    /**\r\n     * Get the distinct-attribute\r\n     * @memberof Index\r\n     * @method getDistinctAttribute\r\n     * @returns {Promise<string | null>} Promise containing the distinct-attribute of the index\r\n     */\n\n\n    Index.prototype.getDistinctAttribute = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/distinct-attribute\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the distinct-attribute.\r\n     * @memberof Index\r\n     * @method updateDistinctAttribute\r\n     * @param {DistinctAttribute} distinctAttribute Field name of the distinct-attribute\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateDistinctAttribute = function (distinctAttribute) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/distinct-attribute\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, distinctAttribute)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the distinct-attribute.\r\n     * @memberof Index\r\n     * @method resetDistinctAttribute\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetDistinctAttribute = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/distinct-attribute\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// FILTERABLE ATTRIBUTES\n    ///\n\n    /**\r\n     * Get the filterable-attributes\r\n     * @memberof Index\r\n     * @method getFilterableAttributes\r\n     * @returns {Promise<string[]>} Promise containing an array of filterable-attributes\r\n     */\n\n\n    Index.prototype.getFilterableAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/filterable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the filterable-attributes.\r\n     * @memberof Index\r\n     * @method updateFilterableAttributes\r\n     * @param {FilterableAttributes} filterableAttributes Array of strings containing the attributes that can be used as filters at query time\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateFilterableAttributes = function (filterableAttributes) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/filterable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, filterableAttributes)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the filterable-attributes.\r\n     * @memberof Index\r\n     * @method resetFilterableAttributes\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetFilterableAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/filterable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// SORTABLE ATTRIBUTES\n    ///\n\n    /**\r\n     * Get the sortable-attributes\r\n     * @memberof Index\r\n     * @method getSortableAttributes\r\n     * @returns {Promise<string[]>} Promise containing array of sortable-attributes\r\n     */\n\n\n    Index.prototype.getSortableAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/sortable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the sortable-attributes.\r\n     * @memberof Index\r\n     * @method updateSortableAttributes\r\n     * @param {SortableAttributes} sortableAttributes Array of strings containing the attributes that can be used to sort search results at query time\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateSortableAttributes = function (sortableAttributes) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/sortable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, sortableAttributes)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the sortable-attributes.\r\n     * @memberof Index\r\n     * @method resetSortableAttributes\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetSortableAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/sortable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// SEARCHABLE ATTRIBUTE\n    ///\n\n    /**\r\n     * Get the searchable-attributes\r\n     * @memberof Index\r\n     * @method getSearchableAttributes\r\n     * @returns {Promise<string[]>} Promise containing array of searchable-attributes\r\n     */\n\n\n    Index.prototype.getSearchableAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/searchable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the searchable-attributes.\r\n     * @memberof Index\r\n     * @method updateSearchableAttributes\r\n     * @param {SearchableAttributes} searchableAttributes Array of strings that contains searchable attributes sorted by order of importance(most to least important)\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateSearchableAttributes = function (searchableAttributes) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/searchable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, searchableAttributes)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the searchable-attributes.\r\n     * @memberof Index\r\n     * @method resetSearchableAttributes\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetSearchableAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/searchable-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// DISPLAYED ATTRIBUTE\n    ///\n\n    /**\r\n     * Get the displayed-attributes\r\n     * @memberof Index\r\n     * @method getDisplayedAttributes\r\n     * @returns {Promise<string[]>} Promise containing array of displayed-attributes\r\n     */\n\n\n    Index.prototype.getDisplayedAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/displayed-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the displayed-attributes.\r\n     * @memberof Index\r\n     * @method updateDisplayedAttributes\r\n     * @param {DisplayedAttributes} displayedAttributes Array of strings that contains attributes of an index to display\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateDisplayedAttributes = function (displayedAttributes) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/displayed-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest.put(url, displayedAttributes)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the displayed-attributes.\r\n     * @memberof Index\r\n     * @method resetDisplayedAttributes\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetDisplayedAttributes = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/displayed-attributes\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// TYPO TOLERANCE\n    ///\n\n    /**\r\n     * Get the typo tolerance settings.\r\n     * @memberof Index\r\n     * @method getTypoTolerance\r\n     * @returns {Promise<string[]>} Promise containing the typo tolerance settings.\r\n     */\n\n\n    Index.prototype.getTypoTolerance = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/typo-tolerance\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the typo tolerance settings.\r\n     * @memberof Index\r\n     * @method updateTypoTolerance\r\n     * @param {TypoTolerance} typoTolerance Object containing the custom typo tolerance settings.\r\n     * @returns {Promise<EnqueuedTask>} Promise containing object of the enqueued update\r\n     */\n\n\n    Index.prototype.updateTypoTolerance = function (typoTolerance) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/typo-tolerance\");\n              return [4\n              /*yield*/\n              , this.httpRequest.patch(url, typoTolerance)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the typo tolerance settings.\r\n     * @memberof Index\r\n     * @method resetTypoTolerance\r\n     * @returns {Promise<EnqueuedTask>} Promise containing object of the enqueued update\r\n     */\n\n\n    Index.prototype.resetTypoTolerance = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/typo-tolerance\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              task.enqueuedAt = new Date(task.enqueuedAt);\n              return [2\n              /*return*/\n              , task];\n          }\n        });\n      });\n    }; ///\n    /// FACETING\n    ///\n\n    /**\r\n     * Get the faceting settings.\r\n     * @memberof Index\r\n     * @method getFaceting\r\n     * @returns {Promise<Faceting>} Promise containing object of faceting index settings\r\n     */\n\n\n    Index.prototype.getFaceting = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/faceting\");\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update the faceting settings.\r\n     * @memberof Index\r\n     * @method updateFaceting\r\n     * @param {Faceting} faceting Faceting index settings object\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.updateFaceting = function (faceting) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/faceting\");\n              return [4\n              /*yield*/\n              , this.httpRequest.patch(url, faceting)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n    /**\r\n     * Reset the faceting settings.\r\n     * @memberof Index\r\n     * @method resetFaceting\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Index.prototype.resetFaceting = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes/\".concat(this.uid, \"/settings/faceting\");\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    };\n\n    return Index;\n  }();\n\n  /*\r\n   * Bundle: MeiliSearch\r\n   * Project: MeiliSearch - Javascript API\r\n   * Author: Quentin de Quelen <<EMAIL>>\r\n   * Copyright: 2019, MeiliSearch\r\n   */\n\n  var Client =\n  /** @class */\n  function () {\n    /**\r\n     * Creates new MeiliSearch instance\r\n     * @param {Config} config Configuration object\r\n     */\n    function Client(config) {\n      this.config = config;\n      this.httpRequest = new HttpRequests(config);\n      this.tasks = new TaskClient(config);\n    }\n    /**\r\n     * Return an Index instance\r\n     * @memberof MeiliSearch\r\n     * @method index\r\n     * @template T\r\n     * @param {string} indexUid The index UID\r\n     * @returns {Index<T>} Instance of Index\r\n     */\n\n\n    Client.prototype.index = function (indexUid) {\n      return new Index(this.config, indexUid);\n    };\n    /**\r\n     * Gather information about an index by calling MeiliSearch and\r\n     * return an Index instance with the gathered information\r\n     * @memberof MeiliSearch\r\n     * @method getIndex\r\n     * @template T\r\n     * @param {string} indexUid The index UID\r\n     * @returns {Promise<Index<T>>} Promise returning Index instance\r\n     */\n\n\n    Client.prototype.getIndex = function (indexUid) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          return [2\n          /*return*/\n          , new Index(this.config, indexUid).fetchInfo()];\n        });\n      });\n    };\n    /**\r\n     * Gather information about an index by calling MeiliSearch and\r\n     * return the raw JSON response\r\n     * @memberof MeiliSearch\r\n     * @method getRawIndex\r\n     * @param {string} indexUid The index UID\r\n     * @returns {Promise<IndexObject>} Promise returning index information\r\n     */\n\n\n    Client.prototype.getRawIndex = function (indexUid) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          return [2\n          /*return*/\n          , new Index(this.config, indexUid).getRawInfo()];\n        });\n      });\n    };\n    /**\r\n     * Get all the indexes as Index instances.\r\n     * @memberof MeiliSearch\r\n     * @method getIndexes\r\n     * @param {IndexesQuery} [parameters={}] - Parameters to browse the indexes\r\n     *\r\n     * @returns {Promise<IndexesResults<Index[]>>} Promise returning array of raw index information\r\n     */\n\n\n    Client.prototype.getIndexes = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var rawIndexes, indexes;\n\n        var _this = this;\n\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.getRawIndexes(parameters)];\n\n            case 1:\n              rawIndexes = _a.sent();\n              indexes = rawIndexes.results.map(function (index) {\n                return new Index(_this.config, index.uid, index.primaryKey);\n              });\n              return [2\n              /*return*/\n              , __assign(__assign({}, rawIndexes), {\n                results: indexes\n              })];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get all the indexes in their raw value (no Index instances).\r\n     * @memberof MeiliSearch\r\n     * @method getRawIndexes\r\n     * @param {IndexesQuery} [parameters={}] - Parameters to browse the indexes\r\n     *\r\n     * @returns {Promise<IndexesResults<IndexObject[]>>} Promise returning array of raw index information\r\n     */\n\n\n    Client.prototype.getRawIndexes = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"indexes\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url, parameters)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Create a new index\r\n     * @memberof MeiliSearch\r\n     * @method createIndex\r\n     * @template T\r\n     * @param {string} uid The index UID\r\n     * @param {IndexOptions} options Index options\r\n     * @returns {Promise<Index<T>>} Promise returning Index instance\r\n     */\n\n\n    Client.prototype.createIndex = function (uid, options) {\n      if (options === void 0) {\n        options = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , Index.create(uid, options, this.config)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update an index\r\n     * @memberof MeiliSearch\r\n     * @method updateIndex\r\n     * @template T\r\n     * @param {string} uid The index UID\r\n     * @param {IndexOptions} options Index options to update\r\n     * @returns {Promise<Index<T>>} Promise returning Index instance after updating\r\n     */\n\n\n    Client.prototype.updateIndex = function (uid, options) {\n      if (options === void 0) {\n        options = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , new Index(this.config, uid).update(options)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete an index\r\n     * @memberof MeiliSearch\r\n     * @method deleteIndex\r\n     * @param {string} uid The index UID\r\n     * @returns {Promise<void>} Promise which resolves when index is deleted successfully\r\n     */\n\n\n    Client.prototype.deleteIndex = function (uid) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , new Index(this.config, uid)[\"delete\"]()];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Deletes an index if it already exists.\r\n     * @memberof MeiliSearch\r\n     * @method deleteIndexIfExists\r\n     * @param {string} uid The index UID\r\n     * @returns {Promise<boolean>} Promise which resolves to true when index exists and is deleted successfully, otherwise false if it does not exist\r\n     */\n\n\n    Client.prototype.deleteIndexIfExists = function (uid) {\n      return __awaiter(this, void 0, void 0, function () {\n        var e_1;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              _a.trys.push([0, 2,, 3]);\n\n              return [4\n              /*yield*/\n              , this.deleteIndex(uid)];\n\n            case 1:\n              _a.sent();\n\n              return [2\n              /*return*/\n              , true];\n\n            case 2:\n              e_1 = _a.sent();\n\n              if (e_1.code === \"index_not_found\"\n              /* INDEX_NOT_FOUND */\n              ) {\n                return [2\n                /*return*/\n                , false];\n              }\n\n              throw e_1;\n\n            case 3:\n              return [2\n              /*return*/\n              ];\n          }\n        });\n      });\n    };\n    /**\r\n     * Swaps a list of index tuples.\r\n     *\r\n     * @memberof MeiliSearch\r\n     * @method swapIndexes\r\n     * @param {SwapIndexesParams} params - List of indexes tuples to swap.\r\n     * @returns {Promise<EnqueuedTask>} - Promise returning object of the enqueued task\r\n     */\n\n\n    Client.prototype.swapIndexes = function (params) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = '/swap-indexes';\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url, params)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    }; ///\n    /// TASKS\n    ///\n\n    /**\r\n     * Get the list of all client tasks\r\n     * @memberof MeiliSearch\r\n     * @method getTasks\r\n     * @param {TasksQuery} [parameters={}] - Parameters to browse the tasks\r\n     *\r\n     * @returns {Promise<TasksResults>} - Promise returning all tasks\r\n     */\n\n\n    Client.prototype.getTasks = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.getTasks(parameters)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get one task on the client scope\r\n     * @memberof MeiliSearch\r\n     * @method getTask\r\n     * @param {number} taskUid - Task identifier\r\n     * @returns {Promise<Task>} - Promise returning a task\r\n     */\n\n\n    Client.prototype.getTask = function (taskUid) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.getTask(taskUid)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Wait for multiple tasks to be finished.\r\n     *\r\n     * @memberof MeiliSearch\r\n     * @method waitForTasks\r\n     * @param {number[]} taskUids - Tasks identifier\r\n     * @param {WaitOptions} waitOptions - Options on timeout and interval\r\n     *\r\n     * @returns {Promise<Task[]>} - Promise returning an array of tasks\r\n     */\n\n\n    Client.prototype.waitForTasks = function (taskUids, _a) {\n      var _b = _a === void 0 ? {} : _a,\n          _c = _b.timeOutMs,\n          timeOutMs = _c === void 0 ? 5000 : _c,\n          _d = _b.intervalMs,\n          intervalMs = _d === void 0 ? 50 : _d;\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_e) {\n          switch (_e.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.waitForTasks(taskUids, {\n                timeOutMs: timeOutMs,\n                intervalMs: intervalMs\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _e.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Wait for a task to be finished.\r\n     *\r\n     * @memberof MeiliSearch\r\n     * @method waitForTask\r\n     *\r\n     * @param {number} taskUid - Task identifier\r\n     * @param {WaitOptions} waitOptions - Options on timeout and interval\r\n     *\r\n     * @returns {Promise<Task>} - Promise returning an array of tasks\r\n     */\n\n\n    Client.prototype.waitForTask = function (taskUid, _a) {\n      var _b = _a === void 0 ? {} : _a,\n          _c = _b.timeOutMs,\n          timeOutMs = _c === void 0 ? 5000 : _c,\n          _d = _b.intervalMs,\n          intervalMs = _d === void 0 ? 50 : _d;\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_e) {\n          switch (_e.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.waitForTask(taskUid, {\n                timeOutMs: timeOutMs,\n                intervalMs: intervalMs\n              })];\n\n            case 1:\n              return [2\n              /*return*/\n              , _e.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Cancel a list of enqueued or processing tasks.\r\n     * @memberof MeiliSearch\r\n     * @method cancelTasks\r\n     * @param {CancelTasksQuery} [parameters={}] - Parameters to filter the tasks.\r\n     *\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Client.prototype.cancelTasks = function (parameters) {\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.cancelTasks(parameters)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete a list of tasks.\r\n     * @memberof MeiliSearch\r\n     * @method deleteTasks\r\n     * @param {DeleteTasksQuery} [parameters={}] - Parameters to filter the tasks.\r\n     *\r\n     * @returns {Promise<EnqueuedTask>} Promise containing an EnqueuedTask\r\n     */\n\n\n    Client.prototype.deleteTasks = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4\n              /*yield*/\n              , this.tasks.deleteTasks(parameters)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    }; ///\n    /// KEYS\n    ///\n\n    /**\r\n     * Get all API keys\r\n     * @memberof MeiliSearch\r\n     * @method getKeys\r\n     * @param {KeysQuery} [parameters={}] - Parameters to browse the indexes\r\n     *\r\n     * @returns {Promise<KeysResults>} Promise returning an object with keys\r\n     */\n\n\n    Client.prototype.getKeys = function (parameters) {\n      if (parameters === void 0) {\n        parameters = {};\n      }\n\n      return __awaiter(this, void 0, void 0, function () {\n        var url, keys;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"keys\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url, parameters)];\n\n            case 1:\n              keys = _a.sent();\n              keys.results = keys.results.map(function (key) {\n                return __assign(__assign({}, key), {\n                  createdAt: new Date(key.createdAt),\n                  updateAt: new Date(key.updateAt)\n                });\n              });\n              return [2\n              /*return*/\n              , keys];\n          }\n        });\n      });\n    };\n    /**\r\n     * Get one API key\r\n     * @memberof MeiliSearch\r\n     * @method getKey\r\n     *\r\n     * @param {string} keyOrUid - Key or uid of the API key\r\n     * @returns {Promise<Key>} Promise returning a key\r\n     */\n\n\n    Client.prototype.getKey = function (keyOrUid) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"keys/\".concat(keyOrUid);\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Create one API key\r\n     * @memberof MeiliSearch\r\n     * @method createKey\r\n     *\r\n     * @param {KeyCreation} options - Key options\r\n     * @returns {Promise<Key>} Promise returning a key\r\n     */\n\n\n    Client.prototype.createKey = function (options) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"keys\";\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url, options)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Update one API key\r\n     * @memberof MeiliSearch\r\n     * @method updateKey\r\n     *\r\n     * @param {string} keyOrUid - Key\r\n     * @param {KeyUpdate} options - Key options\r\n     * @returns {Promise<Key>} Promise returning a key\r\n     */\n\n\n    Client.prototype.updateKey = function (keyOrUid, options) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"keys/\".concat(keyOrUid);\n              return [4\n              /*yield*/\n              , this.httpRequest.patch(url, options)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Delete one API key\r\n     * @memberof MeiliSearch\r\n     * @method deleteKey\r\n     *\r\n     * @param {string} keyOrUid - Key\r\n     * @returns {Promise<Void>}\r\n     */\n\n\n    Client.prototype.deleteKey = function (keyOrUid) {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"keys/\".concat(keyOrUid);\n              return [4\n              /*yield*/\n              , this.httpRequest[\"delete\"](url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    }; ///\n    /// HEALTH\n    ///\n\n    /**\r\n     * Checks if the server is healthy, otherwise an error will be thrown.\r\n     * @memberof MeiliSearch\r\n     * @method health\r\n     * @returns {Promise<Health>} Promise returning an object with health details\r\n     */\n\n\n    Client.prototype.health = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"health\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    };\n    /**\r\n     * Checks if the server is healthy, return true or false.\r\n     * @memberof MeiliSearch\r\n     * @method isHealthy\r\n     * @returns {Promise<boolean>} Promise returning a boolean\r\n     */\n\n\n    Client.prototype.isHealthy = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              _a.trys.push([0, 2,, 3]);\n\n              url = \"health\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              _a.sent();\n\n              return [2\n              /*return*/\n              , true];\n\n            case 2:\n              _a.sent();\n              return [2\n              /*return*/\n              , false];\n\n            case 3:\n              return [2\n              /*return*/\n              ];\n          }\n        });\n      });\n    }; ///\n    /// STATS\n    ///\n\n    /**\r\n     * Get the stats of all the database\r\n     * @memberof MeiliSearch\r\n     * @method getStats\r\n     * @returns {Promise<Stats>} Promise returning object of all the stats\r\n     */\n\n\n    Client.prototype.getStats = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"stats\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    }; ///\n    /// VERSION\n    ///\n\n    /**\r\n     * Get the version of MeiliSearch\r\n     * @memberof MeiliSearch\r\n     * @method getVersion\r\n     * @returns {Promise<Version>} Promise returning object with version details\r\n     */\n\n\n    Client.prototype.getVersion = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"version\";\n              return [4\n              /*yield*/\n              , this.httpRequest.get(url)];\n\n            case 1:\n              return [2\n              /*return*/\n              , _a.sent()];\n          }\n        });\n      });\n    }; ///\n    /// DUMPS\n    ///\n\n    /**\r\n     * Creates a dump\r\n     * @memberof MeiliSearch\r\n     * @method createDump\r\n     * @returns {Promise<EnqueuedTask>} Promise returning object of the enqueued task\r\n     */\n\n\n    Client.prototype.createDump = function () {\n      return __awaiter(this, void 0, void 0, function () {\n        var url, task;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              url = \"dumps\";\n              return [4\n              /*yield*/\n              , this.httpRequest.post(url)];\n\n            case 1:\n              task = _a.sent();\n              return [2\n              /*return*/\n              , new EnqueuedTask(task)];\n          }\n        });\n      });\n    }; ///\n    /// TOKENS\n    ///\n\n    /**\r\n     * Generate a tenant token\r\n     *\r\n     * @memberof MeiliSearch\r\n     * @method generateTenantToken\r\n     * @param {apiKeyUid} apiKeyUid The uid of the api key used as issuer of the token.\r\n     * @param {SearchRules} searchRules Search rules that are applied to every search.\r\n     * @param {TokenOptions} options Token options to customize some aspect of the token.\r\n     *\r\n     * @returns {String} The token in JWT format.\r\n     */\n\n\n    Client.prototype.generateTenantToken = function (_apiKeyUid, _searchRules, _options) {\n      var error = new Error();\n      throw new Error(\"Meilisearch: failed to generate a tenant token. Generation of a token only works in a node environment \\n \".concat(error.stack, \".\"));\n    };\n\n    return Client;\n  }();\n\n  var MeiliSearch =\n  /** @class */\n  function (_super) {\n    __extends(MeiliSearch, _super);\n\n    function MeiliSearch(config) {\n      return _super.call(this, config) || this;\n    }\n\n    return MeiliSearch;\n  }(Client);\n\n  exports.Index = Index;\n  exports.MatchingStrategies = MatchingStrategies;\n  exports.MeiliSearch = MeiliSearch;\n  exports.MeiliSearchApiError = MeiliSearchApiError;\n  exports.MeiliSearchCommunicationError = MeiliSearchCommunicationError;\n  exports.MeiliSearchError = MeiliSearchError;\n  exports.MeiliSearchTimeOutError = MeiliSearchTimeOutError;\n  exports[\"default\"] = MeiliSearch;\n  exports.httpErrorHandler = httpErrorHandler;\n  exports.httpResponseErrorHandler = httpResponseErrorHandler;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/meilisearch/dist/bundles/meilisearch.umd.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Cdebug-authors.js&page=%2Fdebug-authors!":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Cdebug-authors.js&page=%2Fdebug-authors! ***!
  \***********************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/debug-authors\",\n      function () {\n        return __webpack_require__(/*! ./pages/debug-authors.js */ \"./pages/debug-authors.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/debug-authors\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDcmVwJTVDVFBTRyU1Q3Rwc2ctbmV4dCU1Q3BhZ2VzJTVDZGVidWctYXV0aG9ycy5qcyZwYWdlPSUyRmRlYnVnLWF1dGhvcnMhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9hY2NmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvZGVidWctYXV0aG9yc1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvZGVidWctYXV0aG9ycy5qc1wiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvZGVidWctYXV0aG9yc1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Cdebug-authors.js&page=%2Fdebug-authors!\n"));

/***/ }),

/***/ "./api/meili-client.js":
/*!*****************************!*\
  !*** ./api/meili-client.js ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeiliApi: function() { return /* binding */ MeiliApi; }\n/* harmony export */ });\n/* harmony import */ var meilisearch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! meilisearch */ \"./node_modules/meilisearch/dist/bundles/meilisearch.umd.js\");\n/* harmony import */ var meilisearch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(meilisearch__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MeiliClient = new meilisearch__WEBPACK_IMPORTED_MODULE_0__.MeiliSearch({\n    host: \"http://127.0.0.1:7700\",\n    apiKey: \"\"\n});\nconst index = MeiliClient.index(\"post\");\nconst instantSearch = async (q)=>{\n    return await index.search(q, {\n        attributesToCrop: [\n            \"body\"\n        ],\n        cropLength: 200,\n        limit: 5\n    });\n};\nconst search = async function() {\n    let q = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\", params = arguments.length > 1 ? arguments[1] : void 0;\n    return await index.search(q, {\n        attributesToRetrieve: [\n            \"title\",\n            \"author\",\n            \"type\",\n            \"image\",\n            \"route\",\n            \"slug\",\n            \"date\",\n            \"cs\",\n            \"lead\"\n        ],\n        attributesToHighlight: [\n            \"title\"\n        ],\n        ...params\n    });\n};\nconst searchHighlight = async function() {\n    let q = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\", params = arguments.length > 1 ? arguments[1] : void 0;\n    return await index.search(q, {\n        attributesToRetrieve: [\n            \"title\",\n            \"author\",\n            \"type\",\n            \"image\",\n            \"route\",\n            \"slug\",\n            \"date\",\n            \"body\",\n            \"lead\",\n            \"topics\",\n            \"cs\"\n        ],\n        attributesToHighlight: [\n            \"title\",\n            \"body\",\n            \"lead\"\n        ],\n        attributesToCrop: [\n            \"body\",\n            \"lead\"\n        ],\n        cropLength: 100,\n        ...params\n    });\n};\nconst urls = async ()=>{\n    return await index.getDocuments({\n        attributesToRetrieve: [\n            \"route\",\n            \"date\"\n        ],\n        limit: 9999\n    });\n};\nconst MeiliApi = {\n    instantSearch,\n    search,\n    searchHighlight,\n    urls\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./api/meili-client.js\n"));

/***/ }),

/***/ "./pages/debug-authors.js":
/*!********************************!*\
  !*** ./pages/debug-authors.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugAuthors; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var api_meili_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! api/meili-client */ \"./api/meili-client.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/graphql-tag/lib/index.js\");\n/* harmony import */ var _api_apollo_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../api/apollo-client */ \"./api/apollo-client.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetPostBySlug($slug: String!) {\\n    posts(where: { slug: $slug }) {\\n      id\\n      title\\n      slug\\n      author {\\n        fullName\\n        firstName\\n        lastName\\n      }\\n      type\\n      published_at\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DebugAuthors() {\n    _s();\n    const [meilisearchData, setMeilisearchData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [strapiData, setStrapiData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        async function fetchData() {\n            try {\n                // Test specific articles mentioned in the issue\n                const testSlugs = [\n                    \"pasteur-un-jour-pasteur-toujours\",\n                    \"un-pasteur-du-19e-siecle-nous-parle-de-predication-textuelle\",\n                    \"laccompagnement-biblique-une-question-de-communaute\"\n                ];\n                // Fetch from Meilisearch\n                const meiliResults = await Promise.all(testSlugs.map(async (slug)=>{\n                    const result = await api_meili_client__WEBPACK_IMPORTED_MODULE_3__.MeiliApi.searchHighlight(slug, {\n                        limit: 1\n                    });\n                    return {\n                        slug,\n                        result: result.hits[0] || null\n                    };\n                }));\n                // Fetch from Strapi GraphQL\n                const strapiResults = await Promise.all(testSlugs.map(async (slug)=>{\n                    try {\n                        const { data } = await _api_apollo_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].query({\n                            query: GET_POST_BY_SLUG,\n                            variables: {\n                                slug\n                            }\n                        });\n                        return {\n                            slug,\n                            result: data.posts[0] || null\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching from Strapi:\", slug, error);\n                        return {\n                            slug,\n                            result: null,\n                            error: error.message\n                        };\n                    }\n                }));\n                setMeilisearchData(meiliResults);\n                setStrapiData(strapiResults);\n                setLoading(false);\n                // Log detailed comparison\n                console.log(\"\\uD83D\\uDD0D DETAILED COMPARISON:\");\n                testSlugs.forEach((slug, index)=>{\n                    var _meiliResults_index, _strapiResults_index, _strapiResult_author;\n                    const meiliResult = (_meiliResults_index = meiliResults[index]) === null || _meiliResults_index === void 0 ? void 0 : _meiliResults_index.result;\n                    const strapiResult = (_strapiResults_index = strapiResults[index]) === null || _strapiResults_index === void 0 ? void 0 : _strapiResults_index.result;\n                    console.log(\"\\n\\uD83D\\uDCC4 Article: \".concat(slug));\n                    console.log(\"Meilisearch data:\", {\n                        title: meiliResult === null || meiliResult === void 0 ? void 0 : meiliResult.title,\n                        author: meiliResult === null || meiliResult === void 0 ? void 0 : meiliResult.author,\n                        authorType: typeof (meiliResult === null || meiliResult === void 0 ? void 0 : meiliResult.author),\n                        hasAuthor: !!(meiliResult === null || meiliResult === void 0 ? void 0 : meiliResult.author),\n                        allKeys: meiliResult ? Object.keys(meiliResult) : \"N/A\"\n                    });\n                    console.log(\"Strapi data:\", {\n                        title: strapiResult === null || strapiResult === void 0 ? void 0 : strapiResult.title,\n                        author: strapiResult === null || strapiResult === void 0 ? void 0 : strapiResult.author,\n                        authorFullName: strapiResult === null || strapiResult === void 0 ? void 0 : (_strapiResult_author = strapiResult.author) === null || _strapiResult_author === void 0 ? void 0 : _strapiResult_author.fullName,\n                        authorType: typeof (strapiResult === null || strapiResult === void 0 ? void 0 : strapiResult.author),\n                        hasAuthor: !!(strapiResult === null || strapiResult === void 0 ? void 0 : strapiResult.author),\n                        allKeys: strapiResult ? Object.keys(strapiResult) : \"N/A\"\n                    });\n                });\n            } catch (error) {\n                console.error(\"Error fetching debug data:\", error);\n                setLoading(false);\n            }\n        }\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            style: {\n                padding: \"20px\"\n            },\n            children: \"Loading debug data...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n            lineNumber: 85,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"monospace\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h1\", {\n                children: \"Debug Authors Data\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                children: \"Meilisearch Results\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"pre\", {\n                style: {\n                    background: \"#f5f5f5\",\n                    padding: \"10px\",\n                    overflow: \"auto\"\n                },\n                children: JSON.stringify(meilisearchData, null, 2)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                children: \"Strapi Results\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"pre\", {\n                style: {\n                    background: \"#f5f5f5\",\n                    padding: \"10px\",\n                    overflow: \"auto\"\n                },\n                children: JSON.stringify(strapiData, null, 2)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                children: \"Console Logs\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                children: \"Check the browser console for detailed comparison logs.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\debug-authors.js\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugAuthors, \"wSS/v20imxYMp5dSWUx211DBQR8=\");\n_c = DebugAuthors;\nconst GET_POST_BY_SLUG = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_5__.gql)(_templateObject());\nvar _c;\n$RefreshReg$(_c, \"DebugAuthors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/debug-authors.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Cdebug-authors.js&page=%2Fdebug-authors!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);