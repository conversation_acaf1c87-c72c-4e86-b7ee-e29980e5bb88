{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "75013", "content-type": "application/json", "date": "<PERSON><PERSON>, 27 May 2025 10:39:07 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "10212ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}