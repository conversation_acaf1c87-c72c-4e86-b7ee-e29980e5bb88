/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/categories"],{

/***/ "./node_modules/next-seo/lib/next-seo.module.js":
/*!******************************************************!*\
  !*** ./node_modules/next-seo/lib/next-seo.module.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArticleJsonLd: function() { return /* binding */ ArticleJsonLd; },\n/* harmony export */   BlogJsonLd: function() { return /* binding */ BlogJsonLd; },\n/* harmony export */   BrandJsonLd: function() { return /* binding */ BrandJsonLd; },\n/* harmony export */   BreadcrumbJsonLd: function() { return /* binding */ BreadCrumbJsonLd; },\n/* harmony export */   CarouselJsonLd: function() { return /* binding */ CarouselJsonLd; },\n/* harmony export */   CollectionPageJsonLd: function() { return /* binding */ CollectionPageJsonLd; },\n/* harmony export */   CorporateContactJsonLd: function() { return /* binding */ CorporateContactJsonLd; },\n/* harmony export */   CourseJsonLd: function() { return /* binding */ CourseJsonLd; },\n/* harmony export */   DatasetJsonLd: function() { return /* binding */ DatasetJsonLd; },\n/* harmony export */   DefaultSeo: function() { return /* binding */ DefaultSeo; },\n/* harmony export */   EventJsonLd: function() { return /* binding */ EventJsonLd; },\n/* harmony export */   FAQPageJsonLd: function() { return /* binding */ FAQPageJsonLd; },\n/* harmony export */   JobPostingJsonLd: function() { return /* binding */ JobPostingJsonLd; },\n/* harmony export */   LocalBusinessJsonLd: function() { return /* binding */ LocalBusinessJsonLd; },\n/* harmony export */   LogoJsonLd: function() { return /* binding */ LogoJsonLd; },\n/* harmony export */   NewsArticleJsonLd: function() { return /* binding */ NewsArticleJsonLd; },\n/* harmony export */   NextSeo: function() { return /* binding */ NextSeo; },\n/* harmony export */   OrganizationJsonLd: function() { return /* binding */ OrganizationJsonLd; },\n/* harmony export */   ProductJsonLd: function() { return /* binding */ ProductJsonLd; },\n/* harmony export */   ProfilePageJsonLd: function() { return /* binding */ ProfilePageJsonLd; },\n/* harmony export */   QAPageJsonld: function() { return /* binding */ QAPageJsonLd; },\n/* harmony export */   RecipeJsonLd: function() { return /* binding */ RecipeJsonLd; },\n/* harmony export */   SiteLinksSearchBoxJsonLd: function() { return /* binding */ SiteLinksSearchBoxJsonLd; },\n/* harmony export */   SocialProfileJsonLd: function() { return /* binding */ SocialProfileJsonLd; },\n/* harmony export */   SoftwareAppJsonLd: function() { return /* binding */ SoftwareAppJsonLd; },\n/* harmony export */   VideoGameJsonLd: function() { return /* binding */ VideoGameJsonLd; },\n/* harmony export */   VideoJsonLd: function() { return /* binding */ VideoJsonLd; },\n/* harmony export */   WebPageJsonLd: function() { return /* binding */ WebPageJsonLd; }\n/* harmony export */ });\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nvar defaults = {\n  templateTitle: '',\n  noindex: false,\n  nofollow: false,\n  defaultOpenGraphImageWidth: 0,\n  defaultOpenGraphImageHeight: 0,\n  defaultOpenGraphVideoWidth: 0,\n  defaultOpenGraphVideoHeight: 0,\n  disableGooglebot: false\n};\n\nvar buildOpenGraphMediaTags = function buildOpenGraphMediaTags(mediaType, media, _temp) {\n  if (media === void 0) {\n    media = [];\n  }\n\n  var _ref = _temp === void 0 ? {} : _temp,\n      defaultWidth = _ref.defaultWidth,\n      defaultHeight = _ref.defaultHeight;\n\n  return media.reduce(function (tags, medium, index) {\n    tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n      key: \"og:\" + mediaType + \":0\" + index,\n      property: \"og:\" + mediaType,\n      content: medium.url\n    }));\n\n    if (medium.alt) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":alt0\" + index,\n        property: \"og:\" + mediaType + \":alt\",\n        content: medium.alt\n      }));\n    }\n\n    if (medium.secureUrl) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":secure_url0\" + index,\n        property: \"og:\" + mediaType + \":secure_url\",\n        content: medium.secureUrl.toString()\n      }));\n    }\n\n    if (medium.type) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":type0\" + index,\n        property: \"og:\" + mediaType + \":type\",\n        content: medium.type.toString()\n      }));\n    }\n\n    if (medium.width) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":width0\" + index,\n        property: \"og:\" + mediaType + \":width\",\n        content: medium.width.toString()\n      }));\n    } else if (defaultWidth) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":width0\" + index,\n        property: \"og:\" + mediaType + \":width\",\n        content: defaultWidth.toString()\n      }));\n    }\n\n    if (medium.height) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":height\" + index,\n        property: \"og:\" + mediaType + \":height\",\n        content: medium.height.toString()\n      }));\n    } else if (defaultHeight) {\n      tags.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:\" + mediaType + \":height\" + index,\n        property: \"og:\" + mediaType + \":height\",\n        content: defaultHeight.toString()\n      }));\n    }\n\n    return tags;\n  }, []);\n};\n\nvar buildTags = function buildTags(config) {\n  var _config$openGraph, _config$openGraph3, _config$additionalLin;\n\n  var tagsToRender = [];\n\n  if (config.titleTemplate) {\n    defaults.templateTitle = config.titleTemplate;\n  }\n\n  var updatedTitle = '';\n\n  if (config.title) {\n    updatedTitle = config.title;\n\n    if (defaults.templateTitle) {\n      updatedTitle = defaults.templateTitle.replace(/%s/g, function () {\n        return updatedTitle;\n      });\n    }\n  } else if (config.defaultTitle) {\n    updatedTitle = config.defaultTitle;\n  }\n\n  if (updatedTitle) {\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"title\", {\n      key: \"title\"\n    }, updatedTitle));\n  }\n\n  var noindex = config.noindex || defaults.noindex || config.dangerouslySetAllPagesToNoIndex;\n  var nofollow = config.nofollow || defaults.nofollow || config.dangerouslySetAllPagesToNoFollow;\n  var disableGooglebot = config.disableGooglebot || defaults.disableGooglebot || config.dangerouslyDisableGooglebot;\n  var robotsParams = '';\n\n  if (config.robotsProps) {\n    var _config$robotsProps = config.robotsProps,\n        nosnippet = _config$robotsProps.nosnippet,\n        maxSnippet = _config$robotsProps.maxSnippet,\n        maxImagePreview = _config$robotsProps.maxImagePreview,\n        maxVideoPreview = _config$robotsProps.maxVideoPreview,\n        noarchive = _config$robotsProps.noarchive,\n        noimageindex = _config$robotsProps.noimageindex,\n        notranslate = _config$robotsProps.notranslate,\n        unavailableAfter = _config$robotsProps.unavailableAfter;\n    robotsParams = \"\" + (nosnippet ? ',nosnippet' : '') + (maxSnippet ? \",max-snippet:\" + maxSnippet : '') + (maxImagePreview ? \",max-image-preview:\" + maxImagePreview : '') + (noarchive ? ',noarchive' : '') + (unavailableAfter ? \",unavailable_after:\" + unavailableAfter : '') + (noimageindex ? ',noimageindex' : '') + (maxVideoPreview ? \",max-video-preview:\" + maxVideoPreview : '') + (notranslate ? ',notranslate' : '');\n  }\n\n  if (config.dangerouslyDisableGooglebot) {\n    defaults.disableGooglebot = true;\n  }\n\n  if (noindex || nofollow) {\n    if (config.dangerouslySetAllPagesToNoIndex) {\n      defaults.noindex = true;\n    }\n\n    if (config.dangerouslySetAllPagesToNoFollow) {\n      defaults.nofollow = true;\n    }\n\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n      key: \"robots\",\n      name: \"robots\",\n      content: (noindex ? 'noindex' : 'index') + \",\" + (nofollow ? 'nofollow' : 'follow') + robotsParams\n    }));\n\n    if (!disableGooglebot) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"googlebot\",\n        name: \"googlebot\",\n        content: (noindex ? 'noindex' : 'index') + \",\" + (nofollow ? 'nofollow' : 'follow') + robotsParams\n      }));\n    }\n  } else {\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n      key: \"robots\",\n      name: \"robots\",\n      content: \"index,follow\" + robotsParams\n    }));\n\n    if (!disableGooglebot) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"googlebot\",\n        name: \"googlebot\",\n        content: \"index,follow\" + robotsParams\n      }));\n    }\n  }\n\n  if (config.description) {\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n      key: \"description\",\n      name: \"description\",\n      content: config.description\n    }));\n  }\n\n  if (config.mobileAlternate) {\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"link\", {\n      rel: \"alternate\",\n      key: \"mobileAlternate\",\n      media: config.mobileAlternate.media,\n      href: config.mobileAlternate.href\n    }));\n  }\n\n  if (config.languageAlternates && config.languageAlternates.length > 0) {\n    config.languageAlternates.forEach(function (languageAlternate) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"link\", {\n        rel: \"alternate\",\n        key: \"languageAlternate-\" + languageAlternate.hrefLang,\n        hrefLang: languageAlternate.hrefLang,\n        href: languageAlternate.href\n      }));\n    });\n  }\n\n  if (config.twitter) {\n    if (config.twitter.cardType) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"twitter:card\",\n        name: \"twitter:card\",\n        content: config.twitter.cardType\n      }));\n    }\n\n    if (config.twitter.site) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"twitter:site\",\n        name: \"twitter:site\",\n        content: config.twitter.site\n      }));\n    }\n\n    if (config.twitter.handle) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"twitter:creator\",\n        name: \"twitter:creator\",\n        content: config.twitter.handle\n      }));\n    }\n  }\n\n  if (config.facebook) {\n    if (config.facebook.appId) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"fb:app_id\",\n        property: \"fb:app_id\",\n        content: config.facebook.appId\n      }));\n    }\n  }\n\n  if ((_config$openGraph = config.openGraph) != null && _config$openGraph.title || config.title) {\n    var _config$openGraph2;\n\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n      key: \"og:title\",\n      property: \"og:title\",\n      content: ((_config$openGraph2 = config.openGraph) == null ? void 0 : _config$openGraph2.title) || updatedTitle\n    }));\n  }\n\n  if ((_config$openGraph3 = config.openGraph) != null && _config$openGraph3.description || config.description) {\n    var _config$openGraph4;\n\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n      key: \"og:description\",\n      property: \"og:description\",\n      content: ((_config$openGraph4 = config.openGraph) == null ? void 0 : _config$openGraph4.description) || config.description\n    }));\n  }\n\n  if (config.openGraph) {\n    if (config.openGraph.url || config.canonical) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:url\",\n        property: \"og:url\",\n        content: config.openGraph.url || config.canonical\n      }));\n    }\n\n    if (config.openGraph.type) {\n      var type = config.openGraph.type.toLowerCase();\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:type\",\n        property: \"og:type\",\n        content: type\n      }));\n\n      if (type === 'profile' && config.openGraph.profile) {\n        if (config.openGraph.profile.firstName) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"profile:first_name\",\n            property: \"profile:first_name\",\n            content: config.openGraph.profile.firstName\n          }));\n        }\n\n        if (config.openGraph.profile.lastName) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"profile:last_name\",\n            property: \"profile:last_name\",\n            content: config.openGraph.profile.lastName\n          }));\n        }\n\n        if (config.openGraph.profile.username) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"profile:username\",\n            property: \"profile:username\",\n            content: config.openGraph.profile.username\n          }));\n        }\n\n        if (config.openGraph.profile.gender) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"profile:gender\",\n            property: \"profile:gender\",\n            content: config.openGraph.profile.gender\n          }));\n        }\n      } else if (type === 'book' && config.openGraph.book) {\n        if (config.openGraph.book.authors && config.openGraph.book.authors.length) {\n          config.openGraph.book.authors.forEach(function (author, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"book:author:0\" + index,\n              property: \"book:author\",\n              content: author\n            }));\n          });\n        }\n\n        if (config.openGraph.book.isbn) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"book:isbn\",\n            property: \"book:isbn\",\n            content: config.openGraph.book.isbn\n          }));\n        }\n\n        if (config.openGraph.book.releaseDate) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"book:release_date\",\n            property: \"book:release_date\",\n            content: config.openGraph.book.releaseDate\n          }));\n        }\n\n        if (config.openGraph.book.tags && config.openGraph.book.tags.length) {\n          config.openGraph.book.tags.forEach(function (tag, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"book:tag:0\" + index,\n              property: \"book:tag\",\n              content: tag\n            }));\n          });\n        }\n      } else if (type === 'article' && config.openGraph.article) {\n        if (config.openGraph.article.publishedTime) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"article:published_time\",\n            property: \"article:published_time\",\n            content: config.openGraph.article.publishedTime\n          }));\n        }\n\n        if (config.openGraph.article.modifiedTime) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"article:modified_time\",\n            property: \"article:modified_time\",\n            content: config.openGraph.article.modifiedTime\n          }));\n        }\n\n        if (config.openGraph.article.expirationTime) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"article:expiration_time\",\n            property: \"article:expiration_time\",\n            content: config.openGraph.article.expirationTime\n          }));\n        }\n\n        if (config.openGraph.article.authors && config.openGraph.article.authors.length) {\n          config.openGraph.article.authors.forEach(function (author, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"article:author:0\" + index,\n              property: \"article:author\",\n              content: author\n            }));\n          });\n        }\n\n        if (config.openGraph.article.section) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"article:section\",\n            property: \"article:section\",\n            content: config.openGraph.article.section\n          }));\n        }\n\n        if (config.openGraph.article.tags && config.openGraph.article.tags.length) {\n          config.openGraph.article.tags.forEach(function (tag, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"article:tag:0\" + index,\n              property: \"article:tag\",\n              content: tag\n            }));\n          });\n        }\n      } else if ((type === 'video.movie' || type === 'video.episode' || type === 'video.tv_show' || type === 'video.other') && config.openGraph.video) {\n        if (config.openGraph.video.actors && config.openGraph.video.actors.length) {\n          config.openGraph.video.actors.forEach(function (actor, index) {\n            if (actor.profile) {\n              tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n                key: \"video:actor:0\" + index,\n                property: \"video:actor\",\n                content: actor.profile\n              }));\n            }\n\n            if (actor.role) {\n              tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n                key: \"video:actor:role:0\" + index,\n                property: \"video:actor:role\",\n                content: actor.role\n              }));\n            }\n          });\n        }\n\n        if (config.openGraph.video.directors && config.openGraph.video.directors.length) {\n          config.openGraph.video.directors.forEach(function (director, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"video:director:0\" + index,\n              property: \"video:director\",\n              content: director\n            }));\n          });\n        }\n\n        if (config.openGraph.video.writers && config.openGraph.video.writers.length) {\n          config.openGraph.video.writers.forEach(function (writer, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"video:writer:0\" + index,\n              property: \"video:writer\",\n              content: writer\n            }));\n          });\n        }\n\n        if (config.openGraph.video.duration) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"video:duration\",\n            property: \"video:duration\",\n            content: config.openGraph.video.duration.toString()\n          }));\n        }\n\n        if (config.openGraph.video.releaseDate) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"video:release_date\",\n            property: \"video:release_date\",\n            content: config.openGraph.video.releaseDate\n          }));\n        }\n\n        if (config.openGraph.video.tags && config.openGraph.video.tags.length) {\n          config.openGraph.video.tags.forEach(function (tag, index) {\n            tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n              key: \"video:tag:0\" + index,\n              property: \"video:tag\",\n              content: tag\n            }));\n          });\n        }\n\n        if (config.openGraph.video.series) {\n          tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n            key: \"video:series\",\n            property: \"video:series\",\n            content: config.openGraph.video.series\n          }));\n        }\n      }\n    } // images\n\n\n    if (config.defaultOpenGraphImageWidth) {\n      defaults.defaultOpenGraphImageWidth = config.defaultOpenGraphImageWidth;\n    }\n\n    if (config.defaultOpenGraphImageHeight) {\n      defaults.defaultOpenGraphImageHeight = config.defaultOpenGraphImageHeight;\n    }\n\n    if (config.openGraph.images && config.openGraph.images.length) {\n      tagsToRender.push.apply(tagsToRender, buildOpenGraphMediaTags('image', config.openGraph.images, {\n        defaultWidth: defaults.defaultOpenGraphImageWidth,\n        defaultHeight: defaults.defaultOpenGraphImageHeight\n      }));\n    } // videos\n\n\n    if (config.defaultOpenGraphVideoWidth) {\n      defaults.defaultOpenGraphVideoWidth = config.defaultOpenGraphVideoWidth;\n    }\n\n    if (config.defaultOpenGraphVideoHeight) {\n      defaults.defaultOpenGraphVideoHeight = config.defaultOpenGraphVideoHeight;\n    }\n\n    if (config.openGraph.videos && config.openGraph.videos.length) {\n      tagsToRender.push.apply(tagsToRender, buildOpenGraphMediaTags('video', config.openGraph.videos, {\n        defaultWidth: defaults.defaultOpenGraphVideoWidth,\n        defaultHeight: defaults.defaultOpenGraphVideoHeight\n      }));\n    }\n\n    if (config.openGraph.locale) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:locale\",\n        property: \"og:locale\",\n        content: config.openGraph.locale\n      }));\n    }\n\n    if (config.openGraph.site_name) {\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", {\n        key: \"og:site_name\",\n        property: \"og:site_name\",\n        content: config.openGraph.site_name\n      }));\n    }\n  }\n\n  if (config.canonical) {\n    tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"link\", {\n      rel: \"canonical\",\n      href: config.canonical,\n      key: \"canonical\"\n    }));\n  }\n\n  if (config.additionalMetaTags && config.additionalMetaTags.length > 0) {\n    config.additionalMetaTags.forEach(function (tag) {\n      var _ref2, _ref3, _tag$keyOverride;\n\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"meta\", _extends({\n        key: \"meta:\" + ((_ref2 = (_ref3 = (_tag$keyOverride = tag.keyOverride) != null ? _tag$keyOverride : tag.name) != null ? _ref3 : tag.property) != null ? _ref2 : tag.httpEquiv)\n      }, tag)));\n    });\n  }\n\n  if ((_config$additionalLin = config.additionalLinkTags) != null && _config$additionalLin.length) {\n    config.additionalLinkTags.forEach(function (tag) {\n      var _tag$keyOverride2;\n\n      tagsToRender.push(react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"link\", _extends({\n        key: \"link\" + ((_tag$keyOverride2 = tag.keyOverride) != null ? _tag$keyOverride2 : tag.href) + tag.rel\n      }, tag)));\n    });\n  }\n\n  return tagsToRender;\n};\n\nvar DefaultSeo = /*#__PURE__*/function (_Component) {\n  _inheritsLoose(DefaultSeo, _Component);\n\n  function DefaultSeo() {\n    return _Component.apply(this, arguments) || this;\n  }\n\n  var _proto = DefaultSeo.prototype;\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        title = _this$props.title,\n        titleTemplate = _this$props.titleTemplate,\n        defaultTitle = _this$props.defaultTitle,\n        _this$props$dangerous = _this$props.dangerouslyDisableGooglebot,\n        dangerouslyDisableGooglebot = _this$props$dangerous === void 0 ? false : _this$props$dangerous,\n        _this$props$dangerous2 = _this$props.dangerouslySetAllPagesToNoIndex,\n        dangerouslySetAllPagesToNoIndex = _this$props$dangerous2 === void 0 ? false : _this$props$dangerous2,\n        _this$props$dangerous3 = _this$props.dangerouslySetAllPagesToNoFollow,\n        dangerouslySetAllPagesToNoFollow = _this$props$dangerous3 === void 0 ? false : _this$props$dangerous3,\n        description = _this$props.description,\n        canonical = _this$props.canonical,\n        facebook = _this$props.facebook,\n        openGraph = _this$props.openGraph,\n        additionalMetaTags = _this$props.additionalMetaTags,\n        twitter = _this$props.twitter,\n        defaultOpenGraphImageWidth = _this$props.defaultOpenGraphImageWidth,\n        defaultOpenGraphImageHeight = _this$props.defaultOpenGraphImageHeight,\n        defaultOpenGraphVideoWidth = _this$props.defaultOpenGraphVideoWidth,\n        defaultOpenGraphVideoHeight = _this$props.defaultOpenGraphVideoHeight,\n        mobileAlternate = _this$props.mobileAlternate,\n        languageAlternates = _this$props.languageAlternates,\n        additionalLinkTags = _this$props.additionalLinkTags;\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, buildTags({\n      title: title,\n      titleTemplate: titleTemplate,\n      defaultTitle: defaultTitle,\n      dangerouslySetAllPagesToNoIndex: dangerouslySetAllPagesToNoIndex,\n      dangerouslySetAllPagesToNoFollow: dangerouslySetAllPagesToNoFollow,\n      description: description,\n      canonical: canonical,\n      facebook: facebook,\n      openGraph: openGraph,\n      additionalMetaTags: additionalMetaTags,\n      twitter: twitter,\n      defaultOpenGraphImageWidth: defaultOpenGraphImageWidth,\n      defaultOpenGraphImageHeight: defaultOpenGraphImageHeight,\n      defaultOpenGraphVideoWidth: defaultOpenGraphVideoWidth,\n      defaultOpenGraphVideoHeight: defaultOpenGraphVideoHeight,\n      mobileAlternate: mobileAlternate,\n      languageAlternates: languageAlternates,\n      additionalLinkTags: additionalLinkTags,\n      dangerouslyDisableGooglebot: dangerouslyDisableGooglebot\n    }));\n  };\n\n  return DefaultSeo;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar NextSeo = /*#__PURE__*/function (_Component) {\n  _inheritsLoose(NextSeo, _Component);\n\n  function NextSeo() {\n    return _Component.apply(this, arguments) || this;\n  }\n\n  var _proto = NextSeo.prototype;\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        title = _this$props.title,\n        _this$props$noindex = _this$props.noindex,\n        noindex = _this$props$noindex === void 0 ? false : _this$props$noindex,\n        nofollow = _this$props.nofollow,\n        robotsProps = _this$props.robotsProps,\n        description = _this$props.description,\n        canonical = _this$props.canonical,\n        openGraph = _this$props.openGraph,\n        facebook = _this$props.facebook,\n        twitter = _this$props.twitter,\n        additionalMetaTags = _this$props.additionalMetaTags,\n        titleTemplate = _this$props.titleTemplate,\n        mobileAlternate = _this$props.mobileAlternate,\n        languageAlternates = _this$props.languageAlternates,\n        additionalLinkTags = _this$props.additionalLinkTags,\n        disableGooglebot = _this$props.disableGooglebot;\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, buildTags({\n      title: title,\n      noindex: noindex,\n      nofollow: nofollow,\n      robotsProps: robotsProps,\n      description: description,\n      canonical: canonical,\n      facebook: facebook,\n      openGraph: openGraph,\n      additionalMetaTags: additionalMetaTags,\n      twitter: twitter,\n      titleTemplate: titleTemplate,\n      mobileAlternate: mobileAlternate,\n      languageAlternates: languageAlternates,\n      additionalLinkTags: additionalLinkTags,\n      disableGooglebot: disableGooglebot\n    }));\n  };\n\n  return NextSeo;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar markup = function markup(jsonld) {\n  return {\n    __html: jsonld\n  };\n};\n\nvar formatAuthorName = function formatAuthorName(authorName) {\n  return Array.isArray(authorName) ? \"[\" + authorName.map(function (name) {\n    return \"{\\\"@type\\\": \\\"Person\\\",\\\"name\\\": \\\"\" + name + \"\\\"}\";\n  }) + \"]\" : \"{\\\"@type\\\": \\\"Person\\\",\\\"name\\\": \\\"\" + authorName + \"\\\"}\";\n};\n\nvar ArticleJsonLd = function ArticleJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      url = _ref.url,\n      title = _ref.title,\n      _ref$images = _ref.images,\n      images = _ref$images === void 0 ? [] : _ref$images,\n      datePublished = _ref.datePublished,\n      _ref$dateModified = _ref.dateModified,\n      dateModified = _ref$dateModified === void 0 ? null : _ref$dateModified,\n      authorName = _ref.authorName,\n      description = _ref.description,\n      publisherName = _ref.publisherName,\n      publisherLogo = _ref.publisherLogo;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Article\\\",\\n    \\\"mainEntityOfPage\\\": {\\n      \\\"@type\\\": \\\"WebPage\\\",\\n      \\\"@id\\\": \\\"\" + url + \"\\\"\\n    },\\n    \\\"headline\\\": \\\"\" + title + \"\\\",\\n    \\\"image\\\": [\\n      \" + images.map(function (image) {\n    return \"\\\"\" + image + \"\\\"\";\n  }) + \"\\n     ],\\n    \\\"datePublished\\\": \\\"\" + datePublished + \"\\\",\\n    \\\"dateModified\\\": \\\"\" + (dateModified || datePublished) + \"\\\",\\n    \\\"author\\\": \" + formatAuthorName(authorName) + \",\\n    \\\"publisher\\\": {\\n      \\\"@type\\\": \\\"Organization\\\",\\n      \\\"name\\\": \\\"\" + publisherName + \"\\\",\\n      \\\"logo\\\": {\\n        \\\"@type\\\": \\\"ImageObject\\\",\\n        \\\"url\\\": \\\"\" + publisherLogo + \"\\\"\\n      }\\n    },\\n    \\\"description\\\": \\\"\" + description + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-article\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar BreadCrumbJsonLd = function BreadCrumbJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      _ref$itemListElements = _ref.itemListElements,\n      itemListElements = _ref$itemListElements === void 0 ? [] : _ref$itemListElements;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"BreadcrumbList\\\",\\n    \\\"itemListElement\\\": [\\n      \" + itemListElements.map(function (itemListElement) {\n    return \"{\\n        \\\"@type\\\": \\\"ListItem\\\",\\n        \\\"position\\\": \" + itemListElement.position + \",\\n        \\\"item\\\": {\\n          \\\"@id\\\": \\\"\" + itemListElement.item + \"\\\",\\n          \\\"name\\\": \\\"\" + itemListElement.name + \"\\\"\\n        }\\n      }\";\n  }) + \"\\n     ]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-breadcrumb\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar buildQuestions = function buildQuestions(mainEntity) {\n  return \"\\n  \" + mainEntity.map(function (question) {\n    return \"{\\n      \\\"@type\\\": \\\"Question\\\",\\n      \\\"name\\\": \\\"\" + question.questionName + \"\\\",\\n      \\\"acceptedAnswer\\\": {\\n        \\\"@type\\\": \\\"Answer\\\",\\n        \\\"text\\\": \\\"\" + question.acceptedAnswerText + \"\\\"\\n      }\\n  }\";\n  });\n};\n\nvar FAQPageJsonLd = function FAQPageJsonLd(_ref) {\n  var _ref$mainEntity = _ref.mainEntity,\n      mainEntity = _ref$mainEntity === void 0 ? [] : _ref$mainEntity;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org/\\\",\\n    \\\"@type\\\": \\\"FAQPage\\\",\\n    \\\"mainEntity\\\": [\" + buildQuestions(mainEntity) + \"]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-faq-page\"\n  }));\n};\n\nvar buildBaseSalary = function buildBaseSalary(baseSalary) {\n  return \"\\n  \\\"baseSalary\\\": {\\n    \\\"@type\\\": \\\"MonetaryAmount\\\",\\n    \" + (baseSalary.currency ? \"\\\"currency\\\": \\\"\" + baseSalary.currency + \"\\\",\" : '') + \"\\n    \\\"value\\\": {\\n      \" + (baseSalary.value ? Array.isArray(baseSalary.value) ? \"\\\"minValue\\\": \\\"\" + baseSalary.value[0] + \"\\\", \\\"maxValue\\\": \\\"\" + baseSalary.value[1] + \"\\\",\" : \"\\\"value\\\": \\\"\" + baseSalary.value + \"\\\",\" : '') + \"\\n      \" + (baseSalary.unitText ? \"\\\"unitText\\\": \\\"\" + baseSalary.unitText + \"\\\",\" : '') + \"\\n      \\\"@type\\\": \\\"QuantitativeValue\\\"\\n    }\\n  },\\n\";\n};\n\nvar JobPostingJsonLd = function JobPostingJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      baseSalary = _ref.baseSalary,\n      datePosted = _ref.datePosted,\n      description = _ref.description,\n      employmentType = _ref.employmentType,\n      hiringOrganization = _ref.hiringOrganization,\n      jobLocation = _ref.jobLocation,\n      applicantLocationRequirements = _ref.applicantLocationRequirements,\n      jobLocationType = _ref.jobLocationType,\n      title = _ref.title,\n      validThrough = _ref.validThrough;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"JobPosting\\\",\\n    \" + (baseSalary ? buildBaseSalary(baseSalary) : '') + \"\\n    \\\"datePosted\\\": \\\"\" + datePosted + \"\\\",\\n    \\\"description\\\": \\\"\" + description + \"\\\",\\n    \" + (employmentType ? \"\\\"employmentType\\\": \\\"\" + employmentType + \"\\\",\" : '') + \"\\n    \\\"hiringOrganization\\\" : {\\n      \\\"@type\\\" : \\\"Organization\\\",\\n      \\\"name\\\" : \\\"\" + hiringOrganization.name + \"\\\",\\n      \\\"sameAs\\\" : \\\"\" + hiringOrganization.sameAs + \"\\\"\\n      \" + (hiringOrganization.logo ? \",\\\"logo\\\": \\\"\" + hiringOrganization.logo + \"\\\"\" : '') + \"\\n    },\\n    \" + (jobLocation ? \"\\\"jobLocation\\\": {\\n      \\\"@type\\\": \\\"Place\\\",\\n      \\\"address\\\": {\\n        \\\"@type\\\": \\\"PostalAddress\\\",\\n        \\\"addressLocality\\\": \\\"\" + jobLocation.addressLocality + \"\\\",\\n        \\\"addressRegion\\\": \\\"\" + jobLocation.addressRegion + \"\\\",\\n        \\\"postalCode\\\" : \\\"\" + jobLocation.postalCode + \"\\\",\\n        \\\"streetAddress\\\" : \\\"\" + jobLocation.streetAddress + \"\\\",\\n        \\\"addressCountry\\\" : \\\"\" + jobLocation.addressCountry + \"\\\"\\n          }\\n      },\" : '') + \"\\n    \" + (applicantLocationRequirements ? \" \\\"applicantLocationRequirements\\\": {\\n        \\\"@type\\\": \\\"Country\\\",\\n        \\\"name\\\": \\\"\" + applicantLocationRequirements + \"\\\"\\n    },\" : '') + \"\\n    \" + (jobLocationType ? \"\\\"jobLocationType\\\": \\\"\" + jobLocationType + \"\\\",\" : '') + \"\\n    \" + (validThrough ? \"\\\"validThrough\\\": \\\"\" + validThrough + \"\\\",\" : '') + \"\\n    \\\"title\\\": \\\"\" + title + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-jobposting\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar BlogJsonLd = function BlogJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      url = _ref.url,\n      title = _ref.title,\n      _ref$images = _ref.images,\n      images = _ref$images === void 0 ? [] : _ref$images,\n      datePublished = _ref.datePublished,\n      _ref$dateModified = _ref.dateModified,\n      dateModified = _ref$dateModified === void 0 ? null : _ref$dateModified,\n      authorName = _ref.authorName,\n      description = _ref.description;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Blog\\\",\\n    \\\"mainEntityOfPage\\\": {\\n      \\\"@type\\\": \\\"WebPage\\\",\\n      \\\"@id\\\": \\\"\" + url + \"\\\"\\n    },\\n    \\\"headline\\\": \\\"\" + title + \"\\\",\\n    \\\"image\\\": [\\n      \" + images.map(function (image) {\n    return \"\\\"\" + image + \"\\\"\";\n  }) + \"\\n     ],\\n    \\\"datePublished\\\": \\\"\" + datePublished + \"\\\",\\n    \\\"dateModified\\\": \\\"\" + (dateModified || datePublished) + \"\\\",\\n    \\\"author\\\": \" + formatAuthorName(authorName) + \",\\n    \\\"description\\\": \\\"\" + description + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-blog\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar CourseJsonLd = function CourseJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      courseName = _ref.courseName,\n      description = _ref.description,\n      providerName = _ref.providerName,\n      providerUrl = _ref.providerUrl;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Course\\\",\\n    \\\"name\\\": \\\"\" + courseName + \"\\\",\\n    \\\"description\\\": \\\"\" + description + \"\\\",\\n    \\\"provider\\\": {\\n      \\\"@type\\\": \\\"Organization\\\",\\n      \\\"name\\\": \\\"\" + providerName + \"\\\"\" + (providerUrl ? \",\\n      \\\"sameAs\\\": \\\"\" + providerUrl + \"\\\"\" : '') + \"\\n    }\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-course\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar DatasetJsonLd = function DatasetJsonLd(_ref) {\n  var description = _ref.description,\n      name = _ref.name,\n      license = _ref.license;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Dataset\\\",\\n    \\\"description\\\": \\\"\" + description + \"\\\",\\n    \\\"name\\\": \\\"\" + name + \"\\\"\" + (license ? \",\\n        \\\"license\\\": \\\"\" + license + \"\\\"\" : '') + \"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-dataset\"\n  }));\n};\n\nvar formatIfArray = function formatIfArray(value) {\n  return Array.isArray(value) ? \"[\" + value.map(function (val) {\n    return \"\\\"\" + val + \"\\\"\";\n  }) + \"]\" : \"\\\"\" + value + \"\\\"\";\n};\n\nvar buildAddress = (function (address) {\n  return \"\\n  \\\"address\\\": {\\n    \\\"@type\\\": \\\"PostalAddress\\\",\\n    \\\"streetAddress\\\": \\\"\" + address.streetAddress + \"\\\",\\n    \\\"addressLocality\\\": \\\"\" + address.addressLocality + \"\\\",\\n    \" + (address.addressRegion ? \"\\\"addressRegion\\\": \\\"\" + address.addressRegion + \"\\\",\" : '') + \"\\n    \\\"postalCode\\\": \\\"\" + address.postalCode + \"\\\",\\n    \\\"addressCountry\\\": \\\"\" + address.addressCountry + \"\\\"\\n  },\\n\";\n});\n\nvar buildAction = function buildAction(action) {\n  return \"\\n  \\\"\" + action.actionName + \"\\\": {\\n    \\\"@type\\\": \\\"\" + action.actionType + \"\\\",\\n    \\\"target\\\": \\\"\" + action.target + \"\\\"\\n  }\\n\";\n};\n\nvar buildAreaServed = function buildAreaServed(areaServed) {\n  return \"\\n  \\\"areaServed\\\": [\\n    \" + areaServed.map(function (area) {\n    return buildGeoCircle(area);\n  }) + \"\\n  ]\\n\";\n};\n\nvar buildAggregateRating = function buildAggregateRating(aggregateRating) {\n  return \"\\n  \\\"aggregateRating\\\": {\\n    \\\"@type\\\": \\\"AggregateRating\\\",\\n    \\\"ratingValue\\\": \\\"\" + aggregateRating.ratingValue + \"\\\",\\n    \\\"ratingCount\\\": \\\"\" + aggregateRating.ratingCount + \"\\\"\\n  },\\n\";\n};\n\nvar buildGeo = function buildGeo(geo) {\n  return \"\\n  \\\"geo\\\": {\\n    \\\"@type\\\": \\\"GeoCoordinates\\\",\\n    \\\"latitude\\\": \\\"\" + geo.latitude + \"\\\",\\n    \\\"longitude\\\": \\\"\" + geo.longitude + \"\\\"\\n  },\\n\";\n};\n\nvar buildGeoCircle = function buildGeoCircle(geoCircle) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"GeoCircle\\\",\\n    \\\"geoMidpoint\\\": {\\n      \\\"@type\\\": \\\"GeoCoordinates\\\",\\n      \\\"latitude\\\": \\\"\" + geoCircle.geoMidpoint.latitude + \"\\\",\\n      \\\"longitude\\\": \\\"\" + geoCircle.geoMidpoint.longitude + \"\\\"\\n    },\\n    \\\"geoRadius\\\": \\\"\" + geoCircle.geoRadius + \"\\\"\\n  }\\n\";\n};\n\nvar buildMakesOffer = function buildMakesOffer(makesOffer) {\n  return \"\\n  \\\"makesOffer\\\":[\\n    \" + makesOffer.map(function (offer) {\n    return buildOffer(offer);\n  }) + \"\\n  ]\\n\";\n};\n\nvar buildOffer = function buildOffer(offer) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"Offer\\\",\\n    \" + buildPriceSpecification(offer.priceSpecification) + \",\\n    \" + buildItemOffered(offer.itemOffered) + \"\\n  }\\n\";\n};\n\nvar buildOpeningHours = function buildOpeningHours(openingHours) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"OpeningHoursSpecification\\\",\\n    \" + (openingHours.dayOfWeek ? \"\\\"dayOfWeek\\\": \" + formatIfArray(openingHours.dayOfWeek) + \",\" : '') + \"\\n    \\\"opens\\\": \\\"\" + openingHours.opens + \"\\\",\\n    \" + (openingHours.validFrom ? \"\\\"validFrom\\\": \\\"\" + openingHours.validFrom + \"\\\",\" : '') + \"\\n    \" + (openingHours.validThrough ? \"\\\"validThrough\\\": \\\"\" + openingHours.validThrough + \"\\\",\" : '') + \"\\n    \\\"closes\\\": \\\"\" + openingHours.closes + \"\\\"\\n  }\\n\";\n};\n\nvar buildPriceSpecification = function buildPriceSpecification(priceSpecification) {\n  return \"\\n  \\\"priceSpecification\\\": {\\n    \\\"@type\\\": \\\"\" + priceSpecification.type + \"\\\",\\n    \\\"priceCurrency\\\": \\\"\" + priceSpecification.priceCurrency + \"\\\",\\n    \\\"price\\\": \\\"\" + priceSpecification.price + \"\\\"\\n  }\\n\";\n};\n\nvar buildRating = function buildRating(rating) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"Rating\\\",\\n    \" + (rating.bestRating ? \"\\\"bestRating\\\": \\\"\" + rating.bestRating + \"\\\",\" : '') + \"\\n    \" + (rating.reviewAspect ? \"\\\"reviewAspect\\\": \\\"\" + rating.reviewAspect + \"\\\",\" : '') + \"\\n    \" + (rating.worstRating ? \"\\\"worstRating\\\": \\\"\" + rating.worstRating + \"\\\",\" : '') + \"\\n    \\\"ratingValue\\\": \\\"\" + rating.ratingValue + \"\\\"\\n  }\\n\";\n};\n\nvar buildReview = function buildReview(reviews) {\n  return \"\\n  \\\"review\\\": [\\n    \" + reviews.map(function (review) {\n    return \"\\n      {\\n        \\\"@type\\\": \\\"Review\\\",\\n        \\\"author\\\": \\\"\" + review.author + \"\\\",\\n        \\\"datePublished\\\": \\\"\" + review.datePublished + \"\\\",\\n        \" + (review.name ? \"\\\"name\\\": \\\"\" + review.name + \"\\\",\" : '') + \"\\n        \\\"reviewBody\\\": \\\"\" + review.reviewBody + \"\\\",\\n        \\\"reviewRating\\\": \" + buildRating(review.reviewRating) + \"\\n      }\\n    \";\n  }) + \"\\n  ],\\n\";\n};\n\nvar buildItemOffered = function buildItemOffered(service) {\n  return \"\\n  \\\"itemOffered\\\": {\\n    \\\"@type\\\": \\\"Service\\\",\\n    \\\"name\\\": \\\"\" + service.name + \"\\\",\\n    \\\"description\\\": \\\"\" + service.description + \"\\\"\\n  }\\n\";\n};\n\nvar LocalBusinessJsonLd = function LocalBusinessJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      type = _ref.type,\n      id = _ref.id,\n      name = _ref.name,\n      description = _ref.description,\n      url = _ref.url,\n      telephone = _ref.telephone,\n      address = _ref.address,\n      geo = _ref.geo,\n      images = _ref.images,\n      rating = _ref.rating,\n      review = _ref.review,\n      priceRange = _ref.priceRange,\n      servesCuisine = _ref.servesCuisine,\n      sameAs = _ref.sameAs,\n      openingHours = _ref.openingHours,\n      action = _ref.action,\n      areaServed = _ref.areaServed,\n      makesOffer = _ref.makesOffer;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"\" + type + \"\\\",\\n    \" + (id ? \"\\\"@id\\\": \\\"\" + id + \"\\\",\" : '') + \"\\n    \" + (description ? \"\\\"description\\\": \\\"\" + description + \"\\\",\" : '') + \"\\n    \" + (url ? \"\\\"url\\\": \\\"\" + url + \"\\\",\" : '') + \"\\n    \" + (telephone ? \"\\\"telephone\\\": \\\"\" + telephone + \"\\\",\" : '') + \"\\n    \" + buildAddress(address) + \"\\n    \" + (geo ? \"\" + buildGeo(geo) : '') + \"\\n    \" + (rating ? \"\" + buildAggregateRating(rating) : '') + \"\\n    \" + (review ? \"\" + buildReview(review) : '') + \"\\n    \" + (action ? buildAction(action) + \",\" : '') + \"\\n    \" + (areaServed ? buildAreaServed(areaServed) + \",\" : '') + \"\\n    \" + (makesOffer ? buildMakesOffer(makesOffer) + \",\" : '') + \"\\n    \" + (priceRange ? \"\\\"priceRange\\\": \\\"\" + priceRange + \"\\\",\" : '') + \"\\n    \" + (servesCuisine ? \"\\\"servesCuisine\\\":\" + formatIfArray(servesCuisine) + \",\" : '') + \"\\n    \" + (images ? \"\\\"image\\\":\" + formatIfArray(images) + \",\" : '') + \"\\n    \" + (sameAs ? \"\\\"sameAs\\\": [\" + sameAs.map(function (url) {\n    return \"\\\"\" + url + \"\\\"\";\n  }) + \"],\" : '') + \"\\n    \" + (openingHours ? \"\\\"openingHoursSpecification\\\": \" + (Array.isArray(openingHours) ? \"[\" + openingHours.map(function (hours) {\n    return \"\" + buildOpeningHours(hours);\n  }) + \"]\" : buildOpeningHours(openingHours)) + \",\" : '') + \"\\n    \\\"name\\\": \\\"\" + name + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-local-business\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar LogoJsonLd = function LogoJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      url = _ref.url,\n      logo = _ref.logo;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Organization\\\",\\n    \\\"url\\\": \\\"\" + url + \"\\\",\\n    \\\"logo\\\": \\\"\" + logo + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-logo\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\n// TODO: Docs for offers itemCondition & availability\n// TODO: Seller type, make dynamic\nvar buildOffers = function buildOffers(offers) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"Offer\\\",\\n    \\\"priceCurrency\\\": \\\"\" + offers.priceCurrency + \"\\\",\\n    \" + (offers.priceValidUntil ? \"\\\"priceValidUntil\\\": \\\"\" + offers.priceValidUntil + \"\\\",\" : '') + \"\\n    \" + (offers.itemCondition ? \"\\\"itemCondition\\\": \\\"\" + offers.itemCondition + \"\\\",\" : '') + \"\\n    \" + (offers.availability ? \"\\\"availability\\\": \\\"\" + offers.availability + \"\\\",\" : '') + \"\\n    \" + (offers.url ? \"\\\"url\\\": \\\"\" + offers.url + \"\\\",\" : '') + \"\\n    \" + (offers.seller ? \"\\n      \\\"seller\\\": {\\n      \\\"@type\\\": \\\"Organization\\\",\\n      \\\"name\\\": \\\"\" + offers.seller.name + \"\\\"\\n    },\\n    \" : '') + \"\\n    \\\"price\\\": \\\"\" + offers.price + \"\\\"\\n  }\\n\";\n};\n\nvar buildAggregateOffer = function buildAggregateOffer(offer) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"AggregateOffer\\\",\\n    \\\"priceCurrency\\\": \\\"\" + offer.priceCurrency + \"\\\",\\n    \" + (offer.highPrice ? \"\\\"highPrice\\\": \\\"\" + offer.highPrice + \"\\\",\" : '') + \"\\n    \" + (offer.offerCount ? \"\\\"offerCount\\\": \\\"\" + offer.offerCount + \"\\\",\" : '') + \"\\n    \" + (offer.offers ? \"\\\"offers\\\": \" + (Array.isArray(offer.offers) ? \"[\" + offer.offers.map(function (offer) {\n    return \"\" + buildOffers(offer);\n  }) + \"]\" : buildOffers(offer.offers)) + \",\" : '') + \"\\n    \\\"lowPrice\\\": \\\"\" + offer.lowPrice + \"\\\"\\n  }\\n\";\n};\n\nvar buildAggregateRating$1 = function buildAggregateRating(aggregateRating) {\n  return \"\\n  \\\"aggregateRating\\\": {\\n      \\\"@type\\\": \\\"AggregateRating\\\",\\n      \" + (aggregateRating.ratingCount ? \"\\\"ratingCount\\\": \\\"\" + aggregateRating.ratingCount + \"\\\",\" : '') + \"\\n      \" + (aggregateRating.reviewCount ? \"\\\"reviewCount\\\": \\\"\" + aggregateRating.reviewCount + \"\\\",\" : '') + \"\\n      \" + (aggregateRating.bestRating ? \"\\\"bestRating\\\": \\\"\" + aggregateRating.bestRating + \"\\\",\" : '') + \"\\n      \\\"ratingValue\\\": \\\"\" + aggregateRating.ratingValue + \"\\\"\\n    },\\n\";\n};\n\nvar buildReviewRating = function buildReviewRating(rating) {\n  return rating ? \"\\\"reviewRating\\\": {\\n          \\\"@type\\\": \\\"Rating\\\",\\n          \" + (rating.bestRating ? \"\\\"bestRating\\\": \\\"\" + rating.bestRating + \"\\\",\" : '') + \"\\n          \" + (rating.worstRating ? \"\\\"worstRating\\\": \\\"\" + rating.worstRating + \"\\\",\" : '') + \"\\n          \\\"ratingValue\\\": \\\"\" + rating.ratingValue + \"\\\"\\n        }\" : '';\n};\nvar buildAuthor = function buildAuthor(author) {\n  return \"\\n  \\\"author\\\": {\\n      \\\"@type\\\": \\\"\" + author.type + \"\\\",\\n      \\\"name\\\": \\\"\" + author.name + \"\\\"\\n  },\\n\";\n};\nvar buildPublisher = function buildPublisher(publisher) {\n  return \"\\n  \\\"publisher\\\": {\\n      \\\"@type\\\": \\\"\" + publisher.type + \"\\\",\\n      \\\"name\\\": \\\"\" + publisher.name + \"\\\"\\n  },\\n\";\n};\nvar buildReviews = function buildReviews(reviews) {\n  return \"\\n  \\\"review\\\": [\\n    \" + reviews.map(function (review) {\n    return \"{\\n        \\\"@type\\\": \\\"Review\\\",\\n        \" + (review.author ? buildAuthor(review.author) : '') + \"\\n        \" + (review.publisher ? buildPublisher(review.publisher) : '') + \"\\n        \" + (review.datePublished ? \"\\\"datePublished\\\": \\\"\" + review.datePublished + \"\\\",\" : '') + \"\\n        \" + (review.reviewBody ? \"\\\"reviewBody\\\": \\\"\" + review.reviewBody + \"\\\",\" : '') + \"\\n        \" + (review.name ? \"\\\"name\\\": \\\"\" + review.name + \"\\\",\" : '') + \"\\n        \" + buildReviewRating(review.reviewRating) + \"\\n    }\";\n  }) + \"\\n  ],\\n\";\n};\n\nvar buildBrand = function buildBrand(brand) {\n  return \"\\n  \\\"brand\\\": {\\n      \\\"@type\\\": \\\"Thing\\\",\\n      \\\"name\\\": \\\"\" + brand + \"\\\"\\n    },\\n\";\n};\n\nvar ProductJsonLd = function ProductJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      productName = _ref.productName,\n      _ref$images = _ref.images,\n      images = _ref$images === void 0 ? [] : _ref$images,\n      description = _ref.description,\n      sku = _ref.sku,\n      gtin8 = _ref.gtin8,\n      gtin13 = _ref.gtin13,\n      gtin14 = _ref.gtin14,\n      mpn = _ref.mpn,\n      brand = _ref.brand,\n      _ref$reviews = _ref.reviews,\n      reviews = _ref$reviews === void 0 ? [] : _ref$reviews,\n      aggregateRating = _ref.aggregateRating,\n      offers = _ref.offers,\n      aggregateOffer = _ref.aggregateOffer,\n      color = _ref.color,\n      manufacturerName = _ref.manufacturerName,\n      manufacturerLogo = _ref.manufacturerLogo,\n      material = _ref.material,\n      slogan = _ref.slogan,\n      disambiguatingDescription = _ref.disambiguatingDescription,\n      productionDate = _ref.productionDate,\n      releaseDate = _ref.releaseDate,\n      purchaseDate = _ref.purchaseDate,\n      award = _ref.award;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org/\\\",\\n    \\\"@type\\\": \\\"Product\\\",\\n    \" + (images.length ? \"\\\"image\\\":\" + formatIfArray(images) + \",\" : '') + \"\\n    \" + (description ? \"\\\"description\\\": \\\"\" + description + \"\\\",\" : '') + \"\\n    \" + (mpn ? \"\\\"mpn\\\": \\\"\" + mpn + \"\\\",\" : '') + \"\\n    \" + (sku ? \"\\\"sku\\\": \\\"\" + sku + \"\\\",\" : '') + \"\\n    \" + (gtin8 ? \"\\\"gtin8\\\": \\\"\" + gtin8 + \"\\\",\" : '') + \"\\n    \" + (gtin13 ? \"\\\"gtin13\\\": \\\"\" + gtin13 + \"\\\",\" : '') + \"\\n    \" + (gtin14 ? \"\\\"gtin14\\\": \\\"\" + gtin14 + \"\\\",\" : '') + \"\\n    \" + (brand ? buildBrand(brand) : '') + \"\\n    \" + (reviews.length ? buildReviews(reviews) : '') + \"\\n    \" + (aggregateRating ? buildAggregateRating$1(aggregateRating) : '') + \"\\n    \" + (color ? \"\\\"color\\\": \\\"\" + color + \"\\\",\" : '') + \"\\n    \" + (material ? \"\\\"material\\\": \\\"\" + material + \"\\\",\" : '') + \"\\n    \" + (slogan ? \"\\\"slogan\\\": \\\"\" + slogan + \"\\\",\" : '') + \"\\n    \" + (disambiguatingDescription ? \"\\\"disambiguatingDescription\\\": \\\"\" + disambiguatingDescription + \"\\\",\" : '') + \"\\n    \" + (productionDate ? \"\\\"productionDate\\\": \\\"\" + productionDate + \"\\\",\" : '') + \"\\n    \" + (releaseDate ? \"\\\"releaseDate\\\": \\\"\" + releaseDate + \"\\\",\" : '') + \"\\n    \" + (purchaseDate ? \"\\\"purchaseDate\\\": \\\"\" + purchaseDate + \"\\\",\" : '') + \"\\n    \" + (award ? \"\\\"award\\\": \\\"\" + award + \"\\\",\" : '') + \"\\n    \" + (manufacturerName ? \"\\n        \\\"manufacturer\\\": {\\n          \\\"@type\\\": \\\"Organization\\\",\\n          \" + (manufacturerLogo ? \"\\n              \\\"logo\\\": {\\n                \\\"@type\\\": \\\"ImageObject\\\",\\n                \\\"url\\\": \\\"\" + manufacturerLogo + \"\\\"\\n              },\\n              \" : '') + \"\\n          \\\"name\\\": \\\"\" + manufacturerName + \"\\\"\\n        },\\n        \" : '') + \"\\n    \" + (offers ? \"\\\"offers\\\": \" + (Array.isArray(offers) ? \"[\" + offers.map(function (offer) {\n    return \"\" + buildOffers(offer);\n  }) + \"]\" : buildOffers(offers)) + \",\" : '') + \"\\n    \" + (aggregateOffer && !offers ? \"\\\"offers\\\": \" + buildAggregateOffer(aggregateOffer) + \",\" : '') + \"\\n    \\\"name\\\": \\\"\" + productName + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-product\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar SocialProfileJsonLd = function SocialProfileJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      type = _ref.type,\n      name = _ref.name,\n      url = _ref.url,\n      _ref$sameAs = _ref.sameAs,\n      sameAs = _ref$sameAs === void 0 ? [] : _ref$sameAs;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"\" + type + \"\\\",\\n    \\\"name\\\": \\\"\" + name + \"\\\",\\n    \\\"url\\\": \\\"\" + url + \"\\\",\\n    \\\"sameAs\\\": [\\n      \" + sameAs.map(function (socialUrl) {\n    return \"\\\"\" + socialUrl + \"\\\"\";\n  }) + \"\\n     ]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-social\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar formatIfArray$1 = function formatIfArray(value) {\n  return Array.isArray(value) ? \"[\" + value.map(function (val) {\n    return \"\\\"\" + val + \"\\\"\";\n  }) + \"]\" : \"\\\"\" + value + \"\\\"\";\n};\n\nvar buildContactPoint = function buildContactPoint(contactPoint) {\n  return contactPoint.map(function (contact) {\n    return \"{\\n    \\\"@type\\\": \\\"ContactPoint\\\",\\n    \\\"telephone\\\": \\\"\" + contact.telephone + \"\\\",\\n    \\\"contactType\\\": \\\"\" + contact.contactType + \"\\\"\" + (contact.areaServed ? \",\\n    \\\"areaServed\\\": \" + formatIfArray$1(contact.areaServed) : '') + (contact.availableLanguage ? \",\\n    \\\"availableLanguage\\\": \" + formatIfArray$1(contact.availableLanguage) : '') + (contact.contactOption ? \",\\n    \\\"contactOption\\\": \\\"\" + contact.contactOption + \"\\\"\" : '') + \"\\n    }\";\n  }).join(',');\n};\n\nvar CorporateContactJsonLd = function CorporateContactJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      url = _ref.url,\n      logo = _ref.logo,\n      contactPoint = _ref.contactPoint;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Organization\\\",\\n    \\\"url\\\": \\\"\" + url + \"\\\",\\n    \" + (logo ? \"\\\"logo\\\": \\\"\" + logo + \"\\\",\" : '') + \"\\n    \\\"contactPoint\\\": [\" + buildContactPoint(contactPoint) + \"]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-corporate-contact\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar NewsArticleJsonLd = function NewsArticleJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      url = _ref.url,\n      title = _ref.title,\n      _ref$images = _ref.images,\n      images = _ref$images === void 0 ? [] : _ref$images,\n      section = _ref.section,\n      keywords = _ref.keywords,\n      datePublished = _ref.datePublished,\n      _ref$dateCreated = _ref.dateCreated,\n      dateCreated = _ref$dateCreated === void 0 ? null : _ref$dateCreated,\n      _ref$dateModified = _ref.dateModified,\n      dateModified = _ref$dateModified === void 0 ? null : _ref$dateModified,\n      authorName = _ref.authorName,\n      description = _ref.description,\n      body = _ref.body,\n      publisherName = _ref.publisherName,\n      publisherLogo = _ref.publisherLogo;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"NewsArticle\\\",\\n    \\\"mainEntityOfPage\\\": {\\n      \\\"@type\\\": \\\"WebPage\\\",\\n      \\\"@id\\\": \\\"\" + url + \"\\\"\\n    },\\n    \\\"headline\\\": \\\"\" + title + \"\\\",\\n    \\\"image\\\": [\\n      \" + images.map(function (image) {\n    return \"\\\"\" + image + \"\\\"\";\n  }) + \"\\n     ],\\n    \\\"articleSection\\\":\\\"\" + section + \"\\\",\\n    \\\"keywords\\\": \\\"\" + keywords + \"\\\",\\n    \\\"datePublished\\\": \\\"\" + datePublished + \"\\\",\\n    \\\"dateCreated\\\": \\\"\" + (dateCreated || datePublished) + \"\\\",\\n    \\\"dateModified\\\": \\\"\" + (dateModified || datePublished) + \"\\\",\\n    \\\"author\\\": \" + formatAuthorName(authorName) + \",\\n    \\\"publisher\\\": {\\n      \\\"@type\\\": \\\"Organization\\\",\\n      \\\"name\\\": \\\"\" + publisherName + \"\\\",\\n      \\\"logo\\\": {\\n        \\\"@type\\\": \\\"ImageObject\\\",\\n        \\\"url\\\": \\\"\" + publisherLogo + \"\\\"\\n      }\\n    },\\n    \\\"description\\\": \\\"\" + description + \"\\\",\\n    \\\"articleBody\\\": \\\"\" + body + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-newsarticle\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar buildLocation = function buildLocation(location) {\n  return \"\\n  \\\"location\\\": {\\n    \\\"@type\\\": \\\"Place\\\",\\n    \" + buildAddress(location.address) + \"\\n    \" + (location.sameAs ? \"\\\"sameAs\\\": \\\"\" + location.sameAs + \"\\\",\" : \"\") + \"\\n    \\\"name\\\": \\\"\" + location.name + \"\\\"\\n  },\\n\";\n};\n\nvar buildPerformer = function buildPerformer(performer) {\n  return \"\\n  {\\n    \\\"@type\\\": \\\"PerformingGroup\\\",\\n    \\\"name\\\": \\\"\" + performer.name + \"\\\"\\n  }\\n\";\n};\n\nvar EventJsonLd = function EventJsonLd(_ref) {\n  var name = _ref.name,\n      startDate = _ref.startDate,\n      endDate = _ref.endDate,\n      location = _ref.location,\n      url = _ref.url,\n      description = _ref.description,\n      images = _ref.images,\n      offers = _ref.offers,\n      aggregateOffer = _ref.aggregateOffer,\n      performers = _ref.performers;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Event\\\",\\n    \\\"startDate\\\": \\\"\" + startDate + \"\\\",\\n    \\\"endDate\\\": \\\"\" + endDate + \"\\\",\\n    \" + buildLocation(location) + \"\\n    \" + (images ? \"\\\"image\\\":\" + formatIfArray(images) + \",\" : \"\") + \"\\n    \" + (url ? \"\\\"url\\\": \\\"\" + url + \"\\\",\" : \"\") + \"\\n    \" + (description ? \"\\\"description\\\": \\\"\" + description + \"\\\",\" : \"\") + \"\\n    \" + (offers ? \"\\\"offers\\\": \" + (Array.isArray(offers) ? \"[\" + offers.map(function (offer) {\n    return \"\" + buildOffers(offer);\n  }) + \"]\" : buildOffers(offers)) + \",\" : '') + \"\\n    \" + (aggregateOffer && !offers ? \"\\\"offers\\\": \" + buildAggregateOffer(aggregateOffer) + \",\" : '') + \"\\n    \" + (performers ? \"\\\"performer\\\": \" + (Array.isArray(performers) ? \"[\" + performers.map(function (performer) {\n    return \"\" + buildPerformer(performer);\n  }) + \"]\" : buildPerformer(performers)) + \",\" : '') + \"\\n    \\\"name\\\": \\\"\" + name + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-event\"\n  }));\n};\n\nvar buildVideo = (function (video, context) {\n  if (context === void 0) {\n    context = false;\n  }\n\n  return \"{\\n      \" + (context ? \"\\\"@context\\\": \\\"https://schema.org\\\",\" : \"\") + \"\\n      \\\"@type\\\": \\\"VideoObject\\\",\\n      \\\"name\\\": \\\"\" + video.name + \"\\\",\\n      \\\"description\\\": \\\"\" + video.description + \"\\\",\\n      \\\"thumbnailUrl\\\": [\\n          \" + video.thumbnailUrls.map(function (thumbnailUrl) {\n    return \"\\\"\" + thumbnailUrl + \"\\\"\";\n  }).join(',') + \"\\n        ],\\n        \" + (video.contentUrl ? \"\\\"contentUrl\\\": \\\"\" + video.contentUrl + \"\\\",\" : \"\") + \"\\n        \" + (video.duration ? \"\\\"duration\\\": \\\"\" + video.duration + \"\\\",\" : \"\") + \"\\n        \" + (video.embedUrl ? \"\\\"embedUrl\\\": \\\"\" + video.embedUrl + \"\\\",\" : \"\") + \"\\n        \" + (video.expires ? \"\\\"expires\\\": \\\"\" + video.expires + \"\\\",\" : \"\") + \"        \\n        \" + (video.hasPart ? \"\\\"hasPart\\\": \" + (Array.isArray(video.hasPart) ? \"[\" + video.hasPart.map(function (clip) {\n    return \"\" + buildClip(clip);\n  }).join(',') + \"]\" : buildClip(video.hasPart)) + \",\" : '') + \"\\n        \" + (video.watchCount ? \"\" + buildInteractionStatistic(video.watchCount) : \"\") + \"        \\n        \" + (video.publication ? \"\\\"publication\\\": \" + (Array.isArray(video.publication) ? \"[\" + video.publication.map(function (broadcastEvent) {\n    return \"\" + buildBroadcastEvent(broadcastEvent);\n  }).join(',') + \"]\" : buildBroadcastEvent(video.publication)) + \",\" : '') + \"\\n        \" + (video.regionsAllowed ? \"\\\"regionsAllowed\\\": \" + formatIfArray(video.regionsAllowed) + \",\" : '') + \"\\n        \\\"uploadDate\\\": \\\"\" + video.uploadDate + \"\\\"\\n  }\";\n});\n\nvar buildClip = function buildClip(clip) {\n  return \"\\n  \\\"geo\\\": {\\n    \\\"@type\\\": \\\"Clip\\\",\\n    \\\"name\\\": \\\"\" + clip.name + \"\\\",\\n    \\\"startOffset\\\": \" + clip.startOffset + \",\\n    \\\"url\\\": \\\"\" + clip.url + \"\\\"\\n  }\\n\";\n};\n\nvar buildInteractionStatistic = function buildInteractionStatistic(watchCount) {\n  return \"\\n  \\\"interactionStatistic\\\": {\\n    \\\"@type\\\": \\\"InteractionCounter\\\",\\n    \\\"interactionType\\\": { \\\"@type\\\": \\\"https://schema.org/WatchAction\\\" },\\n    \\\"userInteractionCount\\\": \" + watchCount + \"\\n  },\\n\";\n};\n\nvar buildBroadcastEvent = function buildBroadcastEvent(publication) {\n  return \"\\n  \\\"publication\\\": {\\n    \\\"@type\\\": \\\"BroadcastEvent\\\",\\n    \\\"name\\\": \\\"\" + publication.name + \"\\\",\\n    \\\"isLiveBroadcast\\\": \" + publication.isLiveBroadcast + \",\\n    \\\"startDate\\\": \\\"\" + publication.startDate + \"\\\",\\n    \\\"endDate\\\": \\\"\" + publication.endDate + \"\\\"\\n  }\\n\";\n};\n\nvar VideoJsonLd = function VideoJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      name = _ref.name,\n      description = _ref.description,\n      thumbnailUrls = _ref.thumbnailUrls,\n      uploadDate = _ref.uploadDate,\n      contentUrl = _ref.contentUrl,\n      duration = _ref.duration,\n      embedUrl = _ref.embedUrl,\n      expires = _ref.expires,\n      hasPart = _ref.hasPart,\n      watchCount = _ref.watchCount,\n      publication = _ref.publication,\n      regionsAllowed = _ref.regionsAllowed;\n  var jslonld = buildVideo({\n    name: name,\n    description: description,\n    thumbnailUrls: thumbnailUrls,\n    uploadDate: uploadDate,\n    contentUrl: contentUrl,\n    duration: duration,\n    embedUrl: embedUrl,\n    expires: expires,\n    hasPart: hasPart,\n    watchCount: watchCount,\n    publication: publication,\n    regionsAllowed: regionsAllowed\n  }, true);\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-video\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar buildQuestions$1 = function buildQuestions(mainEntity) {\n  return \"{\\n        \\\"@type\\\": \\\"Question\\\",\\n        \\\"name\\\": \\\"\" + mainEntity.name + \"\\\",\\n        \" + (mainEntity.text ? \"\\\"text\\\": \\\"\" + mainEntity.text + \"\\\",\" : '') + \"\\n        \\\"answerCount\\\": \" + mainEntity.answerCount + \",\\n        \" + (mainEntity.upvotedCount ? \"\\\"upvoteCount\\\": \" + mainEntity.upvotedCount + \",\" : '') + \"\\n        \" + (mainEntity.dateCreated ? \"\\\"dateCreated\\\": \\\"\" + mainEntity.dateCreated + \"\\\",\" : '') + \"\\n        \" + (mainEntity.author ? \"\\\"author\\\": {\\n          \\\"@type\\\": \\\"Person\\\",\\n          \\\"name\\\": \\\"\" + mainEntity.author.name + \"\\\"\\n        },\" : '') + \"\\n        \" + (mainEntity.acceptedAnswer ? \"\\\"acceptedAnswer\\\": {\\n          \\\"@type\\\": \\\"Answer\\\",\\n          \\\"text\\\": \\\"\" + mainEntity.acceptedAnswer.text + \"\\\",\\n          \" + (mainEntity.acceptedAnswer.dateCreated ? \"\\\"dateCreated\\\": \\\"\" + mainEntity.acceptedAnswer.dateCreated + \"\\\",\" : '') + \"\\n          \" + (mainEntity.acceptedAnswer.upvotedCount ? \"\\\"upvoteCount\\\": \" + mainEntity.acceptedAnswer.upvotedCount + \",\" : '') + \"\\n          \" + (mainEntity.acceptedAnswer.url ? \"\\\"url\\\": \\\"\" + mainEntity.acceptedAnswer.url + \"\\\",\" : '') + \"\\n          \" + (mainEntity.acceptedAnswer.author ? \"\\\"author\\\": {\\n            \\\"@type\\\": \\\"Person\\\",\\n            \\\"name\\\": \\\"\" + mainEntity.acceptedAnswer.author.name + \"\\\"\\n          }\" : '') + \"\\n        },\" : '') + \"\\n        \" + (mainEntity.suggestedAnswer ? \"\\\"suggestedAnswer\\\": [\" + mainEntity.suggestedAnswer.map(function (suggested) {\n    return \"{\\n            \\\"@type\\\": \\\"Answer\\\",\\n            \\\"text\\\": \\\"\" + suggested.text + \"\\\",\\n            \" + (suggested.dateCreated ? \"\\\"dateCreated\\\": \\\"\" + suggested.dateCreated + \"\\\",\" : '') + \"\\n            \" + (suggested.upvotedCount ? \"\\\"upvoteCount\\\": \" + suggested.upvotedCount + \",\" : \"\\\"upvoteCount\\\": \" + 0 + \",\") + \"\\n            \" + (suggested.url ? \"\\\"url\\\": \\\"\" + suggested.url + \"\\\",\" : '') + \"\\n              \" + (suggested.author ? \"\\\"author\\\": {\\n                        \\\"@type\\\": \\\"Person\\\",\\n                        \\\"name\\\": \\\"\" + suggested.author.name + \"\\\"\\n                    }\" : '') + \"\\n        }\";\n  }) + \"\\n    ]\" : '') + \"\\n}\";\n};\n\nvar QAPageJsonLd = function QAPageJsonLd(_ref) {\n  var mainEntity = _ref.mainEntity,\n      keyOverride = _ref.keyOverride;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"QAPage\\\",\\n    \\\"mainEntity\\\": \" + buildQuestions$1(mainEntity) + \"\\n    }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-qa\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar buildInstruction = function buildInstruction(instruction) {\n  return \"{\\n  \\\"@type\\\": \\\"HowToStep\\\",\\n  \" + (instruction.name ? \"\\\"name\\\": \\\"\" + instruction.name + \"\\\",\" : '') + \"\\n  \" + (instruction.image ? \"\\\"image\\\": \\\"\" + instruction.image + \"\\\",\" : '') + \"\\n  \" + (instruction.url ? \"\\\"url\\\": \\\"\" + instruction.url + \"\\\",\" : '') + \"\\n  \\\"text\\\": \\\"\" + instruction.text + \"\\\"\\n}\";\n};\n\nvar RecipeJsonLd = function RecipeJsonLd(_ref) {\n  var name = _ref.name,\n      description = _ref.description,\n      authorName = _ref.authorName,\n      _ref$images = _ref.images,\n      images = _ref$images === void 0 ? [] : _ref$images,\n      datePublished = _ref.datePublished,\n      prepTime = _ref.prepTime,\n      cookTime = _ref.cookTime,\n      totalTime = _ref.totalTime,\n      keywords = _ref.keywords,\n      yields = _ref.yields,\n      category = _ref.category,\n      cuisine = _ref.cuisine,\n      calories = _ref.calories,\n      ingredients = _ref.ingredients,\n      instructions = _ref.instructions,\n      aggregateRating = _ref.aggregateRating,\n      video = _ref.video;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org/\\\",\\n    \\\"@type\\\": \\\"Recipe\\\",\\n    \\\"name\\\": \\\"\" + name + \"\\\",\\n    \\\"description\\\": \\\"\" + description + \"\\\",\\n    \\\"datePublished\\\": \\\"\" + datePublished + \"\\\",\\n    \\\"author\\\": \" + formatAuthorName(authorName) + \",\\n    \\\"image\\\": [\\n      \" + images.map(function (image) {\n    return \"\\\"\" + image + \"\\\"\";\n  }).join(',') + \"\\n    ],\\n    \" + (prepTime ? \"\\\"prepTime\\\": \\\"\" + prepTime + \"\\\",\" : \"\") + \"\\n    \" + (cookTime ? \"\\\"cookTime\\\": \\\"\" + cookTime + \"\\\",\" : \"\") + \"\\n    \" + (totalTime ? \"\\\"totalTime\\\": \\\"\" + totalTime + \"\\\",\" : \"\") + \"\\n    \" + (keywords ? \"\\\"keywords\\\": \\\"\" + keywords + \"\\\",\" : \"\") + \"\\n    \" + (yields ? \"\\\"recipeYield\\\": \\\"\" + yields + \"\\\",\" : \"\") + \"\\n    \" + (category ? \"\\\"recipeCategory\\\": \\\"\" + category + \"\\\",\" : \"\") + \"\\n    \" + (cuisine ? \"\\\"recipeCuisine\\\": \\\"\" + cuisine + \"\\\",\" : \"\") + \"\\n    \" + (calories ? \"\\\"nutrition\\\": { \\\"@type\\\": \\\"NutritionInformation\\\", \\\"calories\\\": \\\"\" + calories + \" calories\\\" },\" : \"\") + \"\\n    \" + (aggregateRating ? buildAggregateRating$1(aggregateRating) : '') + \"\\n    \" + (video ? \"\\\"video\\\": \" + buildVideo(video) + \",\" : '') + \"\\n    \\\"recipeIngredient\\\": [\\n      \" + ingredients.map(function (ingredient) {\n    return \"\\\"\" + ingredient + \"\\\"\";\n  }).join(',') + \"\\n    ],\\n    \\\"recipeInstructions\\\": [\\n      \" + instructions.map(buildInstruction).join(',') + \"\\n    ]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-recipe\"\n  }));\n};\n\nvar VideoGameJsonLd = function VideoGameJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      name = _ref.name,\n      url = _ref.url,\n      image = _ref.image,\n      description = _ref.description,\n      languageName = _ref.languageName,\n      translatorName = _ref.translatorName,\n      authorName = _ref.authorName,\n      aggregateRating = _ref.aggregateRating,\n      applicationCategory = _ref.applicationCategory,\n      platformName = _ref.platformName,\n      operatingSystemName = _ref.operatingSystemName,\n      datePublished = _ref.datePublished,\n      keywords = _ref.keywords,\n      producerName = _ref.producerName,\n      producerUrl = _ref.producerUrl,\n      providerName = _ref.providerName,\n      providerUrl = _ref.providerUrl,\n      publisherName = _ref.publisherName,\n      offers = _ref.offers,\n      genreName = _ref.genreName,\n      playMode = _ref.playMode,\n      processorRequirements = _ref.processorRequirements,\n      memoryRequirements = _ref.memoryRequirements,\n      storageRequirements = _ref.storageRequirements,\n      trailer = _ref.trailer,\n      _ref$reviews = _ref.reviews,\n      reviews = _ref$reviews === void 0 ? [] : _ref$reviews;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org/\\\",\\n    \\\"@type\\\": \\\"VideoGame\\\",\\n    \\\"name\\\": \\\"\" + name + \"\\\",\\n    \" + (description ? \"\\\"description\\\": \\\"\" + description + \"\\\",\" : '') + \"\\n    \" + (aggregateRating ? buildAggregateRating$1(aggregateRating) : '') + \"\\n    \" + (datePublished ? \"\\\"datePublished\\\": \\\"\" + datePublished + \"\\\",\" : '') + \"\\n    \" + (url ? \"\\\"url\\\": \\\"\" + url + \"\\\",\" : '') + \"\\n    \" + (trailer ? \"\\\"trailer\\\": \" + buildVideo(trailer) + \",\" : '') + \"\\n    \" + (reviews.length ? buildReviews(reviews) : '') + \"\\n    \" + (keywords ? \"\\\"keywords\\\": \\\"\" + keywords + \"\\\",\" : '') + \"\\n    \" + (processorRequirements ? \"\\\"processorRequirements\\\": \\\"\" + processorRequirements + \"\\\",\" : '') + \"\\n    \" + (memoryRequirements ? \"\\\"memoryRequirements\\\": \\\"\" + memoryRequirements + \"\\\",\" : '') + \"\\n    \" + (storageRequirements ? \"\\\"storageRequirements\\\": \\\"\" + storageRequirements + \"\\\",\" : '') + \"\\n    \" + (playMode ? \"\\\"playMode\\\": \\\"\" + playMode + \"\\\",\" : '') + \"\\n    \" + (applicationCategory ? \"\\\"applicationCategory\\\": \\\"\" + applicationCategory + \"\\\",\" : '') + \"\\n    \" + (operatingSystemName ? \"\\\"operatingSystem\\\": \" + (Array.isArray(operatingSystemName) ? formatIfArray(operatingSystemName) : \"\\\"\" + operatingSystemName + \"\\\"\") + \",\" : '') + \"\\n    \" + (platformName ? \"\\\"gamePlatform\\\": \" + (Array.isArray(platformName) ? formatIfArray(platformName) : \"\\\"\" + platformName + \"\\\"\") + \",\" : '') + \"\\n    \" + (translatorName ? \"\\\"translator\\\": \" + (Array.isArray(translatorName) ? formatIfArray(translatorName) : \"\\\"\" + translatorName + \"\\\"\") + \",\" : '') + \"\\n    \" + (languageName ? \"\\\"inLanguage\\\": \" + (Array.isArray(languageName) ? formatIfArray(languageName) : \"\\\"\" + languageName + \"\\\"\") + \",\" : '') + \"\\n    \" + (genreName ? \"\\\"genre\\\": \" + (Array.isArray(genreName) ? formatIfArray(genreName) : \"\\\"\" + genreName + \"\\\"\") + \",\" : '') + \"\\n    \" + (publisherName ? \"\\\"publisher\\\": \" + (Array.isArray(publisherName) ? formatIfArray(publisherName) : \"\\\"\" + publisherName + \"\\\"\") + \",\" : '') + \"\\n    \" + (image ? \"\\n        \\\"image\\\": {\\n          \\\"@type\\\": \\\"ImageObject\\\",\\n          \\\"url\\\": \\\"\" + image + \"\\\"\\n        },\\n        \" : '') + \"\\n    \" + (authorName ? \"\\n        \\\"author\\\": {\\n          \\\"@type\\\": \\\"Organization\\\",\\n          \\\"name\\\": \\\"\" + authorName + \"\\\"\\n        },\\n        \" : '') + \"\\n    \" + (providerName ? \"\\n        \\\"provider\\\": {\\n          \\\"@type\\\": \\\"Organization\\\",\\n          \" + (providerUrl ? \"\\\"sameAs\\\": \\\"\" + providerUrl + \"\\\",\" : '') + \"\\n          \\\"name\\\": \\\"\" + providerName + \"\\\"\\n        },\\n        \" : '') + \"\\n    \" + (producerName ? \"\\n        \\\"producer\\\": {\\n          \\\"@type\\\": \\\"Organization\\\",\\n          \" + (producerUrl ? \"\\\"sameAs\\\": \\\"\" + producerUrl + \"\\\",\" : '') + \"\\n          \\\"name\\\": \\\"\" + producerName + \"\\\"\\n        },\\n        \" : '') + \"\\n    \" + (offers ? \"\\\"offers\\\": \" + (Array.isArray(offers) ? \"[\" + offers.map(function (offer) {\n    return \"\" + buildOffers(offer);\n  }) + \"]\" : buildOffers(offers)) : '') + \"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-video-game\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar CarouselJsonLd = function CarouselJsonLd(_ref) {\n  var type = _ref.type,\n      data = _ref.data;\n  var itemListElement = [];\n\n  switch (type) {\n    case 'default':\n      itemListElement = data.map(function (item, index) {\n        return \"{\\n        \\\"@type\\\": \\\"ListItem\\\",\\n        \\\"position\\\": \\\"\" + (index + 1) + \"\\\",\\n        \\\"url\\\": \\\"\" + item.url + \"\\\"\\n      }\";\n      });\n      break;\n\n    case 'course':\n      itemListElement = data.map(function (item, index) {\n        return \"{\\n        \\\"@type\\\": \\\"ListItem\\\",\\n        \\\"position\\\": \\\"\" + (index + 1) + \"\\\",\\n        \\\"item\\\": {\\n          \\\"@context\\\": \\\"https://schema.org\\\",\\n          \\\"@type\\\": \\\"Course\\\",\\n          \\\"url\\\": \\\"\" + item.url + \"\\\",\\n          \\\"name\\\": \\\"\" + item.courseName + \"\\\",\\n          \\\"description\\\": \\\"\" + item.description + \"\\\",\\n          \\\"provider\\\": {\\n            \\\"@type\\\": \\\"Organization\\\",\\n            \\\"name\\\": \\\"\" + item.providerName + \"\\\"\" + (item.providerUrl ? \",\\n                \\\"sameAs\\\": \\\"\" + item.providerUrl + \"\\\"\" : '') + \"\\n          }\\n      }\\n    }\";\n      });\n      break;\n\n    case 'movie':\n      itemListElement = data.map(function (item, index) {\n        return \"{\\n        \\\"@type\\\": \\\"ListItem\\\",\\n        \\\"position\\\": \\\"\" + (index + 1) + \"\\\",\\n        \\\"item\\\": {\\n          \\\"@context\\\": \\\"https://schema.org\\\",\\n          \\\"@type\\\": \\\"Movie\\\",\\n          \\\"name\\\": \\\"\" + item.name + \"\\\",\\n          \\\"url\\\": \\\"\" + item.url + \"\\\",\\n          \\\"image\\\": \\\"\" + item.image + \"\\\",\\n          \" + (item.dateCreated ? \"\\\"dateCreated\\\": \\\"\" + item.dateCreated + \"\\\",\" : \"\") + \"\\n          \" + (item.director ? \"\\\"director\\\": \" + (Array.isArray(item.director) ? \"[\" + item.director.map(function (director) {\n          return \"{\\n                          \\\"@type\\\": \\\"Person\\\",\\n                          \\\"name\\\": \\\"\" + director.name + \"\\\"\\n                        }\";\n        }).join(',') + \"]\" : \"{\\n                      \\\"@type\\\": \\\"Person\\\",\\n                      \\\"name\\\": \\\"\" + item.director.name + \"\\\"\\n                    }\") : '') + \"\\n          \" + (item.review ? \",\\n              \\\"review\\\": {\\n                \\\"@type\\\": \\\"Review\\\",\\n                \" + (item.review.author ? buildAuthor(item.review.author) : '') + \"\\n                \" + (item.review.publisher ? buildPublisher(item.review.publisher) : '') + \"\\n                \" + (item.review.datePublished ? \"\\\"datePublished\\\": \\\"\" + item.review.datePublished + \"\\\",\" : '') + \"\\n                \" + (item.review.reviewBody ? \"\\\"reviewBody\\\": \\\"\" + item.review.reviewBody + \"\\\",\" : '') + \"\\n                \" + (item.review.name ? \"\\\"name\\\": \\\"\" + item.review.name + \"\\\",\" : '') + \"\\n                \" + buildReviewRating(item.review.reviewRating) + \"\\n            }\" : '') + \"\\n        }\\n      }\";\n      });\n      break;\n\n    case 'recipe':\n      itemListElement = data.map(function (item, index) {\n        var _item$images;\n\n        return \"{\\n        \\\"@type\\\": \\\"ListItem\\\",\\n        \\\"position\\\": \\\"\" + (index + 1) + \"\\\",\\n        \\\"item\\\": {\\n          \\\"@context\\\": \\\"https://schema.org/\\\",\\n          \\\"@type\\\": \\\"Recipe\\\",\\n          \\\"name\\\": \\\"\" + item.name + \"\\\",\\n          \\\"url\\\" : \\\"\" + item.url + \"\\\",\\n          \\\"description\\\": \\\"\" + item.description + \"\\\",\\n          \\\"datePublished\\\": \\\"\" + item.datePublished + \"\\\",\\n          \\\"author\\\": {\\n            \\\"@type\\\": \\\"Person\\\",\\n            \\\"name\\\": \\\"\" + item.authorName + \"\\\"\\n          },\\n          \\\"image\\\": [\\n            \" + ((_item$images = item.images) == null ? void 0 : _item$images.map(function (image) {\n          return \"\\\"\" + image + \"\\\"\";\n        }).join(',')) + \"\\n          ],\\n          \" + (item.prepTime ? \"\\\"prepTime\\\": \\\"\" + item.prepTime + \"\\\",\" : \"\") + \"\\n          \" + (item.cookTime ? \"\\\"cookTime\\\": \\\"\" + item.cookTime + \"\\\",\" : \"\") + \"\\n          \" + (item.totalTime ? \"\\\"totalTime\\\": \\\"\" + item.totalTime + \"\\\",\" : \"\") + \"\\n          \" + (item.keywords ? \"\\\"keywords\\\": \\\"\" + item.keywords + \"\\\",\" : \"\") + \"\\n          \" + (item.yields ? \"\\\"recipeYield\\\": \\\"\" + item.yields + \"\\\",\" : \"\") + \"\\n          \" + (item.category ? \"\\\"recipeCategory\\\": \\\"\" + item.category + \"\\\",\" : \"\") + \"\\n          \" + (item.cuisine ? \"\\\"recipeCuisine\\\": \\\"\" + item.cuisine + \"\\\",\" : \"\") + \"\\n          \" + (item.calories ? \"\\\"nutrition\\\": { \\\"@type\\\": \\\"NutritionInformation\\\", \\\"calories\\\": \\\"\" + item.calories + \" calories\\\" },\" : \"\") + \"\\n          \" + (item.aggregateRating ? buildAggregateRating$1(item.aggregateRating) : '') + \"\\n          \" + (item.video ? \"\\\"video\\\": \" + buildVideo(item.video) + \",\" : '') + \"\\n          \\\"recipeIngredient\\\": [\\n            \" + item.ingredients.map(function (ingredient) {\n          return \"\\\"\" + ingredient + \"\\\"\";\n        }).join(',') + \"\\n          ],\\n          \\\"recipeInstructions\\\": [\\n            \" + item.instructions.map(buildInstruction).join(',') + \"\\n          ]\\n      }\\n      }\";\n      });\n      break;\n  }\n\n  var jsonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"ItemList\\\",\\n    \\\"itemListElement\\\": [\" + itemListElement.join(',') + \"]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jsonld),\n    key: \"jsonld-course\"\n  }));\n};\n\nvar SiteLinksSearchBoxJsonLd = function SiteLinksSearchBoxJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      url = _ref.url,\n      _ref$potentialActions = _ref.potentialActions,\n      potentialActions = _ref$potentialActions === void 0 ? [] : _ref$potentialActions;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"WebSite\\\",\\n    \\\"url\\\": \\\"\" + url + \"\\\",\\n    \\\"potentialAction\\\": [\\n      \" + potentialActions.map(function (_ref2) {\n    var target = _ref2.target,\n        queryInput = _ref2.queryInput;\n    return \"{\\n        \\\"@type\\\": \\\"SearchAction\\\",\\n        \\\"target\\\": \\\"\" + target + \"={\" + queryInput + \"}\\\",\\n        \\\"query-input\\\": \\\"required name=\" + queryInput + \"\\\"\\n      }\";\n  }) + \"\\n     ]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-siteLinksSearchBox\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar buildReview$1 = function buildReview(review) {\n  return \"\\n    \\\"review\\\": {\\n        \\\"@type\\\": \\\"Review\\\",\\n        \" + (review.author ? buildAuthor(review.author) : '') + \"\\n        \" + (review.publisher ? buildPublisher(review.publisher) : '') + \"\\n        \" + (review.datePublished ? \"\\\"datePublished\\\": \\\"\" + review.datePublished + \"\\\",\" : '') + \"\\n        \" + (review.reviewBody ? \"\\\"reviewBody\\\": \\\"\" + review.reviewBody + \"\\\",\" : '') + \"\\n        \" + (review.name ? \"\\\"name\\\": \\\"\" + review.name + \"\\\",\" : '') + \"\\n        \" + buildReviewRating(review.reviewRating) + \"\\n      },\\n  \";\n};\n\nvar SoftwareAppJsonLd = function SoftwareAppJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      name = _ref.name,\n      applicationCategory = _ref.applicationCategory,\n      operatingSystem = _ref.operatingSystem,\n      priceCurrency = _ref.priceCurrency,\n      price = _ref.price,\n      aggregateRating = _ref.aggregateRating,\n      review = _ref.review;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"SoftwareApplication\\\",\\n    \\\"offers\\\": {\\n      \\\"@type\\\": \\\"Offer\\\",\\n      \\\"priceCurrency\\\": \\\"\" + priceCurrency + \"\\\",\\n      \\\"price\\\": \\\"\" + price + \"\\\"\\n    },\\n    \" + (applicationCategory ? \"\\\"applicationCategory\\\": \\\"\" + applicationCategory + \"\\\",\" : '') + \"\\n    \" + (operatingSystem ? \"\\\"operatingSystem\\\": \\\"\" + operatingSystem + \"\\\",\" : '') + \"\\n    \" + (aggregateRating ? buildAggregateRating$1(aggregateRating) : '') + \"\\n    \" + (review ? buildReview$1(review) : '') + \"\\n    \\\"name\\\": \\\"\" + name + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-softwareApp\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar CollectionPageJsonLd = function CollectionPageJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      name = _ref.name,\n      _ref$hasPart = _ref.hasPart,\n      hasPart = _ref$hasPart === void 0 ? [] : _ref$hasPart;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"CollectionPage\\\",\\n    \\\"name\\\": \\\"\" + name + \"\\\",\\n    \\\"hasPart\\\": [\\n      \" + hasPart.map(function (creativeWork) {\n    return \"{\\n        \\\"@type\\\": \\\"CreativeWork\\\",\\n        \\\"author\\\": \\\"\" + creativeWork.author + \"\\\",\\n        \\\"about\\\": \\\"\" + creativeWork.about + \"\\\",\\n        \\\"name\\\": \\\"\" + creativeWork.name + \"\\\",\\n        \" + (creativeWork.audience ? \"\\\"audience\\\": \\\"\" + creativeWork.audience + \"\\\",\" : '') + \"\\n        \" + (creativeWork.keywords ? \"\\\"keywords\\\": \\\"\" + creativeWork.keywords + \"\\\",\" : '') + \"\\n        \" + (creativeWork.thumbnailUrl ? \"\\\"thumbnailUrl\\\": \\\"\" + creativeWork.thumbnailUrl + \"\\\",\" : '') + \"\\n        \" + (creativeWork.image ? \"\\\"image\\\": \\\"\" + creativeWork.image + \"\\\",\" : '') + \"\\n        \\\"datePublished\\\": \\\"\" + creativeWork.datePublished + \"\\\"\\n      }\";\n  }) + \"\\n     ]\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-collection-page\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar buildBreadcrumb = function buildBreadcrumb(itemListElements) {\n  return \"{\\n  \\\"@type\\\": \\\"BreadcrumbList\\\",\\n  \\\"itemListElement\\\": \" + buildBreadcrumbList(itemListElements) + \"\\n}\";\n};\n\nvar buildBreadcrumbList = function buildBreadcrumbList(itemListElements) {\n  return \"[\\n  \" + itemListElements.map(function (itemListElement) {\n    return \"{\\n    \\\"@type\\\": \\\"ListItem\\\",\\n    \\\"position\\\": \" + itemListElement.position + \",\\n    \\\"item\\\": {\\n      \\\"@id\\\": \\\"\" + itemListElement.item + \"\\\",\\n      \\\"name\\\": \\\"\" + itemListElement.name + \"\\\"\\n    }\\n  }\";\n  }) + \"\\n]\";\n};\n\nvar ProfilePageJsonLd = function ProfilePageJsonLd(_ref) {\n  var keyOverride = _ref.keyOverride,\n      breadcrumb = _ref.breadcrumb,\n      lastReviewed = _ref.lastReviewed;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"ProfilePage\\\",\\n    \" + (lastReviewed ? \"\\\"lastReviewed\\\": \\\"\" + lastReviewed + \"\\\",\" : '') + \"\\n    \\\"breadcrumb\\\": \" + (typeof breadcrumb === 'string' ? \"\\\"\" + breadcrumb + \"\\\"\" : buildBreadcrumb(breadcrumb)) + \"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-profile-page\" + (keyOverride ? \"-\" + keyOverride : '')\n  }));\n};\n\nvar OrganizationJsonLd = function OrganizationJsonLd(_ref) {\n  var _ref$organizationType = _ref.organizationType,\n      organizationType = _ref$organizationType === void 0 ? 'Organization' : _ref$organizationType,\n      id = _ref.id,\n      name = _ref.name,\n      logo = _ref.logo,\n      url = _ref.url,\n      legalName = _ref.legalName,\n      _ref$sameAs = _ref.sameAs,\n      sameAs = _ref$sameAs === void 0 ? [] : _ref$sameAs,\n      address = _ref.address,\n      _ref$contactPoints = _ref.contactPoints,\n      contactPoints = _ref$contactPoints === void 0 ? [] : _ref$contactPoints;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"\" + organizationType + \"\\\",\\n    \" + (id ? \"\\\"@id\\\": \\\"\" + id + \"\\\",\" : '') + \"\\n    \" + (logo ? \"\\\"logo\\\": \\\"\" + logo + \"\\\",\" : '') + \"\\n    \" + (legalName ? \"\\\"legalName\\\": \\\"\" + legalName + \"\\\",\" : '') + \"\\n    \\\"name\\\": \\\"\" + name + \"\\\",\\n    \" + (address ? buildAddress(address) : '') + \"\\n    \" + (sameAs.length > 0 ? \"\\\"sameAs\\\": [\" + sameAs.map(function (alias) {\n    return \"\\\"\" + alias + \"\\\"\";\n  }).join(',') + \"],\" : '') + \"\\n    \" + (contactPoints.length > 0 ? \"\\\"contactPoints\\\": [\" + buildContactPoint(contactPoints) + \"],\" : '') + \"\\n    \\\"url\\\": \\\"\" + url + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-organization-page\"\n  }));\n};\n\nvar WebPageJsonLd = function WebPageJsonLd(_ref) {\n  var id = _ref.id,\n      description = _ref.description,\n      lastReviewed = _ref.lastReviewed,\n      reviewedBy = _ref.reviewedBy;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"WebPage\\\",\\n    \" + (description ? \"\\\"description\\\": \\\"\" + description + \"\\\",\" : '') + \"\\n    \" + (lastReviewed ? \"\\\"lastReviewed\\\": \\\"\" + lastReviewed + \"\\\",\" : '') + \"\\n    \" + (reviewedBy ? \"\\\"reviewedBy\\\": {\\n        \\\"@type\\\": \\\"\" + (reviewedBy.type || 'Organization') + \"\\\",\\n        \\\"name\\\": \\\"\" + reviewedBy.name + \"\\\"\\n    },\" : '') + \"\\n    \\\"@id\\\": \\\"\" + id + \"\\\"\\n    }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-webpage\"\n  }));\n};\n\nvar BrandJsonLd = function BrandJsonLd(_ref) {\n  var id = _ref.id,\n      slogan = _ref.slogan,\n      logo = _ref.logo,\n      aggregateRating = _ref.aggregateRating;\n  var jslonld = \"{\\n    \\\"@context\\\": \\\"https://schema.org\\\",\\n    \\\"@type\\\": \\\"Brand\\\",\\n    \" + (aggregateRating ? buildAggregateRating$1(aggregateRating) : '') + \"\\n    \" + (slogan ? \"\\\"slogan\\\": \\\"\" + slogan + \"\\\",\" : '') + \"\\n    \" + (logo ? \"\\\"logo\\\": \\\"\" + logo + \"\\\",\" : '') + \"\\n    \\\"@id\\\": \\\"\" + id + \"\\\"\\n  }\";\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((next_head__WEBPACK_IMPORTED_MODULE_0___default()), null, react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"script\", {\n    type: \"application/ld+json\",\n    dangerouslySetInnerHTML: markup(jslonld),\n    key: \"jsonld-brand\"\n  }));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next-seo/lib/next-seo.module.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Ccategories%5Cindex.js&page=%2Fcategories!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Ccategories%5Cindex.js&page=%2Fcategories! ***!
  \*************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/categories\",\n      function () {\n        return __webpack_require__(/*! ./pages/categories/index.js */ \"./pages/categories/index.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/categories\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDcmVwJTVDVFBTRyU1Q3Rwc2ctbmV4dCU1Q3BhZ2VzJTVDY2F0ZWdvcmllcyU1Q2luZGV4LmpzJnBhZ2U9JTJGY2F0ZWdvcmllcyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxnRUFBNkI7QUFDcEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzcyYzEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9jYXRlZ29yaWVzXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9jYXRlZ29yaWVzL2luZGV4LmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9jYXRlZ29yaWVzXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Ccategories%5Cindex.js&page=%2Fcategories!\n"));

/***/ }),

/***/ "./components/categories/MainList.js":
/*!*******************************************!*\
  !*** ./components/categories/MainList.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MainList; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-intersection-observer */ \"./node_modules/react-intersection-observer/react-intersection-observer.m.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: none;\\n\\n  @media \",\n        \" {\\n    display: initial;\\n    padding: 0;\\n    grid-column: 1 / span 4;\\n    top: 0;\\n    height: 80vh;\\n    position: -webkit-sticky;\\n    position: sticky;\\n  }\\n\\n  @media \",\n        \" {\\n    visibility: visible;\\n  }\\n  \\n  .anchor-list {\\n    position: relative;\\n    font-family: Stelvio, sans-serif;\\n    font-size: 20px;\\n    font-weight: 400;\\n\\n    .list-number {\\n      display: inline-block;\\n      color: #f1f1e8;\\n      width: 40px;\\n    }\\n\\n    ul {\\n      margin-top: 64px;\\n      padding: 0;\\n    }\\n\\n    li {\\n      font-family: Switzer, sans-serif;\\n      list-style: none;\\n      color: rgba(249, 246, 241, 0.6);\\n      margin: 16px 0 0 0;\\n      padding: 0;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  color: rgba(249, 246, 241, 0.85);\\n  mix-blend-mode: exclusion;\\n  grid-column: 1 / span 12;\\n\\n  @media \",\n        \" {\\n    margin-top: 64px;\\n    padding: 0;\\n    grid-column: 5 / span 8;\\n  }\\n\\n  h3 {\\n    font-size: 32px;\\n    margin-bottom: 16px;\\n    font-weight: 500;\\n\\n    @media \",\n        \" {\\n      font-size: clamp(24px, 3vw, 38px);\\n      max-width: 45%;\\n      margin-top: 32px;\\n    }\\n  }\\n\\n  .section-marker {\\n    position: absolute;\\n    margin-top: 360px;\\n    height: 100%;\\n  }\\n\\n  .main-topic-wrapper {\\n    scroll-margin-top: 64px;\\n    @media \",\n        \" {\\n      display: flex;\\n      flex-direction: row;\\n      justify-content: space-between;\\n      border-top: 1px solid rgba(248, 248, 243, 0.18);\\n    }\\n  }\\n\\n  .topic-list-wrapper {\\n    ul {\\n      margin: 0;\\n      padding: 0;\\n    }\\n\\n    li {\\n      font-family: Switzer, sans-serif;\\n      list-style: none;\\n\\n      span {\\n        float: right;\\n      }\\n\\n      //border-bottom: 1px solid rgba(248, 248, 243, 0.2);\\n      &:hover {\\n        color: var(--c-brand-lighter)\\n      }\\n    }\\n\\n    li:last-child {\\n      border: none;\\n      margin-bottom: 8px;\\n    }\\n\\n    .topic-lvl-2 {\\n      padding-top: 24px;\\n      padding-bottom: 16px;\\n      font-size: 20px;\\n      font-weight: 600;\\n    }\\n\\n    .topic-lvl-3 {\\n      padding: 12px 0;\\n      font-size: 18px;\\n      font-weight: 400;\\n      color: rgba(249, 246, 241, 0.9);\\n    }\\n\\n    @media \",\n        \" {\\n      width: calc(50% - 40px);\\n      margin: 16px 0;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: absolute;\\n  width: 80%;\\n  aspect-ratio: 1/1;\\n  left: 25vw;\\n  bottom: -10%;\\n  z-index: -1;\\n  transition: all 450ms ease-in-out;\\n  border-radius: 100%;\\n  overflow: hidden;\\n\\n  .number-container {\\n    position: relative;\\n    margin-top: 11%;\\n    margin-left: \",\n        \";\\n    height: 78%;\\n    width: 546%;\\n    left: \",\n        \"%;\\n    box-sizing: border-box;\\n    transform-origin: right;\\n    transition-duration: 650ms;\\n    transition-timing-function: cubic-bezier(0.79, 0.43, 0.38, 0.99);\\n  }\\n\\n  svg {\\n    position: relative;\\n    height: 100%;\\n    width: 100%;\\n\\n    path {\\n      stroke: none;\\n      fill: #1C373C;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  padding: 64px var(--border-space) 128px var(--border-space);\\n  display: grid;\\n  background-color: var(--blue-dark);\\n  grid-template-columns: repeat(\",\n        \", 1fr);\\n  column-gap: \",\n        \"px;\\n\\n  @media \",\n        \" {\\n    grid-template-columns: repeat(\",\n        \", 1fr);\\n    column-gap: \",\n        \"px;\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst NumbersSVG = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"svg\", {\n        width: \"1400\",\n        height: \"200\",\n        viewBox: \"0 0 1400 200\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                opacity: \"0.01\",\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M0 0H1400V200H0V0Z\",\n                fill: \"#D9D9D9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M104.18 14H119V186H94.9798V48.01L93.7504 47.7147C92.1206 50.9085 89.0265 53.3544 85.2076 55.2186C81.3947 57.0797 76.9229 58.3295 72.6351 59.1661C68.3516 60.002 64.2771 60.4206 61.2715 60.6299C59.7694 60.7346 58.5363 60.7868 57.6798 60.8129C57.4165 60.821 57.1889 60.8265 57 60.8303V39.134C58.3775 39.3073 59.5765 39.3072 60.7392 39.3072H60.7533C81.8748 39.3072 99.2009 31.1452 104.18 14Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M307.465 139.468L307.463 139.469L278.462 165.653L277.208 166.786H278.901H362V186H237V172.371L285.019 130.996C297.746 120.074 309.18 110.181 317.424 99.9347C325.68 89.674 330.78 79.0069 330.78 66.5286C330.78 55.7471 327.763 47.6555 322.003 42.2665C316.251 36.8842 307.865 34.2957 297.332 34.2957C284.372 34.2957 274.277 40.2097 268.059 49.4299C261.972 58.4548 259.623 70.6087 261.857 83.424H239.874C236.414 64.086 240.475 46.7423 250.577 34.2405C260.768 21.6292 277.168 13.8678 298.412 14.0017H298.416C316.323 14.0017 330.72 18.9621 340.633 27.8708C350.539 36.7725 356.037 49.6782 356.037 65.7188C356.037 80.6471 350.018 93.604 340.923 105.58C331.819 117.568 319.671 128.527 307.465 139.468Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M537.102 97.0419V97.042L535.845 97.7305L537.189 98.2275V98.2276L537.192 98.2286L537.205 98.2333L537.258 98.2539C537.306 98.2727 537.38 98.3014 537.477 98.3405C537.669 98.4186 537.955 98.5379 538.32 98.7003C539.051 99.0254 540.1 99.5225 541.359 100.209C543.88 101.582 547.24 103.708 550.599 106.714C557.311 112.722 564 122.229 564 136.304C564 155.287 555.789 167.667 543.516 175.334C531.199 183.027 514.745 186 498.278 186C478.288 186 462.801 180.794 452.282 170.906C441.89 161.137 436.255 146.711 436 127.948H459.82C460.039 140.679 462.84 150.207 469.02 156.568C475.311 163.041 484.973 166.106 498.542 166.106C509.253 166.106 519.455 165.252 526.986 161.09C530.77 158.998 533.883 156.07 536.043 152.01C538.2 147.955 539.388 142.809 539.388 136.304V136.293V136.282C539.186 130.293 537.774 125.571 535.424 121.856C533.073 118.14 529.809 115.474 525.971 113.559C518.325 109.746 508.342 108.887 498.56 108.622H498.551H498.542C493.519 108.622 489.209 109.096 485.187 109.594L485.435 87.4912C489.254 87.9632 493.536 88.1982 498.278 88.1982C507.556 88.1982 516.562 87.1411 523.264 83.2406C526.628 81.2824 529.413 78.6068 531.352 74.9985C533.29 71.3936 534.364 66.8934 534.364 61.3108V61.299V61.2872C533.96 50.117 529.846 43.2038 523.28 39.1257C516.773 35.0837 507.957 33.8935 498.278 33.8935C485.889 33.8935 477.269 36.8306 471.706 43.261C466.254 49.5621 463.857 59.0859 463.529 72.0526H439.706C440.074 52.7432 445.131 38.3144 454.775 28.6823C464.528 18.9415 479.078 14 498.542 14C516.598 14 531.577 17.2391 542.094 24.7437C552.572 32.2208 558.714 43.9999 558.976 61.3207V61.3253C559.235 73.0239 553.791 81.9435 548.24 87.9633C545.467 90.9719 542.675 93.246 540.577 94.7673C539.53 95.5275 538.656 96.0987 538.047 96.4788C537.742 96.6688 537.504 96.8108 537.342 96.9048C537.261 96.9518 537.2 96.9867 537.159 97.0096L537.115 97.0349L537.105 97.0407L537.102 97.0418V97.0419Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M721.05 146.213V145.563H720.4H627V119.491L714.655 14H745.325V125.308V125.958H745.975H772V145.563H745.975H745.325V146.213V186H721.05V146.213ZM721.05 40.8575V39.0562L719.9 40.4419L649.774 124.892L648.889 125.958H650.275H720.4H721.05V125.308V40.8575Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M862.967 82.4934L862.746 84.5929L864.106 82.9826V82.9824L864.107 82.9814L864.112 82.9747L864.139 82.9446C864.146 82.9364 864.155 82.9268 864.164 82.9161C864.186 82.8908 864.215 82.8584 864.251 82.8195C864.351 82.7082 864.504 82.5424 864.71 82.3301C865.122 81.9054 865.745 81.2942 866.582 80.5586C868.255 79.0873 870.781 77.1192 874.174 75.1492C880.96 71.2109 891.222 67.261 905.104 67.261C923.291 67.261 938 72.8944 948.161 82.9902C958.322 93.0854 964 107.708 964 125.819C964 144.186 957.792 159.214 946.791 169.652C935.788 180.093 919.93 185.998 900.53 186C873.755 185.461 857.857 176.001 848.533 163.814C839.393 151.87 836.501 137.215 836 125.514L860.03 129.618C861.376 151.351 878.851 165.672 900.793 165.672C912.25 165.672 921.986 161.975 928.861 155.283C935.74 148.587 939.701 138.948 939.701 127.171C939.701 115.126 935.673 105.216 928.662 98.3188C921.652 91.4227 911.713 87.5887 899.985 87.5887C884.341 87.5887 871.273 94.5556 864.865 106.987L837.647 102.566C837.661 102.438 837.676 102.292 837.694 102.128C837.768 101.457 837.874 100.483 838.011 99.2461C838.283 96.7735 838.673 93.2558 839.14 89.0421C840.075 80.6146 841.321 69.4031 842.567 58.2C843.813 46.997 845.059 35.8024 845.994 27.4086L847.123 17.2675L847.439 14.4268L847.487 14H953.493V33.2463H868.731H868.148L868.086 33.8285L862.967 82.4934Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M1237.5 34.7492V35.2492H1238H1334.27C1304.28 70.6968 1285.77 130.921 1284.97 185.993L1284.96 186.5H1285.47H1310.14H1310.64V186C1310.64 122.767 1329.65 72.393 1366.28 35.0996L1366.3 35.0799L1366.32 35.0582L1367.39 33.6931L1367.5 33.5571V33.3841V14V13.5H1367H1238H1237.5V14V34.7492Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"path\", {\n                d: \"M1063.85 88.4617H1064.15L1064.34 88.2332C1071.86 79.3816 1086.45 70.9725 1107.91 70.9725C1140.51 70.9725 1164 96.8831 1164 129.149C1164 163.443 1136.91 186 1103.7 186C1079.63 186 1062.87 175.836 1052.04 160.342C1041.19 144.817 1036.26 123.896 1036 102.381C1036 79.3952 1040.41 57.2838 1051.29 40.9384C1062.15 24.631 1079.49 14 1105.54 14C1129.69 14 1153.39 24.5765 1161.67 47.0575L1136.86 52.5961C1132.83 39.8889 1119.75 33.897 1106.86 33.897C1091.75 33.897 1080.61 40.0155 1073.26 49.6201C1065.93 59.2033 1062.41 72.2073 1062.41 85.9558V87.8107V88.4617H1063.05H1063.85ZM1064.78 129.149C1064.78 151.277 1081.74 166.103 1102.9 166.103C1113.6 166.103 1122.75 162.541 1129.22 156.11C1135.68 149.678 1139.44 140.427 1139.44 129.149C1139.44 117.607 1135.62 108.092 1129.02 101.461C1122.42 94.8306 1113.07 91.1345 1102.11 91.1345C1091.4 91.1345 1082.06 94.8326 1075.4 101.458C1068.73 108.087 1064.78 117.601 1064.78 129.149Z\",\n                stroke: \"#333333\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = NumbersSVG;\nconst N1Theme = (param)=>{\n    let { topic, id } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        id: \"theme-\".concat(id),\n        className: \"main-topic-wrapper\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                children: topic.name\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(N2List, {\n                topics: topic.children\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = N1Theme;\nconst N2List = (param)=>{\n    let { topics } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"topic-list-wrapper\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            children: topics.map((topic, index)=>topic.postCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(N2Theme, {\n                    topic: topic\n                }, index, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = N2List;\nconst N2Theme = (param)=>{\n    let { topic } = param;\n    var _topic_children;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"li\", {\n                className: \"topic-lvl-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/categories/\".concat(topic.slug),\n                    children: [\n                        topic.name,\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                            children: topic.postCount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                            lineNumber: 64,\n                            columnNumber: 24\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            ((_topic_children = topic.children) === null || _topic_children === void 0 ? void 0 : _topic_children.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"ul\", {\n                children: topic.children.map((topic, index)=>topic.postCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"li\", {\n                        className: \"topic-lvl-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/categories/\".concat(topic.slug),\n                            children: [\n                                topic.name,\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    children: topic.postCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                    lineNumber: 73,\n                                    columnNumber: 36\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                            lineNumber: 72,\n                            columnNumber: 21\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                        lineNumber: 71,\n                        columnNumber: 19\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c3 = N2Theme;\nfunction MainList(param) {\n    let { topics } = param;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [S1, S1inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const [S2, S2inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const [S3, S3inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const [S4, S4inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const [S5, S5inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const [S6, S6inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const [S7, S7inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView)();\n    const getRef = (id)=>{\n        switch(id){\n            case 0:\n                return S1;\n            case 1:\n                return S2;\n            case 2:\n                return S3;\n            case 3:\n                return S4;\n            case 4:\n                return S5;\n            case 5:\n                return S6;\n            case 6:\n                return S7;\n            default:\n                return null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        let next = activeSection;\n        if (S1inView) {\n            next = 0;\n        }\n        if (S2inView) {\n            next = 1;\n        }\n        if (S3inView) {\n            next = 2;\n        }\n        if (S4inView) {\n            next = 3;\n        }\n        if (S5inView) {\n            next = 4;\n        }\n        if (S6inView) {\n            next = 5;\n        }\n        if (S7inView) {\n            next = 6;\n        }\n        setActiveSection(next);\n    }, [\n        activeSection,\n        S1inView,\n        S2inView,\n        S3inView,\n        S4inView,\n        S5inView,\n        S6inView,\n        S7inView\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Grid, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(TopicsNavigation, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        className: \"anchor-list\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"ul\", {\n                            children: topics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"a\", {\n                                        href: \"#theme-\".concat(index),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                className: \"list-number\",\n                                                children: \"\".concat(index + 1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this),\n                                            topic.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(BigNumber, {\n                        position: activeSection,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"number-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(NumbersSVG, {}, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Topics, {\n                children: topics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"section-marker\",\n                                ref: getRef(index)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(N1Theme, {\n                                topic: topic,\n                                id: index\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\categories\\\\MainList.js\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(MainList, \"1BmB4+ySRS1brKJiPlp2Ss/yQO4=\", false, function() {\n    return [\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_5__.useInView\n    ];\n});\n_c4 = MainList;\nconst TopicsNavigation = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"MainList__TopicsNavigation\",\n    componentId: \"sc-ec7745a2-0\"\n})(_templateObject(), styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop, styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop);\n_c5 = TopicsNavigation;\nconst Topics = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"MainList__Topics\",\n    componentId: \"sc-ec7745a2-1\"\n})(_templateObject1(), styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop, styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop, styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop, styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop);\n_c6 = Topics;\nconst BigNumber = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"MainList__BigNumber\",\n    componentId: \"sc-ec7745a2-2\"\n})(_templateObject2(), (p)=>p.position > 0 ? \"11%\" : \"11%\", (p)=>-p.position * 78);\n_c7 = BigNumber;\nconst Grid = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div.withConfig({\n    displayName: \"MainList__Grid\",\n    componentId: \"sc-ec7745a2-3\"\n})(_templateObject3(), (p)=>p.col ? p.col : 2, (p)=>p.gutter ? p.gutter : 0, styles_device__WEBPACK_IMPORTED_MODULE_3__.device.desktop, (p)=>p.col ? p.col : 12, (p)=>p.gutter ? 64 : 0);\n_c8 = Grid;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"NumbersSVG\");\n$RefreshReg$(_c1, \"N1Theme\");\n$RefreshReg$(_c2, \"N2List\");\n$RefreshReg$(_c3, \"N2Theme\");\n$RefreshReg$(_c4, \"MainList\");\n$RefreshReg$(_c5, \"TopicsNavigation\");\n$RefreshReg$(_c6, \"Topics\");\n$RefreshReg$(_c7, \"BigNumber\");\n$RefreshReg$(_c8, \"Grid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/categories/MainList.js\n"));

/***/ }),

/***/ "./components/shared/ListLink.js":
/*!***************************************!*\
  !*** ./components/shared/ListLink.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ListLink; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _utils_image_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/image-utils */ \"./utils/image-utils.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  font-family: Switzer, sans-serif;\\n  font-weight: 500;\\n  position: relative;\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  margin-top: -1px;\\n  border-top: 1px solid #CCCAC7;\\n  border-bottom: 1px solid #CCCAC7;\\n  padding: 10px 0 10px 4px;\\n  color: #161616;\\n  font-size: 18px;\\n  \\n  .ll-text {\\n    margin: 0;\\n  }\\n\\n  &:after {\\n    content: \"→\";\\n    position: absolute;\\n    right: 0;\\n    line-height: 100%;\\n    padding-bottom: 4px;\\n  }\\n  \\n  &:hover {\\n    cursor: pointer;\\n    color: var(--brand-color);\\n  }\\n\\n  @media ',\n        \" {\\n    font-size: 22px;\\n    padding: 12px 0 12px 4px;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n\nfunction ListLink(param) {\n    let { route, image, text } = param;\n    // const imgSrc = withRealSrc(image);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: route,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(InnerLink, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                    className: \"ll-text\",\n                    children: text\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ListLink.js\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ListLink.js\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ListLink.js\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ListLink.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = ListLink;\nconst InnerLink = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].a.withConfig({\n    displayName: \"ListLink__InnerLink\",\n    componentId: \"sc-1bebecf-0\"\n})(_templateObject(), styles_device__WEBPACK_IMPORTED_MODULE_3__.device.tablet);\n_c1 = InnerLink;\nvar _c, _c1;\n$RefreshReg$(_c, \"ListLink\");\n$RefreshReg$(_c1, \"InnerLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/ListLink.js\n"));

/***/ }),

/***/ "./components/shared/categories/SectionVocations.js":
/*!**********************************************************!*\
  !*** ./components/shared/categories/SectionVocations.js ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SectionVocations; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var _ListLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ListLink */ \"./components/shared/ListLink.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  .ministries {\\n    margin-left: 24px;\\n  }\\n  @media \",\n        \" {\\n    .ministries {\\n      margin-left: 40px;\\n    }\\n  }\\n  @media \",\n        \" {\\n    flex-direction: row;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\nconst renderChildren = (children)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"ministries\",\n        children: children === null || children === void 0 ? void 0 : children.map((child, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                image: child.cover,\n                text: child.name,\n                route: \"/categories/\".concat(child.type, \"/\").concat(child.slug)\n            }, key, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\categories\\\\SectionVocations.js\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\categories\\\\SectionVocations.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\nfunction SectionVocations(param) {\n    let { groups } = param;\n    const parents = groups.filter((vocation)=>vocation.children.length > 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SectionVocationsWrapper, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            children: parents === null || parents === void 0 ? void 0 : parents.map((parent, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            image: parent.cover,\n                            text: parent.name,\n                            route: \"/categories/vocation/\".concat(parent.slug)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\categories\\\\SectionVocations.js\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this),\n                        parent.children && renderChildren(parent.children)\n                    ]\n                }, key, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\categories\\\\SectionVocations.js\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\categories\\\\SectionVocations.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\categories\\\\SectionVocations.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_c = SectionVocations;\nconst SectionVocationsWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].section.withConfig({\n    displayName: \"SectionVocations__SectionVocationsWrapper\",\n    componentId: \"sc-156c3f87-0\"\n})(_templateObject(), styles_device__WEBPACK_IMPORTED_MODULE_2__.device.tablet, styles_device__WEBPACK_IMPORTED_MODULE_2__.device.desktop);\n_c1 = SectionVocationsWrapper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SectionVocations\");\n$RefreshReg$(_c1, \"SectionVocationsWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/categories/SectionVocations.js\n"));

/***/ }),

/***/ "./pages/categories/index.js":
/*!***********************************!*\
  !*** ./pages/categories/index.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Categories; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var _utils_list_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../utils/list.utils */ \"./utils/list.utils.js\");\n/* harmony import */ var styles_styled_typography__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styles/styled-typography */ \"./styles/styled-typography.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n/* harmony import */ var components_shared_categories_SectionVocations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/shared/categories/SectionVocations */ \"./components/shared/categories/SectionVocations.js\");\n/* harmony import */ var components_categories_MainList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/categories/MainList */ \"./components/categories/MainList.js\");\n/* harmony import */ var _components_shared_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/shared/atoms/Buttons/BigCta */ \"./components/shared/atoms/Buttons/BigCta.js\");\n/* harmony import */ var next_seo__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-seo */ \"./node_modules/next-seo/lib/next-seo.module.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  height: auto;\\n  margin-bottom: 48px;\\n  \\n  display: flex;\\n  flex-direction: column;\\n  \\n  .description {\\n    font-family: Switzer, sans-serif;\\n    margin-top: 64px;\\n    font-size: 16px;\\n  }\\n  \\n  @media \",\n        \" {\\n    margin-bottom: 96px;\\n    flex-direction: row;\\n    .description {\\n      position: sticky;\\n      top: 80px;\\n      margin-top: 0;\\n      max-height: 200px;\\n      .title {\\n        margin-top: -8px;\\n      }\\n      margin-bottom: 14px;\\n      width: 33%;\\n      line-height: 23px;\\n      color: #161616;\\n    }\\n    .list {\\n      width: 66%;\\n      margin-right: 80px;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 600px;\\n  background-color: #F9F1E6;\\n  \\n  .search-section-text {\\n    text-align: center;\\n    font-family: Stelvio, sans-serif;\\n    font-size: 32px;\\n    font-weight: 500;\\n    margin-bottom: 32px;\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Categories(props) {\n    let { groups, topics } = props;\n    topics = (0,_utils_list_utils__WEBPACK_IMPORTED_MODULE_2__.topicSort)(topics);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_seo__WEBPACK_IMPORTED_MODULE_8__.NextSeo, {\n                title: \"TPSG - Th\\xe8mes\",\n                description: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"site-padding\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"header\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(styles_styled_typography__WEBPACK_IMPORTED_MODULE_3__.PageTitle, {\n                            children: \"Th\\xe8mes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(VocationsWrapper, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_shared_categories_SectionVocations__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    groups: groups\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                className: \"description\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h2\", {\n                                        className: \"title\",\n                                        children: \"Vocations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                        children: \"Glorifier Dieu dans tous les aspects de notre vie c’est le glorifier dans chacune de nos vocations. Cela veut dire assumer les responsabilit\\xe9s qu’il nous confie l\\xe0 o\\xf9 il nous place: dans notre famille, dans notre \\xe9glise, dans notre vie sociale. Concr\\xe8tement, glorifier Dieu c’est \\xeatre le p\\xe8re, le fr\\xe8re et le coll\\xe8gue que Dieu m’appelle \\xe0 \\xeatre.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"section\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(components_categories_MainList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    topics: topics\n                }, void 0, false, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SearchSection, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                        className: \"search-section-text\",\n                        children: [\n                            \"Vous n'avez pas trouv\\xe9 ce que vous cherchiez? \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                                lineNumber: 53,\n                                columnNumber: 92\n                            }, this),\n                            \"Essayez notre outil de recherche\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_shared_atoms_Buttons_BigCta__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        link: \"/recherche\",\n                        text: \"Rechercher\",\n                        theme: \"dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\categories\\\\index.js\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Categories;\nconst VocationsWrapper = styled_components__WEBPACK_IMPORTED_MODULE_9__[\"default\"].section.withConfig({\n    displayName: \"categories__VocationsWrapper\",\n    componentId: \"sc-b2cbc2b2-0\"\n})(_templateObject(), styles_device__WEBPACK_IMPORTED_MODULE_4__.device.desktop);\n_c1 = VocationsWrapper;\nconst SearchSection = styled_components__WEBPACK_IMPORTED_MODULE_9__[\"default\"].section.withConfig({\n    displayName: \"categories__SearchSection\",\n    componentId: \"sc-b2cbc2b2-1\"\n})(_templateObject1());\n_c2 = SearchSection;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Categories\");\n$RefreshReg$(_c1, \"VocationsWrapper\");\n$RefreshReg$(_c2, \"SearchSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/categories/index.js\n"));

/***/ }),

/***/ "./styles/styled-typography.js":
/*!*************************************!*\
  !*** ./styles/styled-typography.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DisplayCardTitle: function() { return /* binding */ DisplayCardTitle; },\n/* harmony export */   PageTitle: function() { return /* binding */ PageTitle; },\n/* harmony export */   PostCardDescription: function() { return /* binding */ PostCardDescription; },\n/* harmony export */   PostCardDetails: function() { return /* binding */ PostCardDetails; },\n/* harmony export */   PostCardTitle: function() { return /* binding */ PostCardTitle; },\n/* harmony export */   PostLead: function() { return /* binding */ PostLead; },\n/* harmony export */   PostLittleTitle: function() { return /* binding */ PostLittleTitle; },\n/* harmony export */   PostTitle: function() { return /* binding */ PostTitle; },\n/* harmony export */   PostType: function() { return /* binding */ PostType; },\n/* harmony export */   SectionTitle: function() { return /* binding */ SectionTitle; },\n/* harmony export */   fontRatios: function() { return /* binding */ fontRatios; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  font-weight: 500;\\n  color: #161616;\\n  font-size: 48px;\\n  margin-top:  calc(var(--spacing-l) - 48px * \",\n        \");\\n  margin-bottom: calc(var(--spacing-l) - 48px * \",\n        \");\\n  \\n  @media \",\n        \" {\\n    font-size: 72px;\\n    margin-top:  calc(var(--spacing-l) - 72px * \",\n        \");\\n    margin-bottom: calc(var(--spacing-l) - 72px * \",\n        \");\\n  }\\n  @media \",\n        \" {\\n    font-size: 104px;\\n    margin-top:  calc(var(--spacing-l) - 104px * \",\n        \");\\n    margin-bottom: calc(var(--spacing-l) - 104px * \",\n        \");\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  box-sizing: content-box;\\n  color: #ececec;\\n  margin-top: 0;\\n  margin-bottom: 0;\\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\\n  font-weight: 400;\\n  max-width: 600px;\\n  line-height: 105%;\\n  &:before {\\n    display: block;\\n    margin-bottom: 8px;\\n    content: '\",\n        \"';\\n    color: \",\n        \";\\n    font-size: 18px;\\n  }\\n  @media \",\n        \" {\\n    font-size: 32px;\\n    margin-right: 24px;\\n  }\\n  @media \",\n        \" {\\n    font-weight: 400;\\n    margin-right: 48px;\\n    font-size: clamp(32px, 3.5vw, 48px);\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  \\n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\\n  \\n  font-family: Stelvio, \"Helvetica Neue\", Helvetica, sans-serif;\\n  font-size: var(--title-size);\\n  color: ',\n        \";\\n  font-weight: 400;\\n  margin-top: 0;\\n  margin-bottom: calc( var(--title-size) * -\",\n        \");\\n  \\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  font-size: 24px;\\n  font-weight: 500;\\n  line-height: 110%;\\n  margin-top: 0;\\n  margin-bottom: 16px;\\n  @media \",\n        \" {\\n    font-size: 48px;\\n    margin-bottom: 16px;\\n    margin-top: 16px;\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  font-size: 20px;\\n  margin-top: 0;\\n  margin-bottom: 10px;\\n  @media \",\n        \" {\\n    font-size: 30px;\\n  }\\n  span {\\n    position: relative;\\n    margin-left: 8px;\\n    display: inline-block;\\n    margin-bottom: -2px;\\n    height: 22px;\\n    width: 22px;\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  font-size: 16px;\\n  color: #888888;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  padding-right: 30px;\\n  margin-top: 0;\\n  margin-bottom: 10px;\\n  @media screen and (max-width:320px){ // Little screen only\\n    padding: 0;\\n  }\\n  @media \",\n        \" {\\n    font-size:20px;\\n  }\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  font-size: 14px;\\n  font-style: italic;\\n  font-family: \"Lora\", Charter, Times, \"Times New Roman\", serif;\\n  color: #7a7a7a;\\n  margin-top: 0;\\n  margin-bottom: 10px;\\n  @media ',\n        \" {\\n    font-size: 17px;\\n  }\\n\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  font-size: 38px;\\n  font-weight: 500;\\n  line-height: 110%;\\n  margin-top: 0;\\n  margin-bottom: 16px;\\n  @media \",\n        \" {\\n    font-size: 64px;\\n    margin-bottom: 24px;\\n    margin-top: 24px;\\n  }\\n  @media \",\n        \" {\\n    margin-bottom: 24px;\\n    margin-top: 24px;\\n  }\\n\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  color: #161616;\\n  letter-spacing: 0.04em;\\n  margin-top: 48px;\\n  text-transform: uppercase;\\n  @media \",\n        \" {\\n    margin-top: 0;\\n    margin-bottom: 0;\\n    font-size: 22px;\\n    font-weight: 500;\\n    &:hover {\\n      color: var(--brand-color);\\n    }\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  font-weight: 400;\\n  font-size: 26px;\\n  margin: 0;\\n  color: #161616;\\n  @media \",\n        \" {\\n    font-size: 32px;\\n  }\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\n\n\nconst fontRatios = {\n    realSize: 0.6346,\n    containerSize: 1.202,\n    topSpace: 0.096,\n    minBottomSpace: 0.2788,\n    maxBottomSpace: 0.4711,\n    baseLine: 0.731\n};\n/**\r\n * Titre des principales pages du site\r\n */ const PageTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h1.withConfig({\n    displayName: \"styled-typography__PageTitle\",\n    componentId: \"sc-a3af5335-0\"\n})(_templateObject(), fontRatios.topSpace, fontRatios.minBottomSpace, styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet, fontRatios.topSpace, fontRatios.minBottomSpace, styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop, fontRatios.topSpace, fontRatios.minBottomSpace);\n/**\r\n * Titre pour le ticket, timbre et diplôme\r\n */ const DisplayCardTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h2.withConfig({\n    displayName: \"styled-typography__DisplayCardTitle\",\n    componentId: \"sc-a3af5335-1\"\n})(_templateObject1(), (props)=>props.label, (props)=>props.color, styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet, styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop);\n/**\r\n * Titre des sections (principalement utilisé sur la page d'accueil)\r\n */ const SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h2.withConfig({\n    displayName: \"styled-typography__SectionTitle\",\n    componentId: \"sc-a3af5335-2\"\n})(_templateObject2(), (props)=>props.light ? \"var(--c-soft-cream)\" : \"var(--soft-dark)\", fontRatios.maxBottomSpace);\n/**\r\n * À partir d'ici on ne sait plus vraiment\r\n *\r\n *\r\n *\r\n *\r\n *\r\n *\r\n *\r\n */ const PostLittleTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h2.withConfig({\n    displayName: \"styled-typography__PostLittleTitle\",\n    componentId: \"sc-a3af5335-3\"\n})(_templateObject3(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet);\n/**\r\n * PostCard\r\n */ const PostCardTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h4.withConfig({\n    displayName: \"styled-typography__PostCardTitle\",\n    componentId: \"sc-a3af5335-4\"\n})(_templateObject4(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet);\nconst PostCardDescription = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"styled-typography__PostCardDescription\",\n    componentId: \"sc-a3af5335-5\"\n})(_templateObject5(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet);\nconst PostCardDetails = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"styled-typography__PostCardDetails\",\n    componentId: \"sc-a3af5335-6\"\n})(_templateObject6(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet);\n/**\r\n * PostPage\r\n */ const PostTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h1.withConfig({\n    displayName: \"styled-typography__PostTitle\",\n    componentId: \"sc-a3af5335-7\"\n})(_templateObject7(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet, styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop);\nconst PostType = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"styled-typography__PostType\",\n    componentId: \"sc-a3af5335-8\"\n})(_templateObject8(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop);\nconst PostLead = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"styled-typography__PostLead\",\n    componentId: \"sc-a3af5335-9\"\n})(_templateObject9(), styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet);\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/styled-typography.js\n"));

/***/ }),

/***/ "./node_modules/react-intersection-observer/react-intersection-observer.m.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/react-intersection-observer/react-intersection-observer.m.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InView: function() { return /* binding */ InView; },\n/* harmony export */   \"default\": function() { return /* binding */ InView; },\n/* harmony export */   defaultFallbackInView: function() { return /* binding */ defaultFallbackInView; },\n/* harmony export */   observe: function() { return /* binding */ observe; },\n/* harmony export */   useInView: function() { return /* binding */ useInView; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n\n  _setPrototypeOf(subClass, superClass);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar observerMap = new Map();\nvar RootIds = new WeakMap();\nvar rootId = 0;\nvar unsupportedValue = undefined;\n/**\r\n * What should be the default behavior if the IntersectionObserver is unsupported?\r\n * Ideally the polyfill has been loaded, you can have the following happen:\r\n * - `undefined`: Throw an error\r\n * - `true` or `false`: Set the `inView` value to this regardless of intersection state\r\n * **/\n\nfunction defaultFallbackInView(inView) {\n  unsupportedValue = inView;\n}\n/**\r\n * Generate a unique ID for the root element\r\n * @param root\r\n */\n\nfunction getRootId(root) {\n  if (!root) return '0';\n  if (RootIds.has(root)) return RootIds.get(root);\n  rootId += 1;\n  RootIds.set(root, rootId.toString());\n  return RootIds.get(root);\n}\n/**\r\n * Convert the options to a string Id, based on the values.\r\n * Ensures we can reuse the same observer when observing elements with the same options.\r\n * @param options\r\n */\n\n\nfunction optionsToId(options) {\n  return Object.keys(options).sort().filter(function (key) {\n    return options[key] !== undefined;\n  }).map(function (key) {\n    return key + \"_\" + (key === 'root' ? getRootId(options.root) : options[key]);\n  }).toString();\n}\n\nfunction createObserver(options) {\n  // Create a unique ID for this observer instance, based on the root, root margin and threshold.\n  var id = optionsToId(options);\n  var instance = observerMap.get(id);\n\n  if (!instance) {\n    // Create a map of elements this observer is going to observe. Each element has a list of callbacks that should be triggered, once it comes into view.\n    var elements = new Map();\n    var thresholds;\n    var observer = new IntersectionObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _elements$get;\n\n        // While it would be nice if you could just look at isIntersecting to determine if the component is inside the viewport, browsers can't agree on how to use it.\n        // -Firefox ignores `threshold` when considering `isIntersecting`, so it will never be false again if `threshold` is > 0\n        var inView = entry.isIntersecting && thresholds.some(function (threshold) {\n          return entry.intersectionRatio >= threshold;\n        }); // @ts-ignore support IntersectionObserver v2\n\n        if (options.trackVisibility && typeof entry.isVisible === 'undefined') {\n          // The browser doesn't support Intersection Observer v2, falling back to v1 behavior.\n          // @ts-ignore\n          entry.isVisible = inView;\n        }\n\n        (_elements$get = elements.get(entry.target)) == null ? void 0 : _elements$get.forEach(function (callback) {\n          callback(inView, entry);\n        });\n      });\n    }, options); // Ensure we have a valid thresholds array. If not, use the threshold from the options\n\n    thresholds = observer.thresholds || (Array.isArray(options.threshold) ? options.threshold : [options.threshold || 0]);\n    instance = {\n      id: id,\n      observer: observer,\n      elements: elements\n    };\n    observerMap.set(id, instance);\n  }\n\n  return instance;\n}\n/**\r\n * @param element - DOM Element to observe\r\n * @param callback - Callback function to trigger when intersection status changes\r\n * @param options - Intersection Observer options\r\n * @param fallbackInView - Fallback inView value.\r\n * @return Function - Cleanup function that should be triggered to unregister the observer\r\n */\n\n\nfunction observe(element, callback, options, fallbackInView) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  if (fallbackInView === void 0) {\n    fallbackInView = unsupportedValue;\n  }\n\n  if (typeof window.IntersectionObserver === 'undefined' && fallbackInView !== undefined) {\n    var bounds = element.getBoundingClientRect();\n    callback(fallbackInView, {\n      isIntersecting: fallbackInView,\n      target: element,\n      intersectionRatio: typeof options.threshold === 'number' ? options.threshold : 0,\n      time: 0,\n      boundingClientRect: bounds,\n      intersectionRect: bounds,\n      rootBounds: bounds\n    });\n    return function () {// Nothing to cleanup\n    };\n  } // An observer with the same options can be reused, so lets use this fact\n\n\n  var _createObserver = createObserver(options),\n      id = _createObserver.id,\n      observer = _createObserver.observer,\n      elements = _createObserver.elements; // Register the callback listener for this element\n\n\n  var callbacks = elements.get(element) || [];\n\n  if (!elements.has(element)) {\n    elements.set(element, callbacks);\n  }\n\n  callbacks.push(callback);\n  observer.observe(element);\n  return function unobserve() {\n    // Remove the callback from the callback list\n    callbacks.splice(callbacks.indexOf(callback), 1);\n\n    if (callbacks.length === 0) {\n      // No more callback exists for element, so destroy it\n      elements[\"delete\"](element);\n      observer.unobserve(element);\n    }\n\n    if (elements.size === 0) {\n      // No more elements are being observer by this instance, so destroy it\n      observer.disconnect();\n      observerMap[\"delete\"](id);\n    }\n  };\n}\n\nvar _excluded = [\"children\", \"as\", \"triggerOnce\", \"threshold\", \"root\", \"rootMargin\", \"onChange\", \"skip\", \"trackVisibility\", \"delay\", \"initialInView\", \"fallbackInView\"];\n\nfunction isPlainChildren(props) {\n  return typeof props.children !== 'function';\n}\n/**\r\n ## Render props\r\n\n To use the `<InView>` component, you pass it a function. It will be called\r\n whenever the state changes, with the new value of `inView`. In addition to the\r\n `inView` prop, children also receive a `ref` that should be set on the\r\n containing DOM element. This is the element that the IntersectionObserver will\r\n monitor.\r\n\n If you need it, you can also access the\r\n [`IntersectionObserverEntry`](https://developer.mozilla.org/en-US/docs/Web/API/IntersectionObserverEntry)\r\n on `entry`, giving you access to all the details about the current intersection\r\n state.\r\n\n ```jsx\r\n import { InView } from 'react-intersection-observer';\r\n\n const Component = () => (\r\n <InView>\r\n {({ inView, ref, entry }) => (\r\n      <div ref={ref}>\r\n        <h2>{`Header inside viewport ${inView}.`}</h2>\r\n      </div>\r\n    )}\r\n </InView>\r\n );\r\n\n export default Component;\r\n ```\r\n\n ## Plain children\r\n\n You can pass any element to the `<InView />`, and it will handle creating the\r\n wrapping DOM element. Add a handler to the `onChange` method, and control the\r\n state in your own component. Any extra props you add to `<InView>` will be\r\n passed to the HTML element, allowing you set the `className`, `style`, etc.\r\n\n ```jsx\r\n import { InView } from 'react-intersection-observer';\r\n\n const Component = () => (\r\n <InView as=\"div\" onChange={(inView, entry) => console.log('Inview:', inView)}>\r\n <h2>Plain children are always rendered. Use onChange to monitor state.</h2>\r\n </InView>\r\n );\r\n\n export default Component;\r\n ```\r\n */\n\n\nvar InView = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(InView, _React$Component);\n\n  function InView(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this;\n    _this.node = null;\n    _this._unobserveCb = null;\n\n    _this.handleNode = function (node) {\n      if (_this.node) {\n        // Clear the old observer, before we start observing a new element\n        _this.unobserve();\n\n        if (!node && !_this.props.triggerOnce && !_this.props.skip) {\n          // Reset the state if we get a new node, and we aren't ignoring updates\n          _this.setState({\n            inView: !!_this.props.initialInView,\n            entry: undefined\n          });\n        }\n      }\n\n      _this.node = node ? node : null;\n\n      _this.observeNode();\n    };\n\n    _this.handleChange = function (inView, entry) {\n      if (inView && _this.props.triggerOnce) {\n        // If `triggerOnce` is true, we should stop observing the element.\n        _this.unobserve();\n      }\n\n      if (!isPlainChildren(_this.props)) {\n        // Store the current State, so we can pass it to the children in the next render update\n        // There's no reason to update the state for plain children, since it's not used in the rendering.\n        _this.setState({\n          inView: inView,\n          entry: entry\n        });\n      }\n\n      if (_this.props.onChange) {\n        // If the user is actively listening for onChange, always trigger it\n        _this.props.onChange(inView, entry);\n      }\n    };\n\n    _this.state = {\n      inView: !!props.initialInView,\n      entry: undefined\n    };\n    return _this;\n  }\n\n  var _proto = InView.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    // If a IntersectionObserver option changed, reinit the observer\n    if (prevProps.rootMargin !== this.props.rootMargin || prevProps.root !== this.props.root || prevProps.threshold !== this.props.threshold || prevProps.skip !== this.props.skip || prevProps.trackVisibility !== this.props.trackVisibility || prevProps.delay !== this.props.delay) {\n      this.unobserve();\n      this.observeNode();\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.unobserve();\n    this.node = null;\n  };\n\n  _proto.observeNode = function observeNode() {\n    if (!this.node || this.props.skip) return;\n    var _this$props = this.props,\n        threshold = _this$props.threshold,\n        root = _this$props.root,\n        rootMargin = _this$props.rootMargin,\n        trackVisibility = _this$props.trackVisibility,\n        delay = _this$props.delay,\n        fallbackInView = _this$props.fallbackInView;\n    this._unobserveCb = observe(this.node, this.handleChange, {\n      threshold: threshold,\n      root: root,\n      rootMargin: rootMargin,\n      // @ts-ignore\n      trackVisibility: trackVisibility,\n      // @ts-ignore\n      delay: delay\n    }, fallbackInView);\n  };\n\n  _proto.unobserve = function unobserve() {\n    if (this._unobserveCb) {\n      this._unobserveCb();\n\n      this._unobserveCb = null;\n    }\n  };\n\n  _proto.render = function render() {\n    if (!isPlainChildren(this.props)) {\n      var _this$state = this.state,\n          inView = _this$state.inView,\n          entry = _this$state.entry;\n      return this.props.children({\n        inView: inView,\n        entry: entry,\n        ref: this.handleNode\n      });\n    }\n\n    var _this$props2 = this.props,\n        children = _this$props2.children,\n        as = _this$props2.as,\n        props = _objectWithoutPropertiesLoose(_this$props2, _excluded);\n\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(as || 'div', _extends({\n      ref: this.handleNode\n    }, props), children);\n  };\n\n  return InView;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\nInView.displayName = 'InView';\nInView.defaultProps = {\n  threshold: 0,\n  triggerOnce: false,\n  initialInView: false\n};\n\n/**\r\n * React Hooks make it easy to monitor the `inView` state of your components. Call\r\n * the `useInView` hook with the (optional) [options](#options) you need. It will\r\n * return an array containing a `ref`, the `inView` status and the current\r\n * [`entry`](https://developer.mozilla.org/en-US/docs/Web/API/IntersectionObserverEntry).\r\n * Assign the `ref` to the DOM element you want to monitor, and the hook will\r\n * report the status.\r\n *\r\n * @example\r\n * ```jsx\r\n * import React from 'react';\r\n * import { useInView } from 'react-intersection-observer';\r\n *\r\n * const Component = () => {\r\n *   const { ref, inView, entry } = useInView({\r\n *       threshold: 0,\r\n *   });\r\n *\r\n *   return (\r\n *     <div ref={ref}>\r\n *       <h2>{`Header inside viewport ${inView}.`}</h2>\r\n *     </div>\r\n *   );\r\n * };\r\n * ```\r\n */\n\nfunction useInView(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      threshold = _ref.threshold,\n      delay = _ref.delay,\n      trackVisibility = _ref.trackVisibility,\n      rootMargin = _ref.rootMargin,\n      root = _ref.root,\n      triggerOnce = _ref.triggerOnce,\n      skip = _ref.skip,\n      initialInView = _ref.initialInView,\n      fallbackInView = _ref.fallbackInView;\n\n  var unobserve = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    inView: !!initialInView\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var setRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (node) {\n    if (unobserve.current !== undefined) {\n      unobserve.current();\n      unobserve.current = undefined;\n    } // Skip creating the observer\n\n\n    if (skip) return;\n\n    if (node) {\n      unobserve.current = observe(node, function (inView, entry) {\n        setState({\n          inView: inView,\n          entry: entry\n        });\n\n        if (entry.isIntersecting && triggerOnce && unobserve.current) {\n          // If it should only trigger once, unobserve the element after it's inView\n          unobserve.current();\n          unobserve.current = undefined;\n        }\n      }, {\n        root: root,\n        rootMargin: rootMargin,\n        threshold: threshold,\n        // @ts-ignore\n        trackVisibility: trackVisibility,\n        // @ts-ignore\n        delay: delay\n      }, fallbackInView);\n    }\n  }, // We break the rule here, because we aren't including the actual `threshold` variable\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [// If the threshold is an array, convert it to a string so it won't change between renders.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  Array.isArray(threshold) ? threshold.toString() : threshold, root, rootMargin, triggerOnce, skip, trackVisibility, fallbackInView, delay]);\n  /* eslint-disable-next-line */\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!unobserve.current && state.entry && !triggerOnce && !skip) {\n      // If we don't have a ref, then reset the state (unless the hook is set to only `triggerOnce` or `skip`)\n      // This ensures we correctly reflect the current state - If you aren't observing anything, then nothing is inView\n      setState({\n        inView: !!initialInView\n      });\n    }\n  });\n  var result = [setRef, state.inView, state.entry]; // Support object destructuring, by adding the specific values.\n\n  result.ref = result[0];\n  result.inView = result[1];\n  result.entry = result[2];\n  return result;\n}\n\n\n//# sourceMappingURL=react-intersection-observer.m.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtaW50ZXJzZWN0aW9uLW9ic2VydmVyL3JlYWN0LWludGVyc2VjdGlvbi1vYnNlcnZlci5tLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBK0I7QUFDRzs7QUFFbEM7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxjQUFjLHVCQUF1QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxHQUFHOztBQUVaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1AsS0FBSyxZQUFZOztBQUVqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLHdCQUF3QjtBQUN4QjtBQUNBLElBQUk7OztBQUdKO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQzs7O0FBRzNDOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVSxTQUFTOztBQUVuQjtBQUNBO0FBQ0EsRUFBRSxHQUFHLG9CQUFvQjtBQUN6QixnQkFBZ0IsSUFBSTtBQUNwQixhQUFhLDBCQUEwQixPQUFPLEdBQUc7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFVBQVUsU0FBUzs7QUFFbkI7QUFDQSw0QkFBNEIsa0RBQWtEO0FBQzlFO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0IsZ0RBQW1CO0FBQzNDO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0EsQ0FBQyxDQUFDLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxZQUFZO0FBQ3hCO0FBQ0E7QUFDQSxhQUFhLHFCQUFxQjtBQUNsQztBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsaUJBQWlCLElBQUk7QUFDckIsY0FBYywwQkFBMEIsT0FBTyxHQUFHO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQix5Q0FBWTs7QUFFOUIsd0JBQXdCLDJDQUFjO0FBQ3RDO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUEsZUFBZSw4Q0FBaUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0EsTUFBTTs7O0FBR047O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNILG9EQUFvRDs7QUFFcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0Y7QUFDaEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWludGVyc2VjdGlvbi1vYnNlcnZlci9yZWFjdC1pbnRlcnNlY3Rpb24tb2JzZXJ2ZXIubS5qcz9hYTQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbiAodGFyZ2V0KSB7XG4gICAgZm9yICh2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspIHtcbiAgICAgIHZhciBzb3VyY2UgPSBhcmd1bWVudHNbaV07XG5cbiAgICAgIGZvciAodmFyIGtleSBpbiBzb3VyY2UpIHtcbiAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzb3VyY2UsIGtleSkpIHtcbiAgICAgICAgICB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHRhcmdldDtcbiAgfTtcblxuICByZXR1cm4gX2V4dGVuZHMuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cblxuZnVuY3Rpb24gX2luaGVyaXRzTG9vc2Uoc3ViQ2xhc3MsIHN1cGVyQ2xhc3MpIHtcbiAgc3ViQ2xhc3MucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShzdXBlckNsYXNzLnByb3RvdHlwZSk7XG4gIHN1YkNsYXNzLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IHN1YkNsYXNzO1xuXG4gIF9zZXRQcm90b3R5cGVPZihzdWJDbGFzcywgc3VwZXJDbGFzcyk7XG59XG5cbmZ1bmN0aW9uIF9zZXRQcm90b3R5cGVPZihvLCBwKSB7XG4gIF9zZXRQcm90b3R5cGVPZiA9IE9iamVjdC5zZXRQcm90b3R5cGVPZiB8fCBmdW5jdGlvbiBfc2V0UHJvdG90eXBlT2YobywgcCkge1xuICAgIG8uX19wcm90b19fID0gcDtcbiAgICByZXR1cm4gbztcbiAgfTtcblxuICByZXR1cm4gX3NldFByb3RvdHlwZU9mKG8sIHApO1xufVxuXG5mdW5jdGlvbiBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShzb3VyY2UsIGV4Y2x1ZGVkKSB7XG4gIGlmIChzb3VyY2UgPT0gbnVsbCkgcmV0dXJuIHt9O1xuICB2YXIgdGFyZ2V0ID0ge307XG4gIHZhciBzb3VyY2VLZXlzID0gT2JqZWN0LmtleXMoc291cmNlKTtcbiAgdmFyIGtleSwgaTtcblxuICBmb3IgKGkgPSAwOyBpIDwgc291cmNlS2V5cy5sZW5ndGg7IGkrKykge1xuICAgIGtleSA9IHNvdXJjZUtleXNbaV07XG4gICAgaWYgKGV4Y2x1ZGVkLmluZGV4T2Yoa2V5KSA+PSAwKSBjb250aW51ZTtcbiAgICB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxudmFyIG9ic2VydmVyTWFwID0gbmV3IE1hcCgpO1xudmFyIFJvb3RJZHMgPSBuZXcgV2Vha01hcCgpO1xudmFyIHJvb3RJZCA9IDA7XG52YXIgdW5zdXBwb3J0ZWRWYWx1ZSA9IHVuZGVmaW5lZDtcbi8qKlxyXG4gKiBXaGF0IHNob3VsZCBiZSB0aGUgZGVmYXVsdCBiZWhhdmlvciBpZiB0aGUgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgaXMgdW5zdXBwb3J0ZWQ/XHJcbiAqIElkZWFsbHkgdGhlIHBvbHlmaWxsIGhhcyBiZWVuIGxvYWRlZCwgeW91IGNhbiBoYXZlIHRoZSBmb2xsb3dpbmcgaGFwcGVuOlxyXG4gKiAtIGB1bmRlZmluZWRgOiBUaHJvdyBhbiBlcnJvclxyXG4gKiAtIGB0cnVlYCBvciBgZmFsc2VgOiBTZXQgdGhlIGBpblZpZXdgIHZhbHVlIHRvIHRoaXMgcmVnYXJkbGVzcyBvZiBpbnRlcnNlY3Rpb24gc3RhdGVcclxuICogKiovXG5cbmZ1bmN0aW9uIGRlZmF1bHRGYWxsYmFja0luVmlldyhpblZpZXcpIHtcbiAgdW5zdXBwb3J0ZWRWYWx1ZSA9IGluVmlldztcbn1cbi8qKlxyXG4gKiBHZW5lcmF0ZSBhIHVuaXF1ZSBJRCBmb3IgdGhlIHJvb3QgZWxlbWVudFxyXG4gKiBAcGFyYW0gcm9vdFxyXG4gKi9cblxuZnVuY3Rpb24gZ2V0Um9vdElkKHJvb3QpIHtcbiAgaWYgKCFyb290KSByZXR1cm4gJzAnO1xuICBpZiAoUm9vdElkcy5oYXMocm9vdCkpIHJldHVybiBSb290SWRzLmdldChyb290KTtcbiAgcm9vdElkICs9IDE7XG4gIFJvb3RJZHMuc2V0KHJvb3QsIHJvb3RJZC50b1N0cmluZygpKTtcbiAgcmV0dXJuIFJvb3RJZHMuZ2V0KHJvb3QpO1xufVxuLyoqXHJcbiAqIENvbnZlcnQgdGhlIG9wdGlvbnMgdG8gYSBzdHJpbmcgSWQsIGJhc2VkIG9uIHRoZSB2YWx1ZXMuXHJcbiAqIEVuc3VyZXMgd2UgY2FuIHJldXNlIHRoZSBzYW1lIG9ic2VydmVyIHdoZW4gb2JzZXJ2aW5nIGVsZW1lbnRzIHdpdGggdGhlIHNhbWUgb3B0aW9ucy5cclxuICogQHBhcmFtIG9wdGlvbnNcclxuICovXG5cblxuZnVuY3Rpb24gb3B0aW9uc1RvSWQob3B0aW9ucykge1xuICByZXR1cm4gT2JqZWN0LmtleXMob3B0aW9ucykuc29ydCgpLmZpbHRlcihmdW5jdGlvbiAoa2V5KSB7XG4gICAgcmV0dXJuIG9wdGlvbnNba2V5XSAhPT0gdW5kZWZpbmVkO1xuICB9KS5tYXAoZnVuY3Rpb24gKGtleSkge1xuICAgIHJldHVybiBrZXkgKyBcIl9cIiArIChrZXkgPT09ICdyb290JyA/IGdldFJvb3RJZChvcHRpb25zLnJvb3QpIDogb3B0aW9uc1trZXldKTtcbiAgfSkudG9TdHJpbmcoKTtcbn1cblxuZnVuY3Rpb24gY3JlYXRlT2JzZXJ2ZXIob3B0aW9ucykge1xuICAvLyBDcmVhdGUgYSB1bmlxdWUgSUQgZm9yIHRoaXMgb2JzZXJ2ZXIgaW5zdGFuY2UsIGJhc2VkIG9uIHRoZSByb290LCByb290IG1hcmdpbiBhbmQgdGhyZXNob2xkLlxuICB2YXIgaWQgPSBvcHRpb25zVG9JZChvcHRpb25zKTtcbiAgdmFyIGluc3RhbmNlID0gb2JzZXJ2ZXJNYXAuZ2V0KGlkKTtcblxuICBpZiAoIWluc3RhbmNlKSB7XG4gICAgLy8gQ3JlYXRlIGEgbWFwIG9mIGVsZW1lbnRzIHRoaXMgb2JzZXJ2ZXIgaXMgZ29pbmcgdG8gb2JzZXJ2ZS4gRWFjaCBlbGVtZW50IGhhcyBhIGxpc3Qgb2YgY2FsbGJhY2tzIHRoYXQgc2hvdWxkIGJlIHRyaWdnZXJlZCwgb25jZSBpdCBjb21lcyBpbnRvIHZpZXcuXG4gICAgdmFyIGVsZW1lbnRzID0gbmV3IE1hcCgpO1xuICAgIHZhciB0aHJlc2hvbGRzO1xuICAgIHZhciBvYnNlcnZlciA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlcihmdW5jdGlvbiAoZW50cmllcykge1xuICAgICAgZW50cmllcy5mb3JFYWNoKGZ1bmN0aW9uIChlbnRyeSkge1xuICAgICAgICB2YXIgX2VsZW1lbnRzJGdldDtcblxuICAgICAgICAvLyBXaGlsZSBpdCB3b3VsZCBiZSBuaWNlIGlmIHlvdSBjb3VsZCBqdXN0IGxvb2sgYXQgaXNJbnRlcnNlY3RpbmcgdG8gZGV0ZXJtaW5lIGlmIHRoZSBjb21wb25lbnQgaXMgaW5zaWRlIHRoZSB2aWV3cG9ydCwgYnJvd3NlcnMgY2FuJ3QgYWdyZWUgb24gaG93IHRvIHVzZSBpdC5cbiAgICAgICAgLy8gLUZpcmVmb3ggaWdub3JlcyBgdGhyZXNob2xkYCB3aGVuIGNvbnNpZGVyaW5nIGBpc0ludGVyc2VjdGluZ2AsIHNvIGl0IHdpbGwgbmV2ZXIgYmUgZmFsc2UgYWdhaW4gaWYgYHRocmVzaG9sZGAgaXMgPiAwXG4gICAgICAgIHZhciBpblZpZXcgPSBlbnRyeS5pc0ludGVyc2VjdGluZyAmJiB0aHJlc2hvbGRzLnNvbWUoZnVuY3Rpb24gKHRocmVzaG9sZCkge1xuICAgICAgICAgIHJldHVybiBlbnRyeS5pbnRlcnNlY3Rpb25SYXRpbyA+PSB0aHJlc2hvbGQ7XG4gICAgICAgIH0pOyAvLyBAdHMtaWdub3JlIHN1cHBvcnQgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgdjJcblxuICAgICAgICBpZiAob3B0aW9ucy50cmFja1Zpc2liaWxpdHkgJiYgdHlwZW9mIGVudHJ5LmlzVmlzaWJsZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAvLyBUaGUgYnJvd3NlciBkb2Vzbid0IHN1cHBvcnQgSW50ZXJzZWN0aW9uIE9ic2VydmVyIHYyLCBmYWxsaW5nIGJhY2sgdG8gdjEgYmVoYXZpb3IuXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICAgIGVudHJ5LmlzVmlzaWJsZSA9IGluVmlldztcbiAgICAgICAgfVxuXG4gICAgICAgIChfZWxlbWVudHMkZ2V0ID0gZWxlbWVudHMuZ2V0KGVudHJ5LnRhcmdldCkpID09IG51bGwgPyB2b2lkIDAgOiBfZWxlbWVudHMkZ2V0LmZvckVhY2goZnVuY3Rpb24gKGNhbGxiYWNrKSB7XG4gICAgICAgICAgY2FsbGJhY2soaW5WaWV3LCBlbnRyeSk7XG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgfSwgb3B0aW9ucyk7IC8vIEVuc3VyZSB3ZSBoYXZlIGEgdmFsaWQgdGhyZXNob2xkcyBhcnJheS4gSWYgbm90LCB1c2UgdGhlIHRocmVzaG9sZCBmcm9tIHRoZSBvcHRpb25zXG5cbiAgICB0aHJlc2hvbGRzID0gb2JzZXJ2ZXIudGhyZXNob2xkcyB8fCAoQXJyYXkuaXNBcnJheShvcHRpb25zLnRocmVzaG9sZCkgPyBvcHRpb25zLnRocmVzaG9sZCA6IFtvcHRpb25zLnRocmVzaG9sZCB8fCAwXSk7XG4gICAgaW5zdGFuY2UgPSB7XG4gICAgICBpZDogaWQsXG4gICAgICBvYnNlcnZlcjogb2JzZXJ2ZXIsXG4gICAgICBlbGVtZW50czogZWxlbWVudHNcbiAgICB9O1xuICAgIG9ic2VydmVyTWFwLnNldChpZCwgaW5zdGFuY2UpO1xuICB9XG5cbiAgcmV0dXJuIGluc3RhbmNlO1xufVxuLyoqXHJcbiAqIEBwYXJhbSBlbGVtZW50IC0gRE9NIEVsZW1lbnQgdG8gb2JzZXJ2ZVxyXG4gKiBAcGFyYW0gY2FsbGJhY2sgLSBDYWxsYmFjayBmdW5jdGlvbiB0byB0cmlnZ2VyIHdoZW4gaW50ZXJzZWN0aW9uIHN0YXR1cyBjaGFuZ2VzXHJcbiAqIEBwYXJhbSBvcHRpb25zIC0gSW50ZXJzZWN0aW9uIE9ic2VydmVyIG9wdGlvbnNcclxuICogQHBhcmFtIGZhbGxiYWNrSW5WaWV3IC0gRmFsbGJhY2sgaW5WaWV3IHZhbHVlLlxyXG4gKiBAcmV0dXJuIEZ1bmN0aW9uIC0gQ2xlYW51cCBmdW5jdGlvbiB0aGF0IHNob3VsZCBiZSB0cmlnZ2VyZWQgdG8gdW5yZWdpc3RlciB0aGUgb2JzZXJ2ZXJcclxuICovXG5cblxuZnVuY3Rpb24gb2JzZXJ2ZShlbGVtZW50LCBjYWxsYmFjaywgb3B0aW9ucywgZmFsbGJhY2tJblZpZXcpIHtcbiAgaWYgKG9wdGlvbnMgPT09IHZvaWQgMCkge1xuICAgIG9wdGlvbnMgPSB7fTtcbiAgfVxuXG4gIGlmIChmYWxsYmFja0luVmlldyA9PT0gdm9pZCAwKSB7XG4gICAgZmFsbGJhY2tJblZpZXcgPSB1bnN1cHBvcnRlZFZhbHVlO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB3aW5kb3cuSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgPT09ICd1bmRlZmluZWQnICYmIGZhbGxiYWNrSW5WaWV3ICE9PSB1bmRlZmluZWQpIHtcbiAgICB2YXIgYm91bmRzID0gZWxlbWVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBjYWxsYmFjayhmYWxsYmFja0luVmlldywge1xuICAgICAgaXNJbnRlcnNlY3Rpbmc6IGZhbGxiYWNrSW5WaWV3LFxuICAgICAgdGFyZ2V0OiBlbGVtZW50LFxuICAgICAgaW50ZXJzZWN0aW9uUmF0aW86IHR5cGVvZiBvcHRpb25zLnRocmVzaG9sZCA9PT0gJ251bWJlcicgPyBvcHRpb25zLnRocmVzaG9sZCA6IDAsXG4gICAgICB0aW1lOiAwLFxuICAgICAgYm91bmRpbmdDbGllbnRSZWN0OiBib3VuZHMsXG4gICAgICBpbnRlcnNlY3Rpb25SZWN0OiBib3VuZHMsXG4gICAgICByb290Qm91bmRzOiBib3VuZHNcbiAgICB9KTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkgey8vIE5vdGhpbmcgdG8gY2xlYW51cFxuICAgIH07XG4gIH0gLy8gQW4gb2JzZXJ2ZXIgd2l0aCB0aGUgc2FtZSBvcHRpb25zIGNhbiBiZSByZXVzZWQsIHNvIGxldHMgdXNlIHRoaXMgZmFjdFxuXG5cbiAgdmFyIF9jcmVhdGVPYnNlcnZlciA9IGNyZWF0ZU9ic2VydmVyKG9wdGlvbnMpLFxuICAgICAgaWQgPSBfY3JlYXRlT2JzZXJ2ZXIuaWQsXG4gICAgICBvYnNlcnZlciA9IF9jcmVhdGVPYnNlcnZlci5vYnNlcnZlcixcbiAgICAgIGVsZW1lbnRzID0gX2NyZWF0ZU9ic2VydmVyLmVsZW1lbnRzOyAvLyBSZWdpc3RlciB0aGUgY2FsbGJhY2sgbGlzdGVuZXIgZm9yIHRoaXMgZWxlbWVudFxuXG5cbiAgdmFyIGNhbGxiYWNrcyA9IGVsZW1lbnRzLmdldChlbGVtZW50KSB8fCBbXTtcblxuICBpZiAoIWVsZW1lbnRzLmhhcyhlbGVtZW50KSkge1xuICAgIGVsZW1lbnRzLnNldChlbGVtZW50LCBjYWxsYmFja3MpO1xuICB9XG5cbiAgY2FsbGJhY2tzLnB1c2goY2FsbGJhY2spO1xuICBvYnNlcnZlci5vYnNlcnZlKGVsZW1lbnQpO1xuICByZXR1cm4gZnVuY3Rpb24gdW5vYnNlcnZlKCkge1xuICAgIC8vIFJlbW92ZSB0aGUgY2FsbGJhY2sgZnJvbSB0aGUgY2FsbGJhY2sgbGlzdFxuICAgIGNhbGxiYWNrcy5zcGxpY2UoY2FsbGJhY2tzLmluZGV4T2YoY2FsbGJhY2spLCAxKTtcblxuICAgIGlmIChjYWxsYmFja3MubGVuZ3RoID09PSAwKSB7XG4gICAgICAvLyBObyBtb3JlIGNhbGxiYWNrIGV4aXN0cyBmb3IgZWxlbWVudCwgc28gZGVzdHJveSBpdFxuICAgICAgZWxlbWVudHNbXCJkZWxldGVcIl0oZWxlbWVudCk7XG4gICAgICBvYnNlcnZlci51bm9ic2VydmUoZWxlbWVudCk7XG4gICAgfVxuXG4gICAgaWYgKGVsZW1lbnRzLnNpemUgPT09IDApIHtcbiAgICAgIC8vIE5vIG1vcmUgZWxlbWVudHMgYXJlIGJlaW5nIG9ic2VydmVyIGJ5IHRoaXMgaW5zdGFuY2UsIHNvIGRlc3Ryb3kgaXRcbiAgICAgIG9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcbiAgICAgIG9ic2VydmVyTWFwW1wiZGVsZXRlXCJdKGlkKTtcbiAgICB9XG4gIH07XG59XG5cbnZhciBfZXhjbHVkZWQgPSBbXCJjaGlsZHJlblwiLCBcImFzXCIsIFwidHJpZ2dlck9uY2VcIiwgXCJ0aHJlc2hvbGRcIiwgXCJyb290XCIsIFwicm9vdE1hcmdpblwiLCBcIm9uQ2hhbmdlXCIsIFwic2tpcFwiLCBcInRyYWNrVmlzaWJpbGl0eVwiLCBcImRlbGF5XCIsIFwiaW5pdGlhbEluVmlld1wiLCBcImZhbGxiYWNrSW5WaWV3XCJdO1xuXG5mdW5jdGlvbiBpc1BsYWluQ2hpbGRyZW4ocHJvcHMpIHtcbiAgcmV0dXJuIHR5cGVvZiBwcm9wcy5jaGlsZHJlbiAhPT0gJ2Z1bmN0aW9uJztcbn1cbi8qKlxyXG4gIyMgUmVuZGVyIHByb3BzXHJcblxuIFRvIHVzZSB0aGUgYDxJblZpZXc+YCBjb21wb25lbnQsIHlvdSBwYXNzIGl0IGEgZnVuY3Rpb24uIEl0IHdpbGwgYmUgY2FsbGVkXHJcbiB3aGVuZXZlciB0aGUgc3RhdGUgY2hhbmdlcywgd2l0aCB0aGUgbmV3IHZhbHVlIG9mIGBpblZpZXdgLiBJbiBhZGRpdGlvbiB0byB0aGVcclxuIGBpblZpZXdgIHByb3AsIGNoaWxkcmVuIGFsc28gcmVjZWl2ZSBhIGByZWZgIHRoYXQgc2hvdWxkIGJlIHNldCBvbiB0aGVcclxuIGNvbnRhaW5pbmcgRE9NIGVsZW1lbnQuIFRoaXMgaXMgdGhlIGVsZW1lbnQgdGhhdCB0aGUgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgd2lsbFxyXG4gbW9uaXRvci5cclxuXG4gSWYgeW91IG5lZWQgaXQsIHlvdSBjYW4gYWxzbyBhY2Nlc3MgdGhlXHJcbiBbYEludGVyc2VjdGlvbk9ic2VydmVyRW50cnlgXShodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvSW50ZXJzZWN0aW9uT2JzZXJ2ZXJFbnRyeSlcclxuIG9uIGBlbnRyeWAsIGdpdmluZyB5b3UgYWNjZXNzIHRvIGFsbCB0aGUgZGV0YWlscyBhYm91dCB0aGUgY3VycmVudCBpbnRlcnNlY3Rpb25cclxuIHN0YXRlLlxyXG5cbiBgYGBqc3hcclxuIGltcG9ydCB7IEluVmlldyB9IGZyb20gJ3JlYWN0LWludGVyc2VjdGlvbi1vYnNlcnZlcic7XHJcblxuIGNvbnN0IENvbXBvbmVudCA9ICgpID0+IChcclxuIDxJblZpZXc+XHJcbiB7KHsgaW5WaWV3LCByZWYsIGVudHJ5IH0pID0+IChcclxuICAgICAgPGRpdiByZWY9e3JlZn0+XHJcbiAgICAgICAgPGgyPntgSGVhZGVyIGluc2lkZSB2aWV3cG9ydCAke2luVmlld30uYH08L2gyPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICl9XHJcbiA8L0luVmlldz5cclxuICk7XHJcblxuIGV4cG9ydCBkZWZhdWx0IENvbXBvbmVudDtcclxuIGBgYFxyXG5cbiAjIyBQbGFpbiBjaGlsZHJlblxyXG5cbiBZb3UgY2FuIHBhc3MgYW55IGVsZW1lbnQgdG8gdGhlIGA8SW5WaWV3IC8+YCwgYW5kIGl0IHdpbGwgaGFuZGxlIGNyZWF0aW5nIHRoZVxyXG4gd3JhcHBpbmcgRE9NIGVsZW1lbnQuIEFkZCBhIGhhbmRsZXIgdG8gdGhlIGBvbkNoYW5nZWAgbWV0aG9kLCBhbmQgY29udHJvbCB0aGVcclxuIHN0YXRlIGluIHlvdXIgb3duIGNvbXBvbmVudC4gQW55IGV4dHJhIHByb3BzIHlvdSBhZGQgdG8gYDxJblZpZXc+YCB3aWxsIGJlXHJcbiBwYXNzZWQgdG8gdGhlIEhUTUwgZWxlbWVudCwgYWxsb3dpbmcgeW91IHNldCB0aGUgYGNsYXNzTmFtZWAsIGBzdHlsZWAsIGV0Yy5cclxuXG4gYGBganN4XHJcbiBpbXBvcnQgeyBJblZpZXcgfSBmcm9tICdyZWFjdC1pbnRlcnNlY3Rpb24tb2JzZXJ2ZXInO1xyXG5cbiBjb25zdCBDb21wb25lbnQgPSAoKSA9PiAoXHJcbiA8SW5WaWV3IGFzPVwiZGl2XCIgb25DaGFuZ2U9eyhpblZpZXcsIGVudHJ5KSA9PiBjb25zb2xlLmxvZygnSW52aWV3OicsIGluVmlldyl9PlxyXG4gPGgyPlBsYWluIGNoaWxkcmVuIGFyZSBhbHdheXMgcmVuZGVyZWQuIFVzZSBvbkNoYW5nZSB0byBtb25pdG9yIHN0YXRlLjwvaDI+XHJcbiA8L0luVmlldz5cclxuICk7XHJcblxuIGV4cG9ydCBkZWZhdWx0IENvbXBvbmVudDtcclxuIGBgYFxyXG4gKi9cblxuXG52YXIgSW5WaWV3ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfUmVhY3QkQ29tcG9uZW50KSB7XG4gIF9pbmhlcml0c0xvb3NlKEluVmlldywgX1JlYWN0JENvbXBvbmVudCk7XG5cbiAgZnVuY3Rpb24gSW5WaWV3KHByb3BzKSB7XG4gICAgdmFyIF90aGlzO1xuXG4gICAgX3RoaXMgPSBfUmVhY3QkQ29tcG9uZW50LmNhbGwodGhpcywgcHJvcHMpIHx8IHRoaXM7XG4gICAgX3RoaXMubm9kZSA9IG51bGw7XG4gICAgX3RoaXMuX3Vub2JzZXJ2ZUNiID0gbnVsbDtcblxuICAgIF90aGlzLmhhbmRsZU5vZGUgPSBmdW5jdGlvbiAobm9kZSkge1xuICAgICAgaWYgKF90aGlzLm5vZGUpIHtcbiAgICAgICAgLy8gQ2xlYXIgdGhlIG9sZCBvYnNlcnZlciwgYmVmb3JlIHdlIHN0YXJ0IG9ic2VydmluZyBhIG5ldyBlbGVtZW50XG4gICAgICAgIF90aGlzLnVub2JzZXJ2ZSgpO1xuXG4gICAgICAgIGlmICghbm9kZSAmJiAhX3RoaXMucHJvcHMudHJpZ2dlck9uY2UgJiYgIV90aGlzLnByb3BzLnNraXApIHtcbiAgICAgICAgICAvLyBSZXNldCB0aGUgc3RhdGUgaWYgd2UgZ2V0IGEgbmV3IG5vZGUsIGFuZCB3ZSBhcmVuJ3QgaWdub3JpbmcgdXBkYXRlc1xuICAgICAgICAgIF90aGlzLnNldFN0YXRlKHtcbiAgICAgICAgICAgIGluVmlldzogISFfdGhpcy5wcm9wcy5pbml0aWFsSW5WaWV3LFxuICAgICAgICAgICAgZW50cnk6IHVuZGVmaW5lZFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIF90aGlzLm5vZGUgPSBub2RlID8gbm9kZSA6IG51bGw7XG5cbiAgICAgIF90aGlzLm9ic2VydmVOb2RlKCk7XG4gICAgfTtcblxuICAgIF90aGlzLmhhbmRsZUNoYW5nZSA9IGZ1bmN0aW9uIChpblZpZXcsIGVudHJ5KSB7XG4gICAgICBpZiAoaW5WaWV3ICYmIF90aGlzLnByb3BzLnRyaWdnZXJPbmNlKSB7XG4gICAgICAgIC8vIElmIGB0cmlnZ2VyT25jZWAgaXMgdHJ1ZSwgd2Ugc2hvdWxkIHN0b3Agb2JzZXJ2aW5nIHRoZSBlbGVtZW50LlxuICAgICAgICBfdGhpcy51bm9ic2VydmUoKTtcbiAgICAgIH1cblxuICAgICAgaWYgKCFpc1BsYWluQ2hpbGRyZW4oX3RoaXMucHJvcHMpKSB7XG4gICAgICAgIC8vIFN0b3JlIHRoZSBjdXJyZW50IFN0YXRlLCBzbyB3ZSBjYW4gcGFzcyBpdCB0byB0aGUgY2hpbGRyZW4gaW4gdGhlIG5leHQgcmVuZGVyIHVwZGF0ZVxuICAgICAgICAvLyBUaGVyZSdzIG5vIHJlYXNvbiB0byB1cGRhdGUgdGhlIHN0YXRlIGZvciBwbGFpbiBjaGlsZHJlbiwgc2luY2UgaXQncyBub3QgdXNlZCBpbiB0aGUgcmVuZGVyaW5nLlxuICAgICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgICAgaW5WaWV3OiBpblZpZXcsXG4gICAgICAgICAgZW50cnk6IGVudHJ5XG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICBpZiAoX3RoaXMucHJvcHMub25DaGFuZ2UpIHtcbiAgICAgICAgLy8gSWYgdGhlIHVzZXIgaXMgYWN0aXZlbHkgbGlzdGVuaW5nIGZvciBvbkNoYW5nZSwgYWx3YXlzIHRyaWdnZXIgaXRcbiAgICAgICAgX3RoaXMucHJvcHMub25DaGFuZ2UoaW5WaWV3LCBlbnRyeSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIF90aGlzLnN0YXRlID0ge1xuICAgICAgaW5WaWV3OiAhIXByb3BzLmluaXRpYWxJblZpZXcsXG4gICAgICBlbnRyeTogdW5kZWZpbmVkXG4gICAgfTtcbiAgICByZXR1cm4gX3RoaXM7XG4gIH1cblxuICB2YXIgX3Byb3RvID0gSW5WaWV3LnByb3RvdHlwZTtcblxuICBfcHJvdG8uY29tcG9uZW50RGlkVXBkYXRlID0gZnVuY3Rpb24gY29tcG9uZW50RGlkVXBkYXRlKHByZXZQcm9wcykge1xuICAgIC8vIElmIGEgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgb3B0aW9uIGNoYW5nZWQsIHJlaW5pdCB0aGUgb2JzZXJ2ZXJcbiAgICBpZiAocHJldlByb3BzLnJvb3RNYXJnaW4gIT09IHRoaXMucHJvcHMucm9vdE1hcmdpbiB8fCBwcmV2UHJvcHMucm9vdCAhPT0gdGhpcy5wcm9wcy5yb290IHx8IHByZXZQcm9wcy50aHJlc2hvbGQgIT09IHRoaXMucHJvcHMudGhyZXNob2xkIHx8IHByZXZQcm9wcy5za2lwICE9PSB0aGlzLnByb3BzLnNraXAgfHwgcHJldlByb3BzLnRyYWNrVmlzaWJpbGl0eSAhPT0gdGhpcy5wcm9wcy50cmFja1Zpc2liaWxpdHkgfHwgcHJldlByb3BzLmRlbGF5ICE9PSB0aGlzLnByb3BzLmRlbGF5KSB7XG4gICAgICB0aGlzLnVub2JzZXJ2ZSgpO1xuICAgICAgdGhpcy5vYnNlcnZlTm9kZSgpO1xuICAgIH1cbiAgfTtcblxuICBfcHJvdG8uY29tcG9uZW50V2lsbFVubW91bnQgPSBmdW5jdGlvbiBjb21wb25lbnRXaWxsVW5tb3VudCgpIHtcbiAgICB0aGlzLnVub2JzZXJ2ZSgpO1xuICAgIHRoaXMubm9kZSA9IG51bGw7XG4gIH07XG5cbiAgX3Byb3RvLm9ic2VydmVOb2RlID0gZnVuY3Rpb24gb2JzZXJ2ZU5vZGUoKSB7XG4gICAgaWYgKCF0aGlzLm5vZGUgfHwgdGhpcy5wcm9wcy5za2lwKSByZXR1cm47XG4gICAgdmFyIF90aGlzJHByb3BzID0gdGhpcy5wcm9wcyxcbiAgICAgICAgdGhyZXNob2xkID0gX3RoaXMkcHJvcHMudGhyZXNob2xkLFxuICAgICAgICByb290ID0gX3RoaXMkcHJvcHMucm9vdCxcbiAgICAgICAgcm9vdE1hcmdpbiA9IF90aGlzJHByb3BzLnJvb3RNYXJnaW4sXG4gICAgICAgIHRyYWNrVmlzaWJpbGl0eSA9IF90aGlzJHByb3BzLnRyYWNrVmlzaWJpbGl0eSxcbiAgICAgICAgZGVsYXkgPSBfdGhpcyRwcm9wcy5kZWxheSxcbiAgICAgICAgZmFsbGJhY2tJblZpZXcgPSBfdGhpcyRwcm9wcy5mYWxsYmFja0luVmlldztcbiAgICB0aGlzLl91bm9ic2VydmVDYiA9IG9ic2VydmUodGhpcy5ub2RlLCB0aGlzLmhhbmRsZUNoYW5nZSwge1xuICAgICAgdGhyZXNob2xkOiB0aHJlc2hvbGQsXG4gICAgICByb290OiByb290LFxuICAgICAgcm9vdE1hcmdpbjogcm9vdE1hcmdpbixcbiAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgIHRyYWNrVmlzaWJpbGl0eTogdHJhY2tWaXNpYmlsaXR5LFxuICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgZGVsYXk6IGRlbGF5XG4gICAgfSwgZmFsbGJhY2tJblZpZXcpO1xuICB9O1xuXG4gIF9wcm90by51bm9ic2VydmUgPSBmdW5jdGlvbiB1bm9ic2VydmUoKSB7XG4gICAgaWYgKHRoaXMuX3Vub2JzZXJ2ZUNiKSB7XG4gICAgICB0aGlzLl91bm9ic2VydmVDYigpO1xuXG4gICAgICB0aGlzLl91bm9ic2VydmVDYiA9IG51bGw7XG4gICAgfVxuICB9O1xuXG4gIF9wcm90by5yZW5kZXIgPSBmdW5jdGlvbiByZW5kZXIoKSB7XG4gICAgaWYgKCFpc1BsYWluQ2hpbGRyZW4odGhpcy5wcm9wcykpIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZSA9IHRoaXMuc3RhdGUsXG4gICAgICAgICAgaW5WaWV3ID0gX3RoaXMkc3RhdGUuaW5WaWV3LFxuICAgICAgICAgIGVudHJ5ID0gX3RoaXMkc3RhdGUuZW50cnk7XG4gICAgICByZXR1cm4gdGhpcy5wcm9wcy5jaGlsZHJlbih7XG4gICAgICAgIGluVmlldzogaW5WaWV3LFxuICAgICAgICBlbnRyeTogZW50cnksXG4gICAgICAgIHJlZjogdGhpcy5oYW5kbGVOb2RlXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICB2YXIgX3RoaXMkcHJvcHMyID0gdGhpcy5wcm9wcyxcbiAgICAgICAgY2hpbGRyZW4gPSBfdGhpcyRwcm9wczIuY2hpbGRyZW4sXG4gICAgICAgIGFzID0gX3RoaXMkcHJvcHMyLmFzLFxuICAgICAgICBwcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKF90aGlzJHByb3BzMiwgX2V4Y2x1ZGVkKTtcblxuICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChhcyB8fCAnZGl2JywgX2V4dGVuZHMoe1xuICAgICAgcmVmOiB0aGlzLmhhbmRsZU5vZGVcbiAgICB9LCBwcm9wcyksIGNoaWxkcmVuKTtcbiAgfTtcblxuICByZXR1cm4gSW5WaWV3O1xufShSZWFjdC5Db21wb25lbnQpO1xuSW5WaWV3LmRpc3BsYXlOYW1lID0gJ0luVmlldyc7XG5JblZpZXcuZGVmYXVsdFByb3BzID0ge1xuICB0aHJlc2hvbGQ6IDAsXG4gIHRyaWdnZXJPbmNlOiBmYWxzZSxcbiAgaW5pdGlhbEluVmlldzogZmFsc2Vcbn07XG5cbi8qKlxyXG4gKiBSZWFjdCBIb29rcyBtYWtlIGl0IGVhc3kgdG8gbW9uaXRvciB0aGUgYGluVmlld2Agc3RhdGUgb2YgeW91ciBjb21wb25lbnRzLiBDYWxsXHJcbiAqIHRoZSBgdXNlSW5WaWV3YCBob29rIHdpdGggdGhlIChvcHRpb25hbCkgW29wdGlvbnNdKCNvcHRpb25zKSB5b3UgbmVlZC4gSXQgd2lsbFxyXG4gKiByZXR1cm4gYW4gYXJyYXkgY29udGFpbmluZyBhIGByZWZgLCB0aGUgYGluVmlld2Agc3RhdHVzIGFuZCB0aGUgY3VycmVudFxyXG4gKiBbYGVudHJ5YF0oaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL0ludGVyc2VjdGlvbk9ic2VydmVyRW50cnkpLlxyXG4gKiBBc3NpZ24gdGhlIGByZWZgIHRvIHRoZSBET00gZWxlbWVudCB5b3Ugd2FudCB0byBtb25pdG9yLCBhbmQgdGhlIGhvb2sgd2lsbFxyXG4gKiByZXBvcnQgdGhlIHN0YXR1cy5cclxuICpcclxuICogQGV4YW1wbGVcclxuICogYGBganN4XHJcbiAqIGltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbiAqIGltcG9ydCB7IHVzZUluVmlldyB9IGZyb20gJ3JlYWN0LWludGVyc2VjdGlvbi1vYnNlcnZlcic7XHJcbiAqXHJcbiAqIGNvbnN0IENvbXBvbmVudCA9ICgpID0+IHtcclxuICogICBjb25zdCB7IHJlZiwgaW5WaWV3LCBlbnRyeSB9ID0gdXNlSW5WaWV3KHtcclxuICogICAgICAgdGhyZXNob2xkOiAwLFxyXG4gKiAgIH0pO1xyXG4gKlxyXG4gKiAgIHJldHVybiAoXHJcbiAqICAgICA8ZGl2IHJlZj17cmVmfT5cclxuICogICAgICAgPGgyPntgSGVhZGVyIGluc2lkZSB2aWV3cG9ydCAke2luVmlld30uYH08L2gyPlxyXG4gKiAgICAgPC9kaXY+XHJcbiAqICAgKTtcclxuICogfTtcclxuICogYGBgXHJcbiAqL1xuXG5mdW5jdGlvbiB1c2VJblZpZXcoX3RlbXApIHtcbiAgdmFyIF9yZWYgPSBfdGVtcCA9PT0gdm9pZCAwID8ge30gOiBfdGVtcCxcbiAgICAgIHRocmVzaG9sZCA9IF9yZWYudGhyZXNob2xkLFxuICAgICAgZGVsYXkgPSBfcmVmLmRlbGF5LFxuICAgICAgdHJhY2tWaXNpYmlsaXR5ID0gX3JlZi50cmFja1Zpc2liaWxpdHksXG4gICAgICByb290TWFyZ2luID0gX3JlZi5yb290TWFyZ2luLFxuICAgICAgcm9vdCA9IF9yZWYucm9vdCxcbiAgICAgIHRyaWdnZXJPbmNlID0gX3JlZi50cmlnZ2VyT25jZSxcbiAgICAgIHNraXAgPSBfcmVmLnNraXAsXG4gICAgICBpbml0aWFsSW5WaWV3ID0gX3JlZi5pbml0aWFsSW5WaWV3LFxuICAgICAgZmFsbGJhY2tJblZpZXcgPSBfcmVmLmZhbGxiYWNrSW5WaWV3O1xuXG4gIHZhciB1bm9ic2VydmUgPSBSZWFjdC51c2VSZWYoKTtcblxuICB2YXIgX1JlYWN0JHVzZVN0YXRlID0gUmVhY3QudXNlU3RhdGUoe1xuICAgIGluVmlldzogISFpbml0aWFsSW5WaWV3XG4gIH0pLFxuICAgICAgc3RhdGUgPSBfUmVhY3QkdXNlU3RhdGVbMF0sXG4gICAgICBzZXRTdGF0ZSA9IF9SZWFjdCR1c2VTdGF0ZVsxXTtcblxuICB2YXIgc2V0UmVmID0gUmVhY3QudXNlQ2FsbGJhY2soZnVuY3Rpb24gKG5vZGUpIHtcbiAgICBpZiAodW5vYnNlcnZlLmN1cnJlbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgdW5vYnNlcnZlLmN1cnJlbnQoKTtcbiAgICAgIHVub2JzZXJ2ZS5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIH0gLy8gU2tpcCBjcmVhdGluZyB0aGUgb2JzZXJ2ZXJcblxuXG4gICAgaWYgKHNraXApIHJldHVybjtcblxuICAgIGlmIChub2RlKSB7XG4gICAgICB1bm9ic2VydmUuY3VycmVudCA9IG9ic2VydmUobm9kZSwgZnVuY3Rpb24gKGluVmlldywgZW50cnkpIHtcbiAgICAgICAgc2V0U3RhdGUoe1xuICAgICAgICAgIGluVmlldzogaW5WaWV3LFxuICAgICAgICAgIGVudHJ5OiBlbnRyeVxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoZW50cnkuaXNJbnRlcnNlY3RpbmcgJiYgdHJpZ2dlck9uY2UgJiYgdW5vYnNlcnZlLmN1cnJlbnQpIHtcbiAgICAgICAgICAvLyBJZiBpdCBzaG91bGQgb25seSB0cmlnZ2VyIG9uY2UsIHVub2JzZXJ2ZSB0aGUgZWxlbWVudCBhZnRlciBpdCdzIGluVmlld1xuICAgICAgICAgIHVub2JzZXJ2ZS5jdXJyZW50KCk7XG4gICAgICAgICAgdW5vYnNlcnZlLmN1cnJlbnQgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgIH0sIHtcbiAgICAgICAgcm9vdDogcm9vdCxcbiAgICAgICAgcm9vdE1hcmdpbjogcm9vdE1hcmdpbixcbiAgICAgICAgdGhyZXNob2xkOiB0aHJlc2hvbGQsXG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgdHJhY2tWaXNpYmlsaXR5OiB0cmFja1Zpc2liaWxpdHksXG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgZGVsYXk6IGRlbGF5XG4gICAgICB9LCBmYWxsYmFja0luVmlldyk7XG4gICAgfVxuICB9LCAvLyBXZSBicmVhayB0aGUgcnVsZSBoZXJlLCBiZWNhdXNlIHdlIGFyZW4ndCBpbmNsdWRpbmcgdGhlIGFjdHVhbCBgdGhyZXNob2xkYCB2YXJpYWJsZVxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIFsvLyBJZiB0aGUgdGhyZXNob2xkIGlzIGFuIGFycmF5LCBjb252ZXJ0IGl0IHRvIGEgc3RyaW5nIHNvIGl0IHdvbid0IGNoYW5nZSBiZXR3ZWVuIHJlbmRlcnMuXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcbiAgQXJyYXkuaXNBcnJheSh0aHJlc2hvbGQpID8gdGhyZXNob2xkLnRvU3RyaW5nKCkgOiB0aHJlc2hvbGQsIHJvb3QsIHJvb3RNYXJnaW4sIHRyaWdnZXJPbmNlLCBza2lwLCB0cmFja1Zpc2liaWxpdHksIGZhbGxiYWNrSW5WaWV3LCBkZWxheV0pO1xuICAvKiBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgKi9cblxuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghdW5vYnNlcnZlLmN1cnJlbnQgJiYgc3RhdGUuZW50cnkgJiYgIXRyaWdnZXJPbmNlICYmICFza2lwKSB7XG4gICAgICAvLyBJZiB3ZSBkb24ndCBoYXZlIGEgcmVmLCB0aGVuIHJlc2V0IHRoZSBzdGF0ZSAodW5sZXNzIHRoZSBob29rIGlzIHNldCB0byBvbmx5IGB0cmlnZ2VyT25jZWAgb3IgYHNraXBgKVxuICAgICAgLy8gVGhpcyBlbnN1cmVzIHdlIGNvcnJlY3RseSByZWZsZWN0IHRoZSBjdXJyZW50IHN0YXRlIC0gSWYgeW91IGFyZW4ndCBvYnNlcnZpbmcgYW55dGhpbmcsIHRoZW4gbm90aGluZyBpcyBpblZpZXdcbiAgICAgIHNldFN0YXRlKHtcbiAgICAgICAgaW5WaWV3OiAhIWluaXRpYWxJblZpZXdcbiAgICAgIH0pO1xuICAgIH1cbiAgfSk7XG4gIHZhciByZXN1bHQgPSBbc2V0UmVmLCBzdGF0ZS5pblZpZXcsIHN0YXRlLmVudHJ5XTsgLy8gU3VwcG9ydCBvYmplY3QgZGVzdHJ1Y3R1cmluZywgYnkgYWRkaW5nIHRoZSBzcGVjaWZpYyB2YWx1ZXMuXG5cbiAgcmVzdWx0LnJlZiA9IHJlc3VsdFswXTtcbiAgcmVzdWx0LmluVmlldyA9IHJlc3VsdFsxXTtcbiAgcmVzdWx0LmVudHJ5ID0gcmVzdWx0WzJdO1xuICByZXR1cm4gcmVzdWx0O1xufVxuXG5leHBvcnQgeyBJblZpZXcsIEluVmlldyBhcyBkZWZhdWx0LCBkZWZhdWx0RmFsbGJhY2tJblZpZXcsIG9ic2VydmUsIHVzZUluVmlldyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVhY3QtaW50ZXJzZWN0aW9uLW9ic2VydmVyLm0uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-intersection-observer/react-intersection-observer.m.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Cpages%5Ccategories%5Cindex.js&page=%2Fcategories!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);